// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecentUserBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView userAvatarImageView;

  @NonNull
  public final TextView userNameTextView;

  private ItemRecentUserBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageView userAvatarImageView, @NonNull TextView userNameTextView) {
    this.rootView = rootView;
    this.userAvatarImageView = userAvatarImageView;
    this.userNameTextView = userNameTextView;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecentUserBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecentUserBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recent_user, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecentUserBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.userAvatarImageView;
      ImageView userAvatarImageView = ViewBindings.findChildViewById(rootView, id);
      if (userAvatarImageView == null) {
        break missingId;
      }

      id = R.id.userNameTextView;
      TextView userNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (userNameTextView == null) {
        break missingId;
      }

      return new ItemRecentUserBinding((MaterialCardView) rootView, userAvatarImageView,
          userNameTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
