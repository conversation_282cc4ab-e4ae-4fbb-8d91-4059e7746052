<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="@color/screen_background_light_blue"
    tools:context=".ui.KpiDetailActivity">

    <!-- Basic Info -->
    <TextView
        android:id="@+id/detailKpiNameTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceHeadline5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Smart List" />

    <!-- Targets Section Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/targetsCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/detailKpiNameTextView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Targets Title with Icon -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:id="@+id/targetsTitleTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="🎯 Targets"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:textStyle="bold"
                    android:textColor="@color/primary_dark" />

                <!-- Quick Stats -->
                <TextView
                    android:id="@+id/progressSummaryTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0/35,000"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="@color/text_secondary"
                    tools:text="1,250/35,000" />

            </LinearLayout>

            <!-- Annual Target -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:text="📅"
                    android:textSize="16sp"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/detailKpiTargetValueTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textColor="@color/text_primary"
                    tools:text="Annual: 35,000" />

                <!-- Progress Percentage for Annual -->
                <TextView
                    android:id="@+id/annualProgressPercentageTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="@color/kpi_concern"
                    tools:text="15%" />

            </LinearLayout>

            <!-- Monthly Target -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:text="📊"
                    android:textSize="16sp"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/detailKpiMonthlyTargetValueTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textColor="@color/text_primary"
                    tools:text="Monthly: 3,735" />

                <!-- Progress Percentage for Monthly -->
                <TextView
                    android:id="@+id/monthlyProgressPercentageTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="@color/kpi_concern"
                    tools:text="25%" />

            </LinearLayout>

            <!-- Daily Target -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:text="⏰"
                    android:textSize="16sp"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/detailKpiDailyTargetValueTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textColor="@color/text_primary"
                    tools:text="Daily: 145" />

                <!-- Progress Percentage for Daily -->
                <TextView
                    android:id="@+id/dailyProgressPercentageTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="@color/kpi_concern"
                    tools:text="80%" />

            </LinearLayout>

            <!-- Last Entry Date -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="8dp"
                android:background="@drawable/rounded_background_light"
                android:padding="8dp">

                <TextView
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:text="📝"
                    android:textSize="14sp"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/lastEntryDateTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textAppearance="?attr/textAppearanceBody2"
                    android:textColor="?android:attr/textColorSecondary"
                    tools:text="Last Entry: 2025-04-27" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Scrollable Content Area -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/targetsCard"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Progress Indicators Section -->
            <LinearLayout
                android:id="@+id/progressIndicatorsLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:baselineAligned="false"
                android:gravity="center_vertical"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <!-- Daily Progress -->
                <LinearLayout
                    android:id="@+id/dailyProgressLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center_horizontal"
                    android:visibility="visible"
                    tools:visibility="visible">

                    <FrameLayout
                        android:id="@+id/dailyProgressIndicatorContainer"
                        android:layout_width="80dp"
                        android:layout_height="80dp">

                        <com.google.android.material.progressindicator.CircularProgressIndicator
                            android:id="@+id/dailyCircularProgressIndicator"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            app:indicatorSize="80dp"
                            app:trackThickness="8dp"
                            app:trackCornerRadius="4dp"
                            app:indicatorColor="@color/progress_indicator_blue"
                            app:trackColor="@color/progress_indicator_track" /> <!-- Reverted to self-closing tag -->

                        <TextView
                            android:id="@+id/dailyProgressPercentageText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textStyle="bold"
                            tools:text="0%" />
                    </FrameLayout>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Daily"
                        android:textAppearance="?attr/textAppearanceCaption"/>
                </LinearLayout>

                <!-- Monthly Progress -->
                <LinearLayout
                    android:id="@+id/monthlyProgressLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center_horizontal"
                    android:visibility="visible"
                    tools:visibility="visible">

                    <FrameLayout
                        android:id="@+id/monthlyProgressIndicatorContainer"
                        android:layout_width="100dp"
                        android:layout_height="100dp"> <!-- Increased size -->

                        <com.google.android.material.progressindicator.CircularProgressIndicator
                            android:id="@+id/monthlyCircularProgressIndicator"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            app:indicatorSize="100dp"
                            app:trackThickness="10dp"
                            app:trackCornerRadius="5dp"
                            app:indicatorColor="@color/progress_indicator_blue"
                            app:trackColor="@color/progress_indicator_track" />

                        <TextView
                            android:id="@+id/monthlyProgressPercentageText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textStyle="bold"
                            tools:text="71%" />
                    </FrameLayout>
                     <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Monthly"
                        android:textAppearance="?attr/textAppearanceCaption"/>
                </LinearLayout>

                <!-- Annual Progress -->
                 <LinearLayout
                    android:id="@+id/annualProgressLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center_horizontal"
                    android:visibility="visible">

                    <FrameLayout
                        android:id="@+id/annualProgressIndicatorContainer"
                        android:layout_width="80dp"
                        android:layout_height="80dp">

                        <com.google.android.material.progressindicator.CircularProgressIndicator
                            android:id="@+id/annualCircularProgressIndicator"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:max="100"
                            app:indicatorSize="80dp"
                            app:trackThickness="8dp"
                            app:trackCornerRadius="4dp"
                            app:indicatorColor="@color/progress_indicator_blue"
                            app:trackColor="@color/progress_indicator_track" />

                        <TextView
                            android:id="@+id/annualProgressPercentageText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textStyle="bold"
                            tools:text="27%" />
                    </FrameLayout>
                     <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Annual"
                        android:textAppearance="?attr/textAppearanceCaption"/>
                </LinearLayout>

            </LinearLayout>

            <!-- Quick Stats Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/quickStatsCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@android:color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/progressIndicatorsLayout">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Quick Stats Title -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📈 Quick Stats"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:layout_marginBottom="12dp" />

                    <!-- Current Progress -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:id="@+id/detailKpiProgressLabelTextView"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Current Progress"
                            android:textAppearance="?attr/textAppearanceBody1"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/detailKpiCurrentValueTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="?attr/textAppearanceSubtitle1"
                            android:textStyle="bold"
                            android:textColor="@color/progress_indicator_blue"
                            tools:text="140,865" />

                    </LinearLayout>

                    <!-- This Week Progress -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="This Week"
                            android:textAppearance="?attr/textAppearanceBody2"
                            android:textColor="@color/text_secondary" />

                        <TextView
                            android:id="@+id/thisWeekProgressTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="?attr/textAppearanceBody2"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            tools:text="1,250" />

                    </LinearLayout>

                    <!-- Average per Day -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Average/Day"
                            android:textAppearance="?attr/textAppearanceBody2"
                            android:textColor="@color/text_secondary" />

                        <TextView
                            android:id="@+id/averagePerDayTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="?attr/textAppearanceBody2"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            tools:text="178" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Enhanced Add Progress Button -->
            <LinearLayout
                android:id="@+id/addProgressButtonLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:gravity="center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/quickStatsCard">

                <!-- Quick Add Buttons -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/quickAdd10Button"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="+10"
                    android:textSize="12sp"
                    app:cornerRadius="16dp"
                    app:strokeColor="@color/progress_indicator_blue"
                    android:textColor="@color/progress_indicator_blue" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/quickAdd50Button"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="+50"
                    android:textSize="12sp"
                    app:cornerRadius="16dp"
                    app:strokeColor="@color/progress_indicator_blue"
                    android:textColor="@color/progress_indicator_blue" />

                <!-- Main Add Progress Button -->
                <Button
                    android:id="@+id/addProgressButton"
                    style="@style/Widget.MaterialComponents.Button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="➕ Add Progress"
                    android:textAllCaps="false"
                    app:backgroundTint="@color/progress_indicator_blue"
                    app:cornerRadius="20dp"
                    app:icon="@android:drawable/ic_input_add"
                    app:iconTint="@android:color/white" />

            </LinearLayout>

            <!-- Line Chart Area (Progress Over Time) -->
            <TextView
                android:id="@+id/lineChartTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="📊 Progress Over Time"
                android:textAppearance="?attr/textAppearanceSubtitle1"
                android:textStyle="bold"
                android:textColor="@color/primary_dark"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/addProgressButtonLayout" />

            <!-- Month Filter Spinner Re-added -->
            <Spinner
                android:id="@+id/monthFilterSpinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:minHeight="48dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/lineChartTitle"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/kpiLineChart"
                android:layout_width="0dp"
                android:layout_height="250dp"
                android:layout_marginTop="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/monthFilterSpinner"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <!-- Bar Chart Area Removed -->

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
