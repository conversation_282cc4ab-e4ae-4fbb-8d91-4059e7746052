<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:padding="24dp"
    tools:context=".ui.LoginActivity">

    <!-- App Logo/Icon -->
    <ImageView
        android:id="@+id/appLogoImageView"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="48dp"
        android:src="@mipmap/ic_launcher"
        android:contentDescription="App Logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- App Title -->
    <TextView
        android:id="@+id/appTitleTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/app_name"
        android:textColor="@color/purple_700"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appLogoImageView" />

    <!-- Welcome Message -->
    <TextView
        android:id="@+id/welcomeTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Welcome! Please sign in to continue"
        android:textColor="@color/gray_600"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appTitleTextView" />

    <!-- Login Form Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/loginFormCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/welcomeTextView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Username/Email Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/usernameInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Username or Email"
                app:startIconDrawable="@drawable/ic_baseline_person_24"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/usernameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- User Selection Spinner (Alternative to password) -->
            <TextView
                android:id="@+id/orTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                android:text="or select from existing users"
                android:textColor="@color/gray_500"
                android:textSize="14sp" />

            <Spinner
                android:id="@+id/userSelectionSpinner"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/spinner_background" />

            <!-- Admin Mode Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <CheckBox
                    android:id="@+id/adminModeCheckBox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Admin Mode"
                    android:textColor="@color/purple_700"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/ic_baseline_admin_panel_settings_24"
                    android:contentDescription="Admin Icon"
                    app:tint="@color/purple_700" />

            </LinearLayout>

            <!-- Admin Role Selection (Initially Hidden) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/adminRoleInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Admin Role"
                android:visibility="gone"
                app:startIconDrawable="@drawable/ic_baseline_security_24"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/adminRoleAutoComplete"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:text="Admin" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Remember Me Checkbox -->
            <CheckBox
                android:id="@+id/rememberMeCheckBox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="Remember me"
                android:textColor="@color/gray_700" />

            <!-- Login Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/loginButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Sign In"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="12dp" />

            <!-- Create New User Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/createUserButton"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="12dp"
                android:text="Create New User"
                android:textSize="14sp"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                app:cornerRadius="12dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Quick Access Section -->
    <TextView
        android:id="@+id/quickAccessTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="Quick Access"
        android:textColor="@color/gray_600"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/loginFormCard" />

    <!-- Recent Users RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recentUsersRecyclerView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/quickAccessTextView"
        tools:listitem="@layout/item_recent_user" />

    <!-- Loading Progress -->
    <ProgressBar
        android:id="@+id/loadingProgressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
