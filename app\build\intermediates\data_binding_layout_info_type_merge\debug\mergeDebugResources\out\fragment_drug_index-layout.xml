<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_drug_index" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\fragment_drug_index.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_drug_index_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="247" endOffset="14"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="51"/></Target><Target id="@+id/drugAutoComplete" view="AutoCompleteTextView"><Expressions/><location startLine="31" startOffset="12" endLine="44" endOffset="43"/></Target><Target id="@+id/drugNameTextView" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="53" endOffset="43"/></Target><Target id="@+id/usesTextView" view="TextView"><Expressions/><location startLine="75" startOffset="20" endLine="80" endOffset="42"/></Target><Target id="@+id/interactionsTextView" view="TextView"><Expressions/><location startLine="104" startOffset="20" endLine="109" endOffset="42"/></Target><Target id="@+id/sideEffectsTextView" view="TextView"><Expressions/><location startLine="133" startOffset="20" endLine="138" endOffset="42"/></Target><Target id="@+id/usageInstructionsTextView" view="TextView"><Expressions/><location startLine="162" startOffset="20" endLine="167" endOffset="42"/></Target><Target id="@+id/priceCardView" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="172" startOffset="12" endLine="243" endOffset="47"/></Target><Target id="@+id/selectedDrugNameTextView" view="TextView"><Expressions/><location startLine="198" startOffset="20" endLine="207" endOffset="53"/></Target><Target id="@+id/selectedDrugDetailsTextView" view="TextView"><Expressions/><location startLine="209" startOffset="20" endLine="217" endOffset="53"/></Target><Target id="@+id/priceTextView" view="TextView"><Expressions/><location startLine="219" startOffset="20" endLine="230" endOffset="48"/></Target><Target id="@+id/manufacturerTextView" view="TextView"><Expressions/><location startLine="232" startOffset="20" endLine="240" endOffset="53"/></Target></Targets></Layout>