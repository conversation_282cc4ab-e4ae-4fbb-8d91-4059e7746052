// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityUserKpiListBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayoutUserKpi;

  @NonNull
  public final TextView emptyStateTextViewUserKpi;

  @NonNull
  public final FloatingActionButton fabAddKpiUserList;

  @NonNull
  public final Toolbar toolbarUserKpi;

  @NonNull
  public final RecyclerView userKpiRecyclerView;

  private ActivityUserKpiListBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppBarLayout appBarLayoutUserKpi, @NonNull TextView emptyStateTextViewUserKpi,
      @NonNull FloatingActionButton fabAddKpiUserList, @NonNull Toolbar toolbarUserKpi,
      @NonNull RecyclerView userKpiRecyclerView) {
    this.rootView = rootView;
    this.appBarLayoutUserKpi = appBarLayoutUserKpi;
    this.emptyStateTextViewUserKpi = emptyStateTextViewUserKpi;
    this.fabAddKpiUserList = fabAddKpiUserList;
    this.toolbarUserKpi = toolbarUserKpi;
    this.userKpiRecyclerView = userKpiRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityUserKpiListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityUserKpiListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_user_kpi_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityUserKpiListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayoutUserKpi;
      AppBarLayout appBarLayoutUserKpi = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayoutUserKpi == null) {
        break missingId;
      }

      id = R.id.emptyStateTextViewUserKpi;
      TextView emptyStateTextViewUserKpi = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateTextViewUserKpi == null) {
        break missingId;
      }

      id = R.id.fabAddKpiUserList;
      FloatingActionButton fabAddKpiUserList = ViewBindings.findChildViewById(rootView, id);
      if (fabAddKpiUserList == null) {
        break missingId;
      }

      id = R.id.toolbarUserKpi;
      Toolbar toolbarUserKpi = ViewBindings.findChildViewById(rootView, id);
      if (toolbarUserKpi == null) {
        break missingId;
      }

      id = R.id.userKpiRecyclerView;
      RecyclerView userKpiRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (userKpiRecyclerView == null) {
        break missingId;
      }

      return new ActivityUserKpiListBinding((ConstraintLayout) rootView, appBarLayoutUserKpi,
          emptyStateTextViewUserKpi, fabAddKpiUserList, toolbarUserKpi, userKpiRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
