package com.example.kpitrackerapp.persistence;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.kpitrackerapp.models.KpiProgressEntry;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class KpiProgressEntryDao_Impl implements KpiProgressEntryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<KpiProgressEntry> __insertionAdapterOfKpiProgressEntry;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<KpiProgressEntry> __deletionAdapterOfKpiProgressEntry;

  private final EntityDeletionOrUpdateAdapter<KpiProgressEntry> __updateAdapterOfKpiProgressEntry;

  private final SharedSQLiteStatement __preparedStmtOfDeleteEntriesForKpi;

  private final SharedSQLiteStatement __preparedStmtOfDeleteEntriesForMonth;

  public KpiProgressEntryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfKpiProgressEntry = new EntityInsertionAdapter<KpiProgressEntry>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `kpi_progress_entries` (`id`,`kpi_id`,`user_id`,`entry_date`,`value`,`user_identifier`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final KpiProgressEntry entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getKpiId());
        statement.bindString(3, entity.getUserId());
        final Long _tmp = __converters.dateToTimestamp(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        statement.bindDouble(5, entity.getValue());
        if (entity.getUserIdentifier() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getUserIdentifier());
        }
      }
    };
    this.__deletionAdapterOfKpiProgressEntry = new EntityDeletionOrUpdateAdapter<KpiProgressEntry>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `kpi_progress_entries` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final KpiProgressEntry entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfKpiProgressEntry = new EntityDeletionOrUpdateAdapter<KpiProgressEntry>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `kpi_progress_entries` SET `id` = ?,`kpi_id` = ?,`user_id` = ?,`entry_date` = ?,`value` = ?,`user_identifier` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final KpiProgressEntry entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getKpiId());
        statement.bindString(3, entity.getUserId());
        final Long _tmp = __converters.dateToTimestamp(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        statement.bindDouble(5, entity.getValue());
        if (entity.getUserIdentifier() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getUserIdentifier());
        }
        statement.bindString(7, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteEntriesForKpi = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM kpi_progress_entries WHERE kpi_id = ? AND user_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteEntriesForMonth = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        DELETE FROM kpi_progress_entries\n"
                + "        WHERE kpi_id = ? AND user_id = ?\n"
                + "        AND strftime('%Y', entry_date / 1000, 'unixepoch') = ?\n"
                + "        AND strftime('%m', entry_date / 1000, 'unixepoch') = ?\n"
                + "    ";
        return _query;
      }
    };
  }

  @Override
  public Object insertEntry(final KpiProgressEntry entry,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfKpiProgressEntry.insert(entry);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEntry(final KpiProgressEntry entry,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfKpiProgressEntry.handle(entry);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateEntry(final KpiProgressEntry entry,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfKpiProgressEntry.handle(entry);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEntriesForKpi(final String kpiId, final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteEntriesForKpi.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, kpiId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteEntriesForKpi.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEntriesForMonth(final String kpiId, final String userId, final String year,
      final String monthFormatted, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteEntriesForMonth.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, kpiId);
        _argIndex = 2;
        _stmt.bindString(_argIndex, userId);
        _argIndex = 3;
        _stmt.bindString(_argIndex, year);
        _argIndex = 4;
        _stmt.bindString(_argIndex, monthFormatted);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteEntriesForMonth.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<KpiProgressEntry> getEntryForDate(final String kpiId, final String userId,
      final long dateInMillis) {
    final String _sql = "\n"
            + "        SELECT * FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ? AND user_id = ?\n"
            + "        AND date(entry_date / 1000, 'unixepoch') = date(? / 1000, 'unixepoch')\n"
            + "        LIMIT 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindLong(_argIndex, dateInMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<KpiProgressEntry>() {
      @Override
      @Nullable
      public KpiProgressEntry call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final KpiProgressEntry _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _result = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<KpiProgressEntry> getEntryById(final String entryId) {
    final String _sql = "SELECT * FROM kpi_progress_entries WHERE id = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, entryId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<KpiProgressEntry>() {
      @Override
      @Nullable
      public KpiProgressEntry call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final KpiProgressEntry _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _result = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KpiProgressEntry>> getEntriesForKpi(final String kpiId, final String userId) {
    final String _sql = "SELECT * FROM kpi_progress_entries WHERE kpi_id = ? AND user_id = ? ORDER BY entry_date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<List<KpiProgressEntry>>() {
      @Override
      @NonNull
      public List<KpiProgressEntry> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final List<KpiProgressEntry> _result = new ArrayList<KpiProgressEntry>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KpiProgressEntry _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _item = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KpiProgressEntry>> getAllEntries() {
    final String _sql = "SELECT * FROM kpi_progress_entries ORDER BY entry_date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<List<KpiProgressEntry>>() {
      @Override
      @NonNull
      public List<KpiProgressEntry> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final List<KpiProgressEntry> _result = new ArrayList<KpiProgressEntry>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KpiProgressEntry _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _item = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KpiProgressEntry>> getEntriesForKpiInRange(final String kpiId,
      final String userId, final long startDateMillis, final long endDateMillis) {
    final String _sql = "SELECT * FROM kpi_progress_entries WHERE kpi_id = ? AND user_id = ? AND entry_date BETWEEN ? AND ? ORDER BY entry_date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindLong(_argIndex, startDateMillis);
    _argIndex = 4;
    _statement.bindLong(_argIndex, endDateMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<List<KpiProgressEntry>>() {
      @Override
      @NonNull
      public List<KpiProgressEntry> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final List<KpiProgressEntry> _result = new ArrayList<KpiProgressEntry>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KpiProgressEntry _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _item = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getTotalValueForKpi(final String kpiId, final String userId) {
    final String _sql = "SELECT SUM(value) FROM kpi_progress_entries WHERE kpi_id = ? AND user_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForMonth(final String kpiId, final String userId, final String year,
      final String monthFormatted) {
    final String _sql = "\n"
            + "        SELECT SUM(value) FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ? AND user_id = ?\n"
            + "        AND strftime('%Y', entry_date / 1000, 'unixepoch') = ?\n"
            + "        AND strftime('%m', entry_date / 1000, 'unixepoch') = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, year);
    _argIndex = 4;
    _statement.bindString(_argIndex, monthFormatted);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForQuarter(final String kpiId, final String userId, final String year,
      final int startMonth, final int endMonth) {
    final String _sql = "\n"
            + "        SELECT SUM(value) FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ? AND user_id = ?\n"
            + "        AND strftime('%Y', entry_date / 1000, 'unixepoch') = ?\n"
            + "        AND CAST(strftime('%m', entry_date / 1000, 'unixepoch') AS INTEGER) BETWEEN ? AND ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 5);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, year);
    _argIndex = 4;
    _statement.bindLong(_argIndex, startMonth);
    _argIndex = 5;
    _statement.bindLong(_argIndex, endMonth);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForYear(final String kpiId, final String userId, final String year) {
    final String _sql = "\n"
            + "        SELECT SUM(value) FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ? AND user_id = ?\n"
            + "        AND strftime('%Y', entry_date / 1000, 'unixepoch') = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindString(_argIndex, year);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForDate(final String kpiId, final String userId,
      final long dateInMillis) {
    final String _sql = "\n"
            + "        SELECT SUM(value) FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ? AND user_id = ?\n"
            + "        AND date(entry_date / 1000, 'unixepoch') = date(? / 1000, 'unixepoch')\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindLong(_argIndex, dateInMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForDateRange(final String kpiId, final String userId,
      final long startDateMillis, final long endDateMillis) {
    final String _sql = "\n"
            + "        SELECT SUM(value) FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ? AND user_id = ?\n"
            + "        AND entry_date BETWEEN ? AND ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, userId);
    _argIndex = 3;
    _statement.bindLong(_argIndex, startDateMillis);
    _argIndex = 4;
    _statement.bindLong(_argIndex, endDateMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForDateAggregated(final String kpiId, final long dateInMillis) {
    final String _sql = "\n"
            + "        SELECT SUM(value) FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ?\n"
            + "        AND date(entry_date / 1000, 'unixepoch') = date(? / 1000, 'unixepoch')\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, dateInMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForDateAggregatedByName(final String kpiName, final long dateInMillis) {
    final String _sql = "\n"
            + "        SELECT SUM(p.value) FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        WHERE k.name = ?\n"
            + "        AND date(p.entry_date / 1000, 'unixepoch') = date(? / 1000, 'unixepoch')\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiName);
    _argIndex = 2;
    _statement.bindLong(_argIndex, dateInMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries",
        "kpis"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForDateRangeAggregated(final String kpiId, final long startDateMillis,
      final long endDateMillis) {
    final String _sql = "\n"
            + "        SELECT SUM(value) FROM kpi_progress_entries\n"
            + "        WHERE kpi_id = ?\n"
            + "        AND entry_date BETWEEN ? AND ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, startDateMillis);
    _argIndex = 3;
    _statement.bindLong(_argIndex, endDateMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getSumForDateRangeAggregatedByName(final String kpiName,
      final long startDateMillis, final long endDateMillis) {
    final String _sql = "\n"
            + "        SELECT SUM(p.value) FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        WHERE k.name = ?\n"
            + "        AND p.entry_date BETWEEN ? AND ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiName);
    _argIndex = 2;
    _statement.bindLong(_argIndex, startDateMillis);
    _argIndex = 3;
    _statement.bindLong(_argIndex, endDateMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries",
        "kpis"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KpiProgressEntry>> getEntriesForKpiAggregated(final String kpiId) {
    final String _sql = "SELECT * FROM kpi_progress_entries WHERE kpi_id = ? ORDER BY entry_date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<List<KpiProgressEntry>>() {
      @Override
      @NonNull
      public List<KpiProgressEntry> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final List<KpiProgressEntry> _result = new ArrayList<KpiProgressEntry>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KpiProgressEntry _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _item = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KpiProgressEntry>> getEntriesForKpiNameAggregated(final String kpiName) {
    final String _sql = "\n"
            + "        SELECT p.* FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        WHERE k.name = ?\n"
            + "        ORDER BY p.entry_date ASC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiName);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries",
        "kpis"}, new Callable<List<KpiProgressEntry>>() {
      @Override
      @NonNull
      public List<KpiProgressEntry> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final List<KpiProgressEntry> _result = new ArrayList<KpiProgressEntry>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KpiProgressEntry _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _item = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Long> getLastEntryDate() {
    final String _sql = "SELECT MAX(entry_date) FROM kpi_progress_entries";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries"}, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getAggregatedTotalValueByName(final String kpiName) {
    final String _sql = "\n"
            + "        SELECT SUM(p.value) FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        INNER JOIN user_kpi_assignments uka ON k.id = uka.kpi_id\n"
            + "        WHERE k.name = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiName);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries", "kpis",
        "user_kpi_assignments"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getAggregatedSumForMonthByName(final String kpiName, final String year,
      final String monthFormatted) {
    final String _sql = "\n"
            + "        SELECT SUM(p.value) FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        INNER JOIN user_kpi_assignments uka ON k.id = uka.kpi_id\n"
            + "        WHERE k.name = ?\n"
            + "        AND strftime('%Y', p.entry_date / 1000, 'unixepoch') = ?\n"
            + "        AND strftime('%m', p.entry_date / 1000, 'unixepoch') = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiName);
    _argIndex = 2;
    _statement.bindString(_argIndex, year);
    _argIndex = 3;
    _statement.bindString(_argIndex, monthFormatted);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries", "kpis",
        "user_kpi_assignments"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KpiProgressEntry>> getAggregatedEntriesByName(final String kpiName) {
    final String _sql = "\n"
            + "        SELECT p.* FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        INNER JOIN user_kpi_assignments uka ON k.id = uka.kpi_id\n"
            + "        WHERE k.name = ?\n"
            + "        ORDER BY p.entry_date ASC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiName);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries", "kpis",
        "user_kpi_assignments"}, new Callable<List<KpiProgressEntry>>() {
      @Override
      @NonNull
      public List<KpiProgressEntry> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final List<KpiProgressEntry> _result = new ArrayList<KpiProgressEntry>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KpiProgressEntry _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _item = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getAggregatedTotalValueForMaster(final String masterKpiId) {
    final String _sql = "\n"
            + "        SELECT SUM(p.value) FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        WHERE k.master_kpi_id = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, masterKpiId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries",
        "kpis"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Double> getAggregatedSumForMonth(final String masterKpiId, final String year,
      final String monthFormatted) {
    final String _sql = "\n"
            + "        SELECT SUM(p.value) FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        WHERE k.master_kpi_id = ?\n"
            + "        AND strftime('%Y', p.entry_date / 1000, 'unixepoch') = ?\n"
            + "        AND strftime('%m', p.entry_date / 1000, 'unixepoch') = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, masterKpiId);
    _argIndex = 2;
    _statement.bindString(_argIndex, year);
    _argIndex = 3;
    _statement.bindString(_argIndex, monthFormatted);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries",
        "kpis"}, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KpiProgressEntry>> getAggregatedEntriesForMaster(final String masterKpiId) {
    final String _sql = "\n"
            + "        SELECT p.* FROM kpi_progress_entries p\n"
            + "        INNER JOIN kpis k ON p.kpi_id = k.id\n"
            + "        WHERE k.master_kpi_id = ?\n"
            + "        ORDER BY p.entry_date ASC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, masterKpiId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpi_progress_entries",
        "kpis"}, new Callable<List<KpiProgressEntry>>() {
      @Override
      @NonNull
      public List<KpiProgressEntry> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "kpi_id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "entry_date");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfUserIdentifier = CursorUtil.getColumnIndexOrThrow(_cursor, "user_identifier");
          final List<KpiProgressEntry> _result = new ArrayList<KpiProgressEntry>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KpiProgressEntry _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpKpiId;
            _tmpKpiId = _cursor.getString(_cursorIndexOfKpiId);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            final Date _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final double _tmpValue;
            _tmpValue = _cursor.getDouble(_cursorIndexOfValue);
            final String _tmpUserIdentifier;
            if (_cursor.isNull(_cursorIndexOfUserIdentifier)) {
              _tmpUserIdentifier = null;
            } else {
              _tmpUserIdentifier = _cursor.getString(_cursorIndexOfUserIdentifier);
            }
            _item = new KpiProgressEntry(_tmpId,_tmpKpiId,_tmpUserId,_tmpDate,_tmpValue,_tmpUserIdentifier);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
