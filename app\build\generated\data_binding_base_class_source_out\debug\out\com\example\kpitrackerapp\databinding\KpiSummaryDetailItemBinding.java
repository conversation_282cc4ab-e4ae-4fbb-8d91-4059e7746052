// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class KpiSummaryDetailItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Guideline guidelineSummaryDetail70;

  @NonNull
  public final TextView kpiDetailAnnualPercentTextView;

  @NonNull
  public final TextView kpiDetailMonthlyPercentTextView;

  @NonNull
  public final TextView kpiDetailNameTextView;

  private KpiSummaryDetailItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull Guideline guidelineSummaryDetail70, @NonNull TextView kpiDetailAnnualPercentTextView,
      @NonNull TextView kpiDetailMonthlyPercentTextView, @NonNull TextView kpiDetailNameTextView) {
    this.rootView = rootView;
    this.guidelineSummaryDetail70 = guidelineSummaryDetail70;
    this.kpiDetailAnnualPercentTextView = kpiDetailAnnualPercentTextView;
    this.kpiDetailMonthlyPercentTextView = kpiDetailMonthlyPercentTextView;
    this.kpiDetailNameTextView = kpiDetailNameTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static KpiSummaryDetailItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static KpiSummaryDetailItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.kpi_summary_detail_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static KpiSummaryDetailItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.guideline_summary_detail_70;
      Guideline guidelineSummaryDetail70 = ViewBindings.findChildViewById(rootView, id);
      if (guidelineSummaryDetail70 == null) {
        break missingId;
      }

      id = R.id.kpiDetailAnnualPercentTextView;
      TextView kpiDetailAnnualPercentTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiDetailAnnualPercentTextView == null) {
        break missingId;
      }

      id = R.id.kpiDetailMonthlyPercentTextView;
      TextView kpiDetailMonthlyPercentTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiDetailMonthlyPercentTextView == null) {
        break missingId;
      }

      id = R.id.kpiDetailNameTextView;
      TextView kpiDetailNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiDetailNameTextView == null) {
        break missingId;
      }

      return new KpiSummaryDetailItemBinding((ConstraintLayout) rootView, guidelineSummaryDetail70,
          kpiDetailAnnualPercentTextView, kpiDetailMonthlyPercentTextView, kpiDetailNameTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
