// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemUserListBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton chatButton;

  @NonNull
  public final View onlineIndicator;

  @NonNull
  public final TextView roleIcon;

  @NonNull
  public final MaterialCardView userCard;

  @NonNull
  public final TextView userEmail;

  @NonNull
  public final ImageView userImage;

  @NonNull
  public final TextView userName;

  @NonNull
  public final TextView userRole;

  private ItemUserListBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton chatButton, @NonNull View onlineIndicator, @NonNull TextView roleIcon,
      @NonNull MaterialCardView userCard, @NonNull TextView userEmail, @NonNull ImageView userImage,
      @NonNull TextView userName, @NonNull TextView userRole) {
    this.rootView = rootView;
    this.chatButton = chatButton;
    this.onlineIndicator = onlineIndicator;
    this.roleIcon = roleIcon;
    this.userCard = userCard;
    this.userEmail = userEmail;
    this.userImage = userImage;
    this.userName = userName;
    this.userRole = userRole;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemUserListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemUserListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_user_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemUserListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chatButton;
      MaterialButton chatButton = ViewBindings.findChildViewById(rootView, id);
      if (chatButton == null) {
        break missingId;
      }

      id = R.id.onlineIndicator;
      View onlineIndicator = ViewBindings.findChildViewById(rootView, id);
      if (onlineIndicator == null) {
        break missingId;
      }

      id = R.id.roleIcon;
      TextView roleIcon = ViewBindings.findChildViewById(rootView, id);
      if (roleIcon == null) {
        break missingId;
      }

      MaterialCardView userCard = (MaterialCardView) rootView;

      id = R.id.userEmail;
      TextView userEmail = ViewBindings.findChildViewById(rootView, id);
      if (userEmail == null) {
        break missingId;
      }

      id = R.id.userImage;
      ImageView userImage = ViewBindings.findChildViewById(rootView, id);
      if (userImage == null) {
        break missingId;
      }

      id = R.id.userName;
      TextView userName = ViewBindings.findChildViewById(rootView, id);
      if (userName == null) {
        break missingId;
      }

      id = R.id.userRole;
      TextView userRole = ViewBindings.findChildViewById(rootView, id);
      if (userRole == null) {
        break missingId;
      }

      return new ItemUserListBinding((MaterialCardView) rootView, chatButton, onlineIndicator,
          roleIcon, userCard, userEmail, userImage, userName, userRole);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
