<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_notification" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_notification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_notification_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="73" endOffset="51"/></Target><Target id="@+id/typeIconTextView" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="37"/></Target><Target id="@+id/titleTextView" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="37" endOffset="42"/></Target><Target id="@+id/messageTextView" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="48" endOffset="41"/></Target><Target id="@+id/timeTextView" view="TextView"><Expressions/><location startLine="50" startOffset="12" endLine="57" endOffset="41"/></Target><Target id="@+id/unreadIndicator" view="View"><Expressions/><location startLine="61" startOffset="8" endLine="69" endOffset="42"/></Target></Targets></Layout>