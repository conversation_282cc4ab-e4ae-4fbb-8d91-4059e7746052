<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_notifications" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_notifications.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_notifications_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="67" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="66"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="33" startOffset="12" endLine="42" endOffset="43"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="60"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="56" startOffset="4" endLine="65" endOffset="41"/></Target></Targets></Layout>