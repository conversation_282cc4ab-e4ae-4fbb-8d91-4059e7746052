// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.bottomappbar.BottomAppBar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.materialswitch.MaterialSwitch;
import com.google.android.material.slider.Slider;
import com.google.android.material.textfield.MaterialAutoCompleteTextView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAddEditKpiBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton addTaskButton;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final BottomAppBar bottomAppBar;

  @NonNull
  public final MaterialButton btnAddAnother;

  @NonNull
  public final ImageButton btnCamera;

  @NonNull
  public final ImageButton btnLocation;

  @NonNull
  public final MaterialButton btnSaveDraft;

  @NonNull
  public final ImageButton btnTemplate;

  @NonNull
  public final ImageButton btnTimer;

  @NonNull
  public final ImageButton btnVoiceNote;

  @NonNull
  public final MaterialCardView cardAiSuggestions;

  @NonNull
  public final Chip chipContextHome;

  @NonNull
  public final Chip chipContextOffice;

  @NonNull
  public final Chip chipContextOnline;

  @NonNull
  public final Chip chipContextTravel;

  @NonNull
  public final Chip chipEnergyHigh;

  @NonNull
  public final Chip chipEnergyLow;

  @NonNull
  public final Chip chipEnergyMedium;

  @NonNull
  public final ChipGroup chipGroupContext;

  @NonNull
  public final ChipGroup chipGroupEnergyLevel;

  @NonNull
  public final ChipGroup chipGroupTaskIcon;

  @NonNull
  public final Chip chipIconHealth;

  @NonNull
  public final Chip chipIconIdea;

  @NonNull
  public final Chip chipIconSport;

  @NonNull
  public final Chip chipIconStudy;

  @NonNull
  public final Chip chipIconWork;

  @NonNull
  public final View colorBlue;

  @NonNull
  public final View colorGreen;

  @NonNull
  public final View colorOrange;

  @NonNull
  public final View colorPurple;

  @NonNull
  public final View colorRed;

  @NonNull
  public final LinearLayout layoutAdvancedDetails;

  @NonNull
  public final LinearLayout layoutSettings;

  @NonNull
  public final Slider sliderProgress;

  @NonNull
  public final MaterialSwitch switchBreakDown;

  @NonNull
  public final MaterialSwitch switchNotifications;

  @NonNull
  public final MaterialSwitch switchRecurring;

  @NonNull
  public final LinearLayout taskCategorySelector;

  @NonNull
  public final TextView taskCategoryText;

  @NonNull
  public final TextInputEditText taskDescriptionEditText;

  @NonNull
  public final TextInputLayout taskDescriptionInputLayout;

  @NonNull
  public final TextInputEditText taskEstimatedTimeEditText;

  @NonNull
  public final TextInputLayout taskEstimatedTimeInputLayout;

  @NonNull
  public final TextInputEditText taskExpirationDateEditText;

  @NonNull
  public final TextInputLayout taskExpirationDateInputLayout;

  @NonNull
  public final TextInputEditText taskExpirationTimeEditText;

  @NonNull
  public final TextInputLayout taskExpirationTimeInputLayout;

  @NonNull
  public final TextInputEditText taskLocationEditText;

  @NonNull
  public final TextInputLayout taskLocationInputLayout;

  @NonNull
  public final TextInputEditText taskNameEditText;

  @NonNull
  public final TextInputLayout taskNameInputLayout;

  @NonNull
  public final LinearLayout taskPrioritySelector;

  @NonNull
  public final TextView taskPriorityText;

  @NonNull
  public final MaterialAutoCompleteTextView taskReminderAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskReminderInputLayout;

  @NonNull
  public final MaterialCardView templateCall;

  @NonNull
  public final MaterialCardView templateExercise;

  @NonNull
  public final MaterialCardView templateMeeting;

  @NonNull
  public final MaterialCardView templateStudy;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView tvAdvancedDetailsHeader;

  @NonNull
  public final TextView tvProgressValue;

  @NonNull
  public final TextView tvSettingsHeader;

  private ActivityAddEditKpiBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton addTaskButton, @NonNull AppBarLayout appBarLayout,
      @NonNull BottomAppBar bottomAppBar, @NonNull MaterialButton btnAddAnother,
      @NonNull ImageButton btnCamera, @NonNull ImageButton btnLocation,
      @NonNull MaterialButton btnSaveDraft, @NonNull ImageButton btnTemplate,
      @NonNull ImageButton btnTimer, @NonNull ImageButton btnVoiceNote,
      @NonNull MaterialCardView cardAiSuggestions, @NonNull Chip chipContextHome,
      @NonNull Chip chipContextOffice, @NonNull Chip chipContextOnline,
      @NonNull Chip chipContextTravel, @NonNull Chip chipEnergyHigh, @NonNull Chip chipEnergyLow,
      @NonNull Chip chipEnergyMedium, @NonNull ChipGroup chipGroupContext,
      @NonNull ChipGroup chipGroupEnergyLevel, @NonNull ChipGroup chipGroupTaskIcon,
      @NonNull Chip chipIconHealth, @NonNull Chip chipIconIdea, @NonNull Chip chipIconSport,
      @NonNull Chip chipIconStudy, @NonNull Chip chipIconWork, @NonNull View colorBlue,
      @NonNull View colorGreen, @NonNull View colorOrange, @NonNull View colorPurple,
      @NonNull View colorRed, @NonNull LinearLayout layoutAdvancedDetails,
      @NonNull LinearLayout layoutSettings, @NonNull Slider sliderProgress,
      @NonNull MaterialSwitch switchBreakDown, @NonNull MaterialSwitch switchNotifications,
      @NonNull MaterialSwitch switchRecurring, @NonNull LinearLayout taskCategorySelector,
      @NonNull TextView taskCategoryText, @NonNull TextInputEditText taskDescriptionEditText,
      @NonNull TextInputLayout taskDescriptionInputLayout,
      @NonNull TextInputEditText taskEstimatedTimeEditText,
      @NonNull TextInputLayout taskEstimatedTimeInputLayout,
      @NonNull TextInputEditText taskExpirationDateEditText,
      @NonNull TextInputLayout taskExpirationDateInputLayout,
      @NonNull TextInputEditText taskExpirationTimeEditText,
      @NonNull TextInputLayout taskExpirationTimeInputLayout,
      @NonNull TextInputEditText taskLocationEditText,
      @NonNull TextInputLayout taskLocationInputLayout, @NonNull TextInputEditText taskNameEditText,
      @NonNull TextInputLayout taskNameInputLayout, @NonNull LinearLayout taskPrioritySelector,
      @NonNull TextView taskPriorityText,
      @NonNull MaterialAutoCompleteTextView taskReminderAutoCompleteTextView,
      @NonNull TextInputLayout taskReminderInputLayout, @NonNull MaterialCardView templateCall,
      @NonNull MaterialCardView templateExercise, @NonNull MaterialCardView templateMeeting,
      @NonNull MaterialCardView templateStudy, @NonNull MaterialToolbar toolbar,
      @NonNull TextView tvAdvancedDetailsHeader, @NonNull TextView tvProgressValue,
      @NonNull TextView tvSettingsHeader) {
    this.rootView = rootView;
    this.addTaskButton = addTaskButton;
    this.appBarLayout = appBarLayout;
    this.bottomAppBar = bottomAppBar;
    this.btnAddAnother = btnAddAnother;
    this.btnCamera = btnCamera;
    this.btnLocation = btnLocation;
    this.btnSaveDraft = btnSaveDraft;
    this.btnTemplate = btnTemplate;
    this.btnTimer = btnTimer;
    this.btnVoiceNote = btnVoiceNote;
    this.cardAiSuggestions = cardAiSuggestions;
    this.chipContextHome = chipContextHome;
    this.chipContextOffice = chipContextOffice;
    this.chipContextOnline = chipContextOnline;
    this.chipContextTravel = chipContextTravel;
    this.chipEnergyHigh = chipEnergyHigh;
    this.chipEnergyLow = chipEnergyLow;
    this.chipEnergyMedium = chipEnergyMedium;
    this.chipGroupContext = chipGroupContext;
    this.chipGroupEnergyLevel = chipGroupEnergyLevel;
    this.chipGroupTaskIcon = chipGroupTaskIcon;
    this.chipIconHealth = chipIconHealth;
    this.chipIconIdea = chipIconIdea;
    this.chipIconSport = chipIconSport;
    this.chipIconStudy = chipIconStudy;
    this.chipIconWork = chipIconWork;
    this.colorBlue = colorBlue;
    this.colorGreen = colorGreen;
    this.colorOrange = colorOrange;
    this.colorPurple = colorPurple;
    this.colorRed = colorRed;
    this.layoutAdvancedDetails = layoutAdvancedDetails;
    this.layoutSettings = layoutSettings;
    this.sliderProgress = sliderProgress;
    this.switchBreakDown = switchBreakDown;
    this.switchNotifications = switchNotifications;
    this.switchRecurring = switchRecurring;
    this.taskCategorySelector = taskCategorySelector;
    this.taskCategoryText = taskCategoryText;
    this.taskDescriptionEditText = taskDescriptionEditText;
    this.taskDescriptionInputLayout = taskDescriptionInputLayout;
    this.taskEstimatedTimeEditText = taskEstimatedTimeEditText;
    this.taskEstimatedTimeInputLayout = taskEstimatedTimeInputLayout;
    this.taskExpirationDateEditText = taskExpirationDateEditText;
    this.taskExpirationDateInputLayout = taskExpirationDateInputLayout;
    this.taskExpirationTimeEditText = taskExpirationTimeEditText;
    this.taskExpirationTimeInputLayout = taskExpirationTimeInputLayout;
    this.taskLocationEditText = taskLocationEditText;
    this.taskLocationInputLayout = taskLocationInputLayout;
    this.taskNameEditText = taskNameEditText;
    this.taskNameInputLayout = taskNameInputLayout;
    this.taskPrioritySelector = taskPrioritySelector;
    this.taskPriorityText = taskPriorityText;
    this.taskReminderAutoCompleteTextView = taskReminderAutoCompleteTextView;
    this.taskReminderInputLayout = taskReminderInputLayout;
    this.templateCall = templateCall;
    this.templateExercise = templateExercise;
    this.templateMeeting = templateMeeting;
    this.templateStudy = templateStudy;
    this.toolbar = toolbar;
    this.tvAdvancedDetailsHeader = tvAdvancedDetailsHeader;
    this.tvProgressValue = tvProgressValue;
    this.tvSettingsHeader = tvSettingsHeader;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAddEditKpiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAddEditKpiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_add_edit_kpi, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAddEditKpiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addTaskButton;
      MaterialButton addTaskButton = ViewBindings.findChildViewById(rootView, id);
      if (addTaskButton == null) {
        break missingId;
      }

      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottomAppBar;
      BottomAppBar bottomAppBar = ViewBindings.findChildViewById(rootView, id);
      if (bottomAppBar == null) {
        break missingId;
      }

      id = R.id.btnAddAnother;
      MaterialButton btnAddAnother = ViewBindings.findChildViewById(rootView, id);
      if (btnAddAnother == null) {
        break missingId;
      }

      id = R.id.btnCamera;
      ImageButton btnCamera = ViewBindings.findChildViewById(rootView, id);
      if (btnCamera == null) {
        break missingId;
      }

      id = R.id.btnLocation;
      ImageButton btnLocation = ViewBindings.findChildViewById(rootView, id);
      if (btnLocation == null) {
        break missingId;
      }

      id = R.id.btnSaveDraft;
      MaterialButton btnSaveDraft = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveDraft == null) {
        break missingId;
      }

      id = R.id.btnTemplate;
      ImageButton btnTemplate = ViewBindings.findChildViewById(rootView, id);
      if (btnTemplate == null) {
        break missingId;
      }

      id = R.id.btnTimer;
      ImageButton btnTimer = ViewBindings.findChildViewById(rootView, id);
      if (btnTimer == null) {
        break missingId;
      }

      id = R.id.btnVoiceNote;
      ImageButton btnVoiceNote = ViewBindings.findChildViewById(rootView, id);
      if (btnVoiceNote == null) {
        break missingId;
      }

      id = R.id.cardAiSuggestions;
      MaterialCardView cardAiSuggestions = ViewBindings.findChildViewById(rootView, id);
      if (cardAiSuggestions == null) {
        break missingId;
      }

      id = R.id.chipContextHome;
      Chip chipContextHome = ViewBindings.findChildViewById(rootView, id);
      if (chipContextHome == null) {
        break missingId;
      }

      id = R.id.chipContextOffice;
      Chip chipContextOffice = ViewBindings.findChildViewById(rootView, id);
      if (chipContextOffice == null) {
        break missingId;
      }

      id = R.id.chipContextOnline;
      Chip chipContextOnline = ViewBindings.findChildViewById(rootView, id);
      if (chipContextOnline == null) {
        break missingId;
      }

      id = R.id.chipContextTravel;
      Chip chipContextTravel = ViewBindings.findChildViewById(rootView, id);
      if (chipContextTravel == null) {
        break missingId;
      }

      id = R.id.chipEnergyHigh;
      Chip chipEnergyHigh = ViewBindings.findChildViewById(rootView, id);
      if (chipEnergyHigh == null) {
        break missingId;
      }

      id = R.id.chipEnergyLow;
      Chip chipEnergyLow = ViewBindings.findChildViewById(rootView, id);
      if (chipEnergyLow == null) {
        break missingId;
      }

      id = R.id.chipEnergyMedium;
      Chip chipEnergyMedium = ViewBindings.findChildViewById(rootView, id);
      if (chipEnergyMedium == null) {
        break missingId;
      }

      id = R.id.chipGroupContext;
      ChipGroup chipGroupContext = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupContext == null) {
        break missingId;
      }

      id = R.id.chipGroupEnergyLevel;
      ChipGroup chipGroupEnergyLevel = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupEnergyLevel == null) {
        break missingId;
      }

      id = R.id.chipGroupTaskIcon;
      ChipGroup chipGroupTaskIcon = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupTaskIcon == null) {
        break missingId;
      }

      id = R.id.chipIconHealth;
      Chip chipIconHealth = ViewBindings.findChildViewById(rootView, id);
      if (chipIconHealth == null) {
        break missingId;
      }

      id = R.id.chipIconIdea;
      Chip chipIconIdea = ViewBindings.findChildViewById(rootView, id);
      if (chipIconIdea == null) {
        break missingId;
      }

      id = R.id.chipIconSport;
      Chip chipIconSport = ViewBindings.findChildViewById(rootView, id);
      if (chipIconSport == null) {
        break missingId;
      }

      id = R.id.chipIconStudy;
      Chip chipIconStudy = ViewBindings.findChildViewById(rootView, id);
      if (chipIconStudy == null) {
        break missingId;
      }

      id = R.id.chipIconWork;
      Chip chipIconWork = ViewBindings.findChildViewById(rootView, id);
      if (chipIconWork == null) {
        break missingId;
      }

      id = R.id.colorBlue;
      View colorBlue = ViewBindings.findChildViewById(rootView, id);
      if (colorBlue == null) {
        break missingId;
      }

      id = R.id.colorGreen;
      View colorGreen = ViewBindings.findChildViewById(rootView, id);
      if (colorGreen == null) {
        break missingId;
      }

      id = R.id.colorOrange;
      View colorOrange = ViewBindings.findChildViewById(rootView, id);
      if (colorOrange == null) {
        break missingId;
      }

      id = R.id.colorPurple;
      View colorPurple = ViewBindings.findChildViewById(rootView, id);
      if (colorPurple == null) {
        break missingId;
      }

      id = R.id.colorRed;
      View colorRed = ViewBindings.findChildViewById(rootView, id);
      if (colorRed == null) {
        break missingId;
      }

      id = R.id.layoutAdvancedDetails;
      LinearLayout layoutAdvancedDetails = ViewBindings.findChildViewById(rootView, id);
      if (layoutAdvancedDetails == null) {
        break missingId;
      }

      id = R.id.layoutSettings;
      LinearLayout layoutSettings = ViewBindings.findChildViewById(rootView, id);
      if (layoutSettings == null) {
        break missingId;
      }

      id = R.id.sliderProgress;
      Slider sliderProgress = ViewBindings.findChildViewById(rootView, id);
      if (sliderProgress == null) {
        break missingId;
      }

      id = R.id.switchBreakDown;
      MaterialSwitch switchBreakDown = ViewBindings.findChildViewById(rootView, id);
      if (switchBreakDown == null) {
        break missingId;
      }

      id = R.id.switchNotifications;
      MaterialSwitch switchNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchNotifications == null) {
        break missingId;
      }

      id = R.id.switchRecurring;
      MaterialSwitch switchRecurring = ViewBindings.findChildViewById(rootView, id);
      if (switchRecurring == null) {
        break missingId;
      }

      id = R.id.taskCategorySelector;
      LinearLayout taskCategorySelector = ViewBindings.findChildViewById(rootView, id);
      if (taskCategorySelector == null) {
        break missingId;
      }

      id = R.id.taskCategoryText;
      TextView taskCategoryText = ViewBindings.findChildViewById(rootView, id);
      if (taskCategoryText == null) {
        break missingId;
      }

      id = R.id.taskDescriptionEditText;
      TextInputEditText taskDescriptionEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionEditText == null) {
        break missingId;
      }

      id = R.id.taskDescriptionInputLayout;
      TextInputLayout taskDescriptionInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionInputLayout == null) {
        break missingId;
      }

      id = R.id.taskEstimatedTimeEditText;
      TextInputEditText taskEstimatedTimeEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskEstimatedTimeEditText == null) {
        break missingId;
      }

      id = R.id.taskEstimatedTimeInputLayout;
      TextInputLayout taskEstimatedTimeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskEstimatedTimeInputLayout == null) {
        break missingId;
      }

      id = R.id.taskExpirationDateEditText;
      TextInputEditText taskExpirationDateEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationDateEditText == null) {
        break missingId;
      }

      id = R.id.taskExpirationDateInputLayout;
      TextInputLayout taskExpirationDateInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationDateInputLayout == null) {
        break missingId;
      }

      id = R.id.taskExpirationTimeEditText;
      TextInputEditText taskExpirationTimeEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationTimeEditText == null) {
        break missingId;
      }

      id = R.id.taskExpirationTimeInputLayout;
      TextInputLayout taskExpirationTimeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationTimeInputLayout == null) {
        break missingId;
      }

      id = R.id.taskLocationEditText;
      TextInputEditText taskLocationEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskLocationEditText == null) {
        break missingId;
      }

      id = R.id.taskLocationInputLayout;
      TextInputLayout taskLocationInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskLocationInputLayout == null) {
        break missingId;
      }

      id = R.id.taskNameEditText;
      TextInputEditText taskNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskNameEditText == null) {
        break missingId;
      }

      id = R.id.taskNameInputLayout;
      TextInputLayout taskNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskNameInputLayout == null) {
        break missingId;
      }

      id = R.id.taskPrioritySelector;
      LinearLayout taskPrioritySelector = ViewBindings.findChildViewById(rootView, id);
      if (taskPrioritySelector == null) {
        break missingId;
      }

      id = R.id.taskPriorityText;
      TextView taskPriorityText = ViewBindings.findChildViewById(rootView, id);
      if (taskPriorityText == null) {
        break missingId;
      }

      id = R.id.taskReminderAutoCompleteTextView;
      MaterialAutoCompleteTextView taskReminderAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskReminderAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskReminderInputLayout;
      TextInputLayout taskReminderInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskReminderInputLayout == null) {
        break missingId;
      }

      id = R.id.templateCall;
      MaterialCardView templateCall = ViewBindings.findChildViewById(rootView, id);
      if (templateCall == null) {
        break missingId;
      }

      id = R.id.templateExercise;
      MaterialCardView templateExercise = ViewBindings.findChildViewById(rootView, id);
      if (templateExercise == null) {
        break missingId;
      }

      id = R.id.templateMeeting;
      MaterialCardView templateMeeting = ViewBindings.findChildViewById(rootView, id);
      if (templateMeeting == null) {
        break missingId;
      }

      id = R.id.templateStudy;
      MaterialCardView templateStudy = ViewBindings.findChildViewById(rootView, id);
      if (templateStudy == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvAdvancedDetailsHeader;
      TextView tvAdvancedDetailsHeader = ViewBindings.findChildViewById(rootView, id);
      if (tvAdvancedDetailsHeader == null) {
        break missingId;
      }

      id = R.id.tvProgressValue;
      TextView tvProgressValue = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressValue == null) {
        break missingId;
      }

      id = R.id.tvSettingsHeader;
      TextView tvSettingsHeader = ViewBindings.findChildViewById(rootView, id);
      if (tvSettingsHeader == null) {
        break missingId;
      }

      return new ActivityAddEditKpiBinding((CoordinatorLayout) rootView, addTaskButton,
          appBarLayout, bottomAppBar, btnAddAnother, btnCamera, btnLocation, btnSaveDraft,
          btnTemplate, btnTimer, btnVoiceNote, cardAiSuggestions, chipContextHome,
          chipContextOffice, chipContextOnline, chipContextTravel, chipEnergyHigh, chipEnergyLow,
          chipEnergyMedium, chipGroupContext, chipGroupEnergyLevel, chipGroupTaskIcon,
          chipIconHealth, chipIconIdea, chipIconSport, chipIconStudy, chipIconWork, colorBlue,
          colorGreen, colorOrange, colorPurple, colorRed, layoutAdvancedDetails, layoutSettings,
          sliderProgress, switchBreakDown, switchNotifications, switchRecurring,
          taskCategorySelector, taskCategoryText, taskDescriptionEditText,
          taskDescriptionInputLayout, taskEstimatedTimeEditText, taskEstimatedTimeInputLayout,
          taskExpirationDateEditText, taskExpirationDateInputLayout, taskExpirationTimeEditText,
          taskExpirationTimeInputLayout, taskLocationEditText, taskLocationInputLayout,
          taskNameEditText, taskNameInputLayout, taskPrioritySelector, taskPriorityText,
          taskReminderAutoCompleteTextView, taskReminderInputLayout, templateCall, templateExercise,
          templateMeeting, templateStudy, toolbar, tvAdvancedDetailsHeader, tvProgressValue,
          tvSettingsHeader);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
