// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNotificationBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView messageTextView;

  @NonNull
  public final TextView timeTextView;

  @NonNull
  public final TextView titleTextView;

  @NonNull
  public final TextView typeIconTextView;

  @NonNull
  public final View unreadIndicator;

  private ItemNotificationBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView messageTextView, @NonNull TextView timeTextView,
      @NonNull TextView titleTextView, @NonNull TextView typeIconTextView,
      @NonNull View unreadIndicator) {
    this.rootView = rootView;
    this.messageTextView = messageTextView;
    this.timeTextView = timeTextView;
    this.titleTextView = titleTextView;
    this.typeIconTextView = typeIconTextView;
    this.unreadIndicator = unreadIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_notification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNotificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.messageTextView;
      TextView messageTextView = ViewBindings.findChildViewById(rootView, id);
      if (messageTextView == null) {
        break missingId;
      }

      id = R.id.timeTextView;
      TextView timeTextView = ViewBindings.findChildViewById(rootView, id);
      if (timeTextView == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      id = R.id.typeIconTextView;
      TextView typeIconTextView = ViewBindings.findChildViewById(rootView, id);
      if (typeIconTextView == null) {
        break missingId;
      }

      id = R.id.unreadIndicator;
      View unreadIndicator = ViewBindings.findChildViewById(rootView, id);
      if (unreadIndicator == null) {
        break missingId;
      }

      return new ItemNotificationBinding((MaterialCardView) rootView, messageTextView, timeTextView,
          titleTextView, typeIconTextView, unreadIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
