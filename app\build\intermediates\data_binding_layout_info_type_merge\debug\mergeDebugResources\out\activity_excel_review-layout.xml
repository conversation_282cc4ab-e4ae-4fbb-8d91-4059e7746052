<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_excel_review" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_excel_review.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_excel_review_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="105" endOffset="51"/></Target><Target id="@+id/tvExcelReviewTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/spinnerUserFilter" view="Spinner"><Expressions/><location startLine="22" startOffset="4" endLine="34" endOffset="35"/></Target><Target id="@+id/spinnerMonthFilter" view="Spinner"><Expressions/><location startLine="36" startOffset="4" endLine="48" endOffset="35"/></Target><Target id="@+id/rgAggregationOptions" view="RadioGroup"><Expressions/><location startLine="51" startOffset="4" endLine="78" endOffset="16"/></Target><Target id="@+id/rbImportIndividual" view="RadioButton"><Expressions/><location startLine="65" startOffset="8" endLine="70" endOffset="35"/></Target><Target id="@+id/rbCalculateAverage" view="RadioButton"><Expressions/><location startLine="72" startOffset="8" endLine="77" endOffset="45"/></Target><Target id="@+id/rvExcelReviewItems" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="80" startOffset="4" endLine="91" endOffset="52"/></Target><Target id="@+id/btnConfirmExcelImport" view="Button"><Expressions/><location startLine="93" startOffset="4" endLine="103" endOffset="55"/></Target></Targets></Layout>