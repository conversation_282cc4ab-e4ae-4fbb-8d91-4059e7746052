// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.textfield.MaterialAutoCompleteTextView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAddEditKpiOriginalBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextInputEditText kpiDailyTargetEditText;

  @NonNull
  public final TextInputLayout kpiDailyTargetInputLayout;

  @NonNull
  public final TextInputEditText kpiDescriptionEditText;

  @NonNull
  public final TextInputLayout kpiDescriptionInputLayout;

  @NonNull
  public final TextInputEditText kpiMonthlyTargetEditText;

  @NonNull
  public final TextInputLayout kpiMonthlyTargetInputLayout;

  @NonNull
  public final MaterialAutoCompleteTextView kpiNameEditText;

  @NonNull
  public final TextInputLayout kpiNameInputLayout;

  @NonNull
  public final TextInputEditText kpiQuarterlyTargetEditText;

  @NonNull
  public final TextInputLayout kpiQuarterlyTargetInputLayout;

  @NonNull
  public final TextInputEditText kpiTargetEditText;

  @NonNull
  public final TextInputLayout kpiTargetInputLayout;

  @NonNull
  public final TextView kpiUnitLabelTextView;

  @NonNull
  public final RadioGroup kpiUnitRadioGroup;

  @NonNull
  public final ImageView ownerImageView;

  @NonNull
  public final TextInputEditText ownerNameEditText;

  @NonNull
  public final TextInputLayout ownerNameInputLayout;

  @NonNull
  public final LinearLayout ownerNameSelectionLayout;

  @NonNull
  public final AutoCompleteTextView ownerTypeAutoCompleteTextView;

  @NonNull
  public final TextInputLayout ownerTypeInputLayout;

  @NonNull
  public final Button saveKpiButton;

  @NonNull
  public final Button selectOwnerImageButton;

  @NonNull
  public final RadioButton unitCurrencyRadioButton;

  @NonNull
  public final RadioButton unitNumberRadioButton;

  @NonNull
  public final RadioButton unitPercentageRadioButton;

  @NonNull
  public final RadioButton unitPointRadioButton;

  private ActivityAddEditKpiOriginalBinding(@NonNull ScrollView rootView,
      @NonNull TextInputEditText kpiDailyTargetEditText,
      @NonNull TextInputLayout kpiDailyTargetInputLayout,
      @NonNull TextInputEditText kpiDescriptionEditText,
      @NonNull TextInputLayout kpiDescriptionInputLayout,
      @NonNull TextInputEditText kpiMonthlyTargetEditText,
      @NonNull TextInputLayout kpiMonthlyTargetInputLayout,
      @NonNull MaterialAutoCompleteTextView kpiNameEditText,
      @NonNull TextInputLayout kpiNameInputLayout,
      @NonNull TextInputEditText kpiQuarterlyTargetEditText,
      @NonNull TextInputLayout kpiQuarterlyTargetInputLayout,
      @NonNull TextInputEditText kpiTargetEditText, @NonNull TextInputLayout kpiTargetInputLayout,
      @NonNull TextView kpiUnitLabelTextView, @NonNull RadioGroup kpiUnitRadioGroup,
      @NonNull ImageView ownerImageView, @NonNull TextInputEditText ownerNameEditText,
      @NonNull TextInputLayout ownerNameInputLayout, @NonNull LinearLayout ownerNameSelectionLayout,
      @NonNull AutoCompleteTextView ownerTypeAutoCompleteTextView,
      @NonNull TextInputLayout ownerTypeInputLayout, @NonNull Button saveKpiButton,
      @NonNull Button selectOwnerImageButton, @NonNull RadioButton unitCurrencyRadioButton,
      @NonNull RadioButton unitNumberRadioButton, @NonNull RadioButton unitPercentageRadioButton,
      @NonNull RadioButton unitPointRadioButton) {
    this.rootView = rootView;
    this.kpiDailyTargetEditText = kpiDailyTargetEditText;
    this.kpiDailyTargetInputLayout = kpiDailyTargetInputLayout;
    this.kpiDescriptionEditText = kpiDescriptionEditText;
    this.kpiDescriptionInputLayout = kpiDescriptionInputLayout;
    this.kpiMonthlyTargetEditText = kpiMonthlyTargetEditText;
    this.kpiMonthlyTargetInputLayout = kpiMonthlyTargetInputLayout;
    this.kpiNameEditText = kpiNameEditText;
    this.kpiNameInputLayout = kpiNameInputLayout;
    this.kpiQuarterlyTargetEditText = kpiQuarterlyTargetEditText;
    this.kpiQuarterlyTargetInputLayout = kpiQuarterlyTargetInputLayout;
    this.kpiTargetEditText = kpiTargetEditText;
    this.kpiTargetInputLayout = kpiTargetInputLayout;
    this.kpiUnitLabelTextView = kpiUnitLabelTextView;
    this.kpiUnitRadioGroup = kpiUnitRadioGroup;
    this.ownerImageView = ownerImageView;
    this.ownerNameEditText = ownerNameEditText;
    this.ownerNameInputLayout = ownerNameInputLayout;
    this.ownerNameSelectionLayout = ownerNameSelectionLayout;
    this.ownerTypeAutoCompleteTextView = ownerTypeAutoCompleteTextView;
    this.ownerTypeInputLayout = ownerTypeInputLayout;
    this.saveKpiButton = saveKpiButton;
    this.selectOwnerImageButton = selectOwnerImageButton;
    this.unitCurrencyRadioButton = unitCurrencyRadioButton;
    this.unitNumberRadioButton = unitNumberRadioButton;
    this.unitPercentageRadioButton = unitPercentageRadioButton;
    this.unitPointRadioButton = unitPointRadioButton;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAddEditKpiOriginalBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAddEditKpiOriginalBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_add_edit_kpi_original, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAddEditKpiOriginalBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.kpiDailyTargetEditText;
      TextInputEditText kpiDailyTargetEditText = ViewBindings.findChildViewById(rootView, id);
      if (kpiDailyTargetEditText == null) {
        break missingId;
      }

      id = R.id.kpiDailyTargetInputLayout;
      TextInputLayout kpiDailyTargetInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (kpiDailyTargetInputLayout == null) {
        break missingId;
      }

      id = R.id.kpiDescriptionEditText;
      TextInputEditText kpiDescriptionEditText = ViewBindings.findChildViewById(rootView, id);
      if (kpiDescriptionEditText == null) {
        break missingId;
      }

      id = R.id.kpiDescriptionInputLayout;
      TextInputLayout kpiDescriptionInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (kpiDescriptionInputLayout == null) {
        break missingId;
      }

      id = R.id.kpiMonthlyTargetEditText;
      TextInputEditText kpiMonthlyTargetEditText = ViewBindings.findChildViewById(rootView, id);
      if (kpiMonthlyTargetEditText == null) {
        break missingId;
      }

      id = R.id.kpiMonthlyTargetInputLayout;
      TextInputLayout kpiMonthlyTargetInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (kpiMonthlyTargetInputLayout == null) {
        break missingId;
      }

      id = R.id.kpiNameEditText;
      MaterialAutoCompleteTextView kpiNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (kpiNameEditText == null) {
        break missingId;
      }

      id = R.id.kpiNameInputLayout;
      TextInputLayout kpiNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (kpiNameInputLayout == null) {
        break missingId;
      }

      id = R.id.kpiQuarterlyTargetEditText;
      TextInputEditText kpiQuarterlyTargetEditText = ViewBindings.findChildViewById(rootView, id);
      if (kpiQuarterlyTargetEditText == null) {
        break missingId;
      }

      id = R.id.kpiQuarterlyTargetInputLayout;
      TextInputLayout kpiQuarterlyTargetInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (kpiQuarterlyTargetInputLayout == null) {
        break missingId;
      }

      id = R.id.kpiTargetEditText;
      TextInputEditText kpiTargetEditText = ViewBindings.findChildViewById(rootView, id);
      if (kpiTargetEditText == null) {
        break missingId;
      }

      id = R.id.kpiTargetInputLayout;
      TextInputLayout kpiTargetInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (kpiTargetInputLayout == null) {
        break missingId;
      }

      id = R.id.kpiUnitLabelTextView;
      TextView kpiUnitLabelTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiUnitLabelTextView == null) {
        break missingId;
      }

      id = R.id.kpiUnitRadioGroup;
      RadioGroup kpiUnitRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (kpiUnitRadioGroup == null) {
        break missingId;
      }

      id = R.id.ownerImageView;
      ImageView ownerImageView = ViewBindings.findChildViewById(rootView, id);
      if (ownerImageView == null) {
        break missingId;
      }

      id = R.id.ownerNameEditText;
      TextInputEditText ownerNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (ownerNameEditText == null) {
        break missingId;
      }

      id = R.id.ownerNameInputLayout;
      TextInputLayout ownerNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (ownerNameInputLayout == null) {
        break missingId;
      }

      id = R.id.ownerNameSelectionLayout;
      LinearLayout ownerNameSelectionLayout = ViewBindings.findChildViewById(rootView, id);
      if (ownerNameSelectionLayout == null) {
        break missingId;
      }

      id = R.id.ownerTypeAutoCompleteTextView;
      AutoCompleteTextView ownerTypeAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (ownerTypeAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.ownerTypeInputLayout;
      TextInputLayout ownerTypeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (ownerTypeInputLayout == null) {
        break missingId;
      }

      id = R.id.saveKpiButton;
      Button saveKpiButton = ViewBindings.findChildViewById(rootView, id);
      if (saveKpiButton == null) {
        break missingId;
      }

      id = R.id.selectOwnerImageButton;
      Button selectOwnerImageButton = ViewBindings.findChildViewById(rootView, id);
      if (selectOwnerImageButton == null) {
        break missingId;
      }

      id = R.id.unitCurrencyRadioButton;
      RadioButton unitCurrencyRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (unitCurrencyRadioButton == null) {
        break missingId;
      }

      id = R.id.unitNumberRadioButton;
      RadioButton unitNumberRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (unitNumberRadioButton == null) {
        break missingId;
      }

      id = R.id.unitPercentageRadioButton;
      RadioButton unitPercentageRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (unitPercentageRadioButton == null) {
        break missingId;
      }

      id = R.id.unitPointRadioButton;
      RadioButton unitPointRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (unitPointRadioButton == null) {
        break missingId;
      }

      return new ActivityAddEditKpiOriginalBinding((ScrollView) rootView, kpiDailyTargetEditText,
          kpiDailyTargetInputLayout, kpiDescriptionEditText, kpiDescriptionInputLayout,
          kpiMonthlyTargetEditText, kpiMonthlyTargetInputLayout, kpiNameEditText,
          kpiNameInputLayout, kpiQuarterlyTargetEditText, kpiQuarterlyTargetInputLayout,
          kpiTargetEditText, kpiTargetInputLayout, kpiUnitLabelTextView, kpiUnitRadioGroup,
          ownerImageView, ownerNameEditText, ownerNameInputLayout, ownerNameSelectionLayout,
          ownerTypeAutoCompleteTextView, ownerTypeInputLayout, saveKpiButton,
          selectOwnerImageButton, unitCurrencyRadioButton, unitNumberRadioButton,
          unitPercentageRadioButton, unitPointRadioButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
