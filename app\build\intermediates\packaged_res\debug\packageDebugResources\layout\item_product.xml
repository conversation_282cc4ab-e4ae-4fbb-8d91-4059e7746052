<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Status Indicator -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/statusIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="2dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/success_color" />

        <!-- Product Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Product Name -->
            <TextView
                android:id="@+id/productNameText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp"
                tools:text="منتج تجريبي" />

            <!-- Category -->
            <TextView
                android:id="@+id/categoryText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp"
                tools:text="🍎 طعام" />

            <!-- Quantity and Location -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:id="@+id/quantityText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="الكمية: 5" />

                <TextView
                    android:id="@+id/locationText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="الموقع: المخزن" />

            </LinearLayout>

            <!-- Expiry Date -->
            <TextView
                android:id="@+id/expiryDateText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginBottom="4dp"
                tools:text="تاريخ الانتهاء: 15/08/2025" />

            <!-- Notes -->
            <TextView
                android:id="@+id/notesText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                tools:text="ملاحظات: يحفظ في مكان بارد" />

        </LinearLayout>

        <!-- Days Until Expiry -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginStart="16dp">

            <TextView
                android:id="@+id/daysText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                tools:text="ينتهي خلال 5 أيام" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
