package com.example.kpitrackerapp.persistence;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.kpitrackerapp.models.Subtask;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SubtaskDao_Impl implements SubtaskDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Subtask> __insertionAdapterOfSubtask;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<Subtask> __deletionAdapterOfSubtask;

  private final EntityDeletionOrUpdateAdapter<Subtask> __updateAdapterOfSubtask;

  private final SharedSQLiteStatement __preparedStmtOfUpdateSubtaskCompletion;

  private final SharedSQLiteStatement __preparedStmtOfUpdateSubtaskOrder;

  private final SharedSQLiteStatement __preparedStmtOfDeleteSubtasksForTask;

  private final SharedSQLiteStatement __preparedStmtOfUpdateActualTime;

  public SubtaskDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSubtask = new EntityInsertionAdapter<Subtask>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `subtasks` (`id`,`parent_task_id`,`name`,`description`,`is_completed`,`order_index`,`assigned_user_id`,`due_date`,`created_at`,`completed_at`,`estimated_minutes`,`actual_minutes`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Subtask entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getParentTaskId());
        statement.bindString(3, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getOrder());
        if (entity.getAssignedUserId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAssignedUserId());
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getDueDate());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_1);
        }
        final Long _tmp_2 = __converters.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp_2);
        }
        final Long _tmp_3 = __converters.dateToTimestamp(entity.getCompletedAt());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_3);
        }
        if (entity.getEstimatedMinutes() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getEstimatedMinutes());
        }
        if (entity.getActualMinutes() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getActualMinutes());
        }
      }
    };
    this.__deletionAdapterOfSubtask = new EntityDeletionOrUpdateAdapter<Subtask>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `subtasks` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Subtask entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfSubtask = new EntityDeletionOrUpdateAdapter<Subtask>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `subtasks` SET `id` = ?,`parent_task_id` = ?,`name` = ?,`description` = ?,`is_completed` = ?,`order_index` = ?,`assigned_user_id` = ?,`due_date` = ?,`created_at` = ?,`completed_at` = ?,`estimated_minutes` = ?,`actual_minutes` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Subtask entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getParentTaskId());
        statement.bindString(3, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getOrder());
        if (entity.getAssignedUserId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAssignedUserId());
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getDueDate());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_1);
        }
        final Long _tmp_2 = __converters.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp_2);
        }
        final Long _tmp_3 = __converters.dateToTimestamp(entity.getCompletedAt());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_3);
        }
        if (entity.getEstimatedMinutes() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getEstimatedMinutes());
        }
        if (entity.getActualMinutes() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getActualMinutes());
        }
        statement.bindLong(13, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateSubtaskCompletion = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE subtasks SET is_completed = ?, completed_at = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateSubtaskOrder = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE subtasks SET order_index = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteSubtasksForTask = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM subtasks WHERE parent_task_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateActualTime = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE subtasks SET actual_minutes = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertSubtask(final Subtask subtask, final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfSubtask.insertAndReturnId(subtask);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertSubtasks(final List<Subtask> subtasks,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSubtask.insert(subtasks);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSubtask(final Subtask subtask, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfSubtask.handle(subtask);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSubtask(final Subtask subtask, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSubtask.handle(subtask);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSubtaskCompletion(final int subtaskId, final boolean isCompleted,
      final Date completedAt, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateSubtaskCompletion.acquire();
        int _argIndex = 1;
        final int _tmp = isCompleted ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        final Long _tmp_1 = __converters.dateToTimestamp(completedAt);
        if (_tmp_1 == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp_1);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, subtaskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateSubtaskCompletion.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSubtaskOrder(final int subtaskId, final int newOrder,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateSubtaskOrder.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, newOrder);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, subtaskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateSubtaskOrder.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSubtasksForTask(final int taskId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteSubtasksForTask.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, taskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteSubtasksForTask.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateActualTime(final int subtaskId, final int actualMinutes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateActualTime.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, actualMinutes);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, subtaskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateActualTime.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<Subtask> getSubtaskById(final int subtaskId) {
    final String _sql = "SELECT * FROM subtasks WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, subtaskId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subtasks"}, new Callable<Subtask>() {
      @Override
      @Nullable
      public Subtask call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "due_date");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_at");
          final int _cursorIndexOfEstimatedMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_minutes");
          final int _cursorIndexOfActualMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_minutes");
          final Subtask _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpParentTaskId;
            _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpOrder;
            _tmpOrder = _cursor.getInt(_cursorIndexOfOrder);
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Date _tmpDueDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.fromTimestamp(_tmp_1);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Date _tmpCompletedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __converters.fromTimestamp(_tmp_4);
            final Integer _tmpEstimatedMinutes;
            if (_cursor.isNull(_cursorIndexOfEstimatedMinutes)) {
              _tmpEstimatedMinutes = null;
            } else {
              _tmpEstimatedMinutes = _cursor.getInt(_cursorIndexOfEstimatedMinutes);
            }
            final Integer _tmpActualMinutes;
            if (_cursor.isNull(_cursorIndexOfActualMinutes)) {
              _tmpActualMinutes = null;
            } else {
              _tmpActualMinutes = _cursor.getInt(_cursorIndexOfActualMinutes);
            }
            _result = new Subtask(_tmpId,_tmpParentTaskId,_tmpName,_tmpDescription,_tmpIsCompleted,_tmpOrder,_tmpAssignedUserId,_tmpDueDate,_tmpCreatedAt,_tmpCompletedAt,_tmpEstimatedMinutes,_tmpActualMinutes);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Subtask>> getSubtasksForTask(final int taskId) {
    final String _sql = "SELECT * FROM subtasks WHERE parent_task_id = ? ORDER BY order_index ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, taskId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subtasks"}, new Callable<List<Subtask>>() {
      @Override
      @NonNull
      public List<Subtask> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "due_date");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_at");
          final int _cursorIndexOfEstimatedMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_minutes");
          final int _cursorIndexOfActualMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_minutes");
          final List<Subtask> _result = new ArrayList<Subtask>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Subtask _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpParentTaskId;
            _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpOrder;
            _tmpOrder = _cursor.getInt(_cursorIndexOfOrder);
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Date _tmpDueDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.fromTimestamp(_tmp_1);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Date _tmpCompletedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __converters.fromTimestamp(_tmp_4);
            final Integer _tmpEstimatedMinutes;
            if (_cursor.isNull(_cursorIndexOfEstimatedMinutes)) {
              _tmpEstimatedMinutes = null;
            } else {
              _tmpEstimatedMinutes = _cursor.getInt(_cursorIndexOfEstimatedMinutes);
            }
            final Integer _tmpActualMinutes;
            if (_cursor.isNull(_cursorIndexOfActualMinutes)) {
              _tmpActualMinutes = null;
            } else {
              _tmpActualMinutes = _cursor.getInt(_cursorIndexOfActualMinutes);
            }
            _item = new Subtask(_tmpId,_tmpParentTaskId,_tmpName,_tmpDescription,_tmpIsCompleted,_tmpOrder,_tmpAssignedUserId,_tmpDueDate,_tmpCreatedAt,_tmpCompletedAt,_tmpEstimatedMinutes,_tmpActualMinutes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Subtask>> getIncompleteSubtasksForTask(final int taskId) {
    final String _sql = "SELECT * FROM subtasks WHERE parent_task_id = ? AND is_completed = 0 ORDER BY order_index ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, taskId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subtasks"}, new Callable<List<Subtask>>() {
      @Override
      @NonNull
      public List<Subtask> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "due_date");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_at");
          final int _cursorIndexOfEstimatedMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_minutes");
          final int _cursorIndexOfActualMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_minutes");
          final List<Subtask> _result = new ArrayList<Subtask>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Subtask _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpParentTaskId;
            _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpOrder;
            _tmpOrder = _cursor.getInt(_cursorIndexOfOrder);
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Date _tmpDueDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.fromTimestamp(_tmp_1);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Date _tmpCompletedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __converters.fromTimestamp(_tmp_4);
            final Integer _tmpEstimatedMinutes;
            if (_cursor.isNull(_cursorIndexOfEstimatedMinutes)) {
              _tmpEstimatedMinutes = null;
            } else {
              _tmpEstimatedMinutes = _cursor.getInt(_cursorIndexOfEstimatedMinutes);
            }
            final Integer _tmpActualMinutes;
            if (_cursor.isNull(_cursorIndexOfActualMinutes)) {
              _tmpActualMinutes = null;
            } else {
              _tmpActualMinutes = _cursor.getInt(_cursorIndexOfActualMinutes);
            }
            _item = new Subtask(_tmpId,_tmpParentTaskId,_tmpName,_tmpDescription,_tmpIsCompleted,_tmpOrder,_tmpAssignedUserId,_tmpDueDate,_tmpCreatedAt,_tmpCompletedAt,_tmpEstimatedMinutes,_tmpActualMinutes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Subtask>> getCompletedSubtasksForTask(final int taskId) {
    final String _sql = "SELECT * FROM subtasks WHERE parent_task_id = ? AND is_completed = 1 ORDER BY completed_at DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, taskId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subtasks"}, new Callable<List<Subtask>>() {
      @Override
      @NonNull
      public List<Subtask> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "due_date");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_at");
          final int _cursorIndexOfEstimatedMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_minutes");
          final int _cursorIndexOfActualMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_minutes");
          final List<Subtask> _result = new ArrayList<Subtask>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Subtask _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpParentTaskId;
            _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpOrder;
            _tmpOrder = _cursor.getInt(_cursorIndexOfOrder);
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Date _tmpDueDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.fromTimestamp(_tmp_1);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Date _tmpCompletedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __converters.fromTimestamp(_tmp_4);
            final Integer _tmpEstimatedMinutes;
            if (_cursor.isNull(_cursorIndexOfEstimatedMinutes)) {
              _tmpEstimatedMinutes = null;
            } else {
              _tmpEstimatedMinutes = _cursor.getInt(_cursorIndexOfEstimatedMinutes);
            }
            final Integer _tmpActualMinutes;
            if (_cursor.isNull(_cursorIndexOfActualMinutes)) {
              _tmpActualMinutes = null;
            } else {
              _tmpActualMinutes = _cursor.getInt(_cursorIndexOfActualMinutes);
            }
            _item = new Subtask(_tmpId,_tmpParentTaskId,_tmpName,_tmpDescription,_tmpIsCompleted,_tmpOrder,_tmpAssignedUserId,_tmpDueDate,_tmpCreatedAt,_tmpCompletedAt,_tmpEstimatedMinutes,_tmpActualMinutes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSubtaskCount(final int taskId, final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM subtasks WHERE parent_task_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, taskId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedSubtaskCount(final int taskId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM subtasks WHERE parent_task_id = ? AND is_completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, taskId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Subtask>> getSubtasksForUser(final String userId) {
    final String _sql = "SELECT * FROM subtasks WHERE assigned_user_id = ? AND is_completed = 0 ORDER BY due_date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subtasks"}, new Callable<List<Subtask>>() {
      @Override
      @NonNull
      public List<Subtask> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "due_date");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_at");
          final int _cursorIndexOfEstimatedMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_minutes");
          final int _cursorIndexOfActualMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_minutes");
          final List<Subtask> _result = new ArrayList<Subtask>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Subtask _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpParentTaskId;
            _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpOrder;
            _tmpOrder = _cursor.getInt(_cursorIndexOfOrder);
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Date _tmpDueDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.fromTimestamp(_tmp_1);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Date _tmpCompletedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __converters.fromTimestamp(_tmp_4);
            final Integer _tmpEstimatedMinutes;
            if (_cursor.isNull(_cursorIndexOfEstimatedMinutes)) {
              _tmpEstimatedMinutes = null;
            } else {
              _tmpEstimatedMinutes = _cursor.getInt(_cursorIndexOfEstimatedMinutes);
            }
            final Integer _tmpActualMinutes;
            if (_cursor.isNull(_cursorIndexOfActualMinutes)) {
              _tmpActualMinutes = null;
            } else {
              _tmpActualMinutes = _cursor.getInt(_cursorIndexOfActualMinutes);
            }
            _item = new Subtask(_tmpId,_tmpParentTaskId,_tmpName,_tmpDescription,_tmpIsCompleted,_tmpOrder,_tmpAssignedUserId,_tmpDueDate,_tmpCreatedAt,_tmpCompletedAt,_tmpEstimatedMinutes,_tmpActualMinutes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Subtask>> getOverdueSubtasks(final Date date) {
    final String _sql = "SELECT * FROM subtasks WHERE due_date <= ? AND is_completed = 0 ORDER BY due_date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subtasks"}, new Callable<List<Subtask>>() {
      @Override
      @NonNull
      public List<Subtask> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "due_date");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_at");
          final int _cursorIndexOfEstimatedMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_minutes");
          final int _cursorIndexOfActualMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_minutes");
          final List<Subtask> _result = new ArrayList<Subtask>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Subtask _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpParentTaskId;
            _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final int _tmpOrder;
            _tmpOrder = _cursor.getInt(_cursorIndexOfOrder);
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Date _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.fromTimestamp(_tmp_2);
            final Date _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Date _tmp_4 = __converters.fromTimestamp(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final Date _tmpCompletedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __converters.fromTimestamp(_tmp_5);
            final Integer _tmpEstimatedMinutes;
            if (_cursor.isNull(_cursorIndexOfEstimatedMinutes)) {
              _tmpEstimatedMinutes = null;
            } else {
              _tmpEstimatedMinutes = _cursor.getInt(_cursorIndexOfEstimatedMinutes);
            }
            final Integer _tmpActualMinutes;
            if (_cursor.isNull(_cursorIndexOfActualMinutes)) {
              _tmpActualMinutes = null;
            } else {
              _tmpActualMinutes = _cursor.getInt(_cursorIndexOfActualMinutes);
            }
            _item = new Subtask(_tmpId,_tmpParentTaskId,_tmpName,_tmpDescription,_tmpIsCompleted,_tmpOrder,_tmpAssignedUserId,_tmpDueDate,_tmpCreatedAt,_tmpCompletedAt,_tmpEstimatedMinutes,_tmpActualMinutes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getMaxOrderForTask(final int taskId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT MAX(order_index) FROM subtasks WHERE parent_task_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, taskId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
