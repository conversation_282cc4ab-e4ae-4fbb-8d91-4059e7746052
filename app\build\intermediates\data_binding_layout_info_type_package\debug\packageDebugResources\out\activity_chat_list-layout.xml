<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_chat_list" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_chat_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_chat_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="36" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="17" startOffset="8" endLine="24" endOffset="58"/></Target><Target id="@+id/tabLayout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="27" startOffset="8" endLine="34" endOffset="58"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="45" startOffset="8" endLine="51" endOffset="56"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="54" startOffset="8" endLine="63" endOffset="39"/></Target><Target id="@+id/fabNewChat" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="68" startOffset="4" endLine="75" endOffset="41"/></Target></Targets></Layout>