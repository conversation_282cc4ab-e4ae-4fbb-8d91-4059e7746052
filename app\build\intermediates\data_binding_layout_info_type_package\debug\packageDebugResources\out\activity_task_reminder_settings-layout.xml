<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_task_reminder_settings" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_task_reminder_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_task_reminder_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="340" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="62"/></Target><Target id="@+id/editTextEmail" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="58" startOffset="24" endLine="62" endOffset="66"/></Target><Target id="@+id/editTextPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="74" startOffset="24" endLine="78" endOffset="55"/></Target><Target id="@+id/switchEmailReminders" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="129" startOffset="24" endLine="133" endOffset="52"/></Target><Target id="@+id/switchWhatsappReminders" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="157" startOffset="24" endLine="161" endOffset="52"/></Target><Target id="@+id/switchLocalNotifications" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="185" startOffset="24" endLine="189" endOffset="52"/></Target><Target id="@+id/radioNoReminder" view="RadioButton"><Expressions/><location startLine="224" startOffset="24" endLine="229" endOffset="63"/></Target><Target id="@+id/radio1Day" view="RadioButton"><Expressions/><location startLine="231" startOffset="24" endLine="237" endOffset="63"/></Target><Target id="@+id/radio3Days" view="RadioButton"><Expressions/><location startLine="239" startOffset="24" endLine="244" endOffset="63"/></Target><Target id="@+id/radio1Week" view="RadioButton"><Expressions/><location startLine="246" startOffset="24" endLine="250" endOffset="58"/></Target><Target id="@+id/buttonTestWhatsapp" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="288" startOffset="24" endLine="297" endOffset="92"/></Target><Target id="@+id/buttonTestEmail" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="299" startOffset="24" endLine="308" endOffset="92"/></Target><Target id="@+id/buttonCheckApps" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="313" startOffset="20" endLine="319" endOffset="84"/></Target><Target id="@+id/buttonSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="326" startOffset="12" endLine="334" endOffset="65"/></Target></Targets></Layout>