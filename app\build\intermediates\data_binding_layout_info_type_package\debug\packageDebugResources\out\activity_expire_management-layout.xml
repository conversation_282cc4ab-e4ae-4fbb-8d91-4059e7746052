<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_expire_management" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_expire_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_expire_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="526" endOffset="53"/></Target><Target id="@+id/quickAccessCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="20" startOffset="12" endLine="76" endOffset="63"/></Target><Target id="@+id/totalProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="86" startOffset="16" endLine="130" endOffset="67"/></Target><Target id="@+id/totalProductsCount" view="TextView"><Expressions/><location startLine="111" startOffset="24" endLine="118" endOffset="65"/></Target><Target id="@+id/expiredProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="133" startOffset="16" endLine="178" endOffset="67"/></Target><Target id="@+id/expiredProductsCount" view="TextView"><Expressions/><location startLine="159" startOffset="24" endLine="166" endOffset="64"/></Target><Target id="@+id/expiringProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="181" startOffset="16" endLine="225" endOffset="67"/></Target><Target id="@+id/expiringProductsCount" view="TextView"><Expressions/><location startLine="206" startOffset="24" endLine="213" endOffset="67"/></Target><Target id="@+id/medicineCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="247" startOffset="16" endLine="289" endOffset="67"/></Target><Target id="@+id/medicineCount" view="TextView"><Expressions/><location startLine="271" startOffset="24" endLine="278" endOffset="62"/></Target><Target id="@+id/momBabyCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="292" startOffset="16" endLine="334" endOffset="67"/></Target><Target id="@+id/momBabyCount" view="TextView"><Expressions/><location startLine="316" startOffset="24" endLine="323" endOffset="62"/></Target><Target id="@+id/personalCareCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="337" startOffset="16" endLine="379" endOffset="67"/></Target><Target id="@+id/personalCareCount" view="TextView"><Expressions/><location startLine="361" startOffset="24" endLine="368" endOffset="62"/></Target><Target id="@+id/nutraceuticalCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="382" startOffset="16" endLine="424" endOffset="67"/></Target><Target id="@+id/nutraceuticalCount" view="TextView"><Expressions/><location startLine="406" startOffset="24" endLine="413" endOffset="62"/></Target><Target id="@+id/beautyCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="427" startOffset="16" endLine="469" endOffset="67"/></Target><Target id="@+id/beautyCount" view="TextView"><Expressions/><location startLine="451" startOffset="24" endLine="458" endOffset="62"/></Target><Target id="@+id/viewAllProductsButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="482" startOffset="16" endLine="493" endOffset="45"/></Target><Target id="@+id/openExpiryTrackingButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="496" startOffset="16" endLine="507" endOffset="45"/></Target><Target id="@+id/addProductFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="516" startOffset="4" endLine="524" endOffset="51"/></Target></Targets></Layout>