// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAdminDashboardStatBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCardView statCard;

  @NonNull
  public final TextView statIcon;

  @NonNull
  public final TextView statTitle;

  @NonNull
  public final TextView statValue;

  private ItemAdminDashboardStatBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialCardView statCard, @NonNull TextView statIcon, @NonNull TextView statTitle,
      @NonNull TextView statValue) {
    this.rootView = rootView;
    this.statCard = statCard;
    this.statIcon = statIcon;
    this.statTitle = statTitle;
    this.statValue = statValue;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAdminDashboardStatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAdminDashboardStatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_admin_dashboard_stat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAdminDashboardStatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialCardView statCard = (MaterialCardView) rootView;

      id = R.id.statIcon;
      TextView statIcon = ViewBindings.findChildViewById(rootView, id);
      if (statIcon == null) {
        break missingId;
      }

      id = R.id.statTitle;
      TextView statTitle = ViewBindings.findChildViewById(rootView, id);
      if (statTitle == null) {
        break missingId;
      }

      id = R.id.statValue;
      TextView statValue = ViewBindings.findChildViewById(rootView, id);
      if (statValue == null) {
        break missingId;
      }

      return new ItemAdminDashboardStatBinding((MaterialCardView) rootView, statCard, statIcon,
          statTitle, statValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
