<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/soft_lavender_background"
    tools:context=".ui.AddEditKpiActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_accent"
        android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/add_task_title"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Basic Information Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋 المعلومات الأساسية"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="16dp" />

                    <!-- Task Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskNameInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="@string/task_name_hint"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskNameEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapSentences" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Task Description -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskDescriptionInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="وصف المهمة (اختياري)"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskDescriptionEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Category Selection -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskCategoryInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="الفئة"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <AutoCompleteTextView
                            android:id="@+id/taskCategoryAutoCompleteTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Priority and Importance Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⚡ الأولوية والأهمية"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <!-- Priority -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/taskPriorityInputLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="الأولوية"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <AutoCompleteTextView
                                android:id="@+id/taskPriorityAutoCompleteTextView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Importance -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/taskImportanceInputLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="الأهمية"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <AutoCompleteTextView
                                android:id="@+id/taskImportanceAutoCompleteTextView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- Energy Level -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskEnergyLevelInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="مستوى الطاقة المطلوب"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <AutoCompleteTextView
                            android:id="@+id/taskEnergyLevelAutoCompleteTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Date and Time Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📅 التوقيت والمواعيد"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="16dp" />

                    <!-- Expiration Date -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskExpirationDateInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="@string/task_expiration_date_hint"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:endIconMode="custom"
                        app:endIconDrawable="@drawable/ic_baseline_calendar_today_24"
                        app:endIconContentDescription="Select date">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskExpirationDateEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true"
                            android:inputType="date" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Expiration Time (Optional) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskExpirationTimeInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="@string/task_expiration_time_hint"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:endIconMode="custom"
                        app:endIconDrawable="@drawable/ic_baseline_access_time_24"
                        app:endIconContentDescription="Select time">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskExpirationTimeEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true"
                            android:inputType="time" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Estimated Hours -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskEstimatedHoursInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="الوقت المتوقع (بالساعات)"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskEstimatedHoursEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Reminder (days before) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskReminderInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/task_reminder_days_hint"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <AutoCompleteTextView
                            android:id="@+id/taskReminderAutoCompleteTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Additional Options Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⚙️ خيارات إضافية"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="16dp" />

                    <!-- Tags -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskTagsInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="العلامات (مفصولة بفواصل)"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskTagsEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Notes -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskNotesInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="ملاحظات إضافية"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskNotesEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Recurring Task Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="🔄 مهمة متكررة"
                            android:textSize="16sp" />

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/switchRecurringTask"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="16dp"
                android:weightSum="2">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/cancelButton"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="إلغاء"
                    app:cornerRadius="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/addTaskButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="@string/add_task_button_text"
                    android:textColor="@color/purple_button_text"
                    android:backgroundTint="@color/purple_accent"
                    app:cornerRadius="8dp"
                    android:elevation="4dp"/>

            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
