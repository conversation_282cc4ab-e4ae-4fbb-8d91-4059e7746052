<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_product" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_product_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="127" endOffset="51"/></Target><Target id="@+id/statusIndicator" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="19" startOffset="8" endLine="26" endOffset="60"/></Target><Target id="@+id/productNameText" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="44" endOffset="42"/></Target><Target id="@+id/categoryText" view="TextView"><Expressions/><location startLine="47" startOffset="12" endLine="54" endOffset="38"/></Target><Target id="@+id/quantityText" view="TextView"><Expressions/><location startLine="63" startOffset="16" endLine="70" endOffset="44"/></Target><Target id="@+id/locationText" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="49"/></Target><Target id="@+id/expiryDateText" view="TextView"><Expressions/><location startLine="84" startOffset="12" endLine="91" endOffset="57"/></Target><Target id="@+id/notesText" view="TextView"><Expressions/><location startLine="94" startOffset="12" endLine="102" endOffset="57"/></Target><Target id="@+id/daysText" view="TextView"><Expressions/><location startLine="114" startOffset="12" endLine="121" endOffset="48"/></Target></Targets></Layout>