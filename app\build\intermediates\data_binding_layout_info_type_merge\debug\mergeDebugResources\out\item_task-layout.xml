<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_task_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="84" endOffset="51"/></Target><Target id="@+id/statusIndicatorDot" view="View"><Expressions/><location startLine="19" startOffset="8" endLine="27" endOffset="69"/></Target><Target id="@+id/tvTaskName" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="39" endOffset="44"/></Target><Target id="@+id/tvTaskExpirationDate" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="55" endOffset="64"/></Target><Target id="@+id/tvTaskStatusText" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="67" endOffset="41"/></Target><Target id="@+id/ivTaskActions" view="ImageView"><Expressions/><location startLine="70" startOffset="8" endLine="81" endOffset="48"/></Target></Targets></Layout>