// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySearchEditProgressBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Spinner dateSpinner;

  @NonNull
  public final Button deleteButton;

  @NonNull
  public final Button saveButton;

  @NonNull
  public final TextView searchPromptTextView;

  @NonNull
  public final TextInputEditText valueEditText;

  @NonNull
  public final TextInputLayout valueInputLayout;

  private ActivitySearchEditProgressBinding(@NonNull ConstraintLayout rootView,
      @NonNull Spinner dateSpinner, @NonNull Button deleteButton, @NonNull Button saveButton,
      @NonNull TextView searchPromptTextView, @NonNull TextInputEditText valueEditText,
      @NonNull TextInputLayout valueInputLayout) {
    this.rootView = rootView;
    this.dateSpinner = dateSpinner;
    this.deleteButton = deleteButton;
    this.saveButton = saveButton;
    this.searchPromptTextView = searchPromptTextView;
    this.valueEditText = valueEditText;
    this.valueInputLayout = valueInputLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySearchEditProgressBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySearchEditProgressBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_search_edit_progress, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySearchEditProgressBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.dateSpinner;
      Spinner dateSpinner = ViewBindings.findChildViewById(rootView, id);
      if (dateSpinner == null) {
        break missingId;
      }

      id = R.id.deleteButton;
      Button deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.saveButton;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.searchPromptTextView;
      TextView searchPromptTextView = ViewBindings.findChildViewById(rootView, id);
      if (searchPromptTextView == null) {
        break missingId;
      }

      id = R.id.valueEditText;
      TextInputEditText valueEditText = ViewBindings.findChildViewById(rootView, id);
      if (valueEditText == null) {
        break missingId;
      }

      id = R.id.valueInputLayout;
      TextInputLayout valueInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (valueInputLayout == null) {
        break missingId;
      }

      return new ActivitySearchEditProgressBinding((ConstraintLayout) rootView, dateSpinner,
          deleteButton, saveButton, searchPromptTextView, valueEditText, valueInputLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
