// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemConversationBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCardView conversationCard;

  @NonNull
  public final TextView lastMessage;

  @NonNull
  public final ImageView muteIcon;

  @NonNull
  public final View onlineIndicator;

  @NonNull
  public final TextView timeText;

  @NonNull
  public final TextView unreadBadge;

  @NonNull
  public final ImageView userImage;

  @NonNull
  public final TextView userName;

  private ItemConversationBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialCardView conversationCard, @NonNull TextView lastMessage,
      @NonNull ImageView muteIcon, @NonNull View onlineIndicator, @NonNull TextView timeText,
      @NonNull TextView unreadBadge, @NonNull ImageView userImage, @NonNull TextView userName) {
    this.rootView = rootView;
    this.conversationCard = conversationCard;
    this.lastMessage = lastMessage;
    this.muteIcon = muteIcon;
    this.onlineIndicator = onlineIndicator;
    this.timeText = timeText;
    this.unreadBadge = unreadBadge;
    this.userImage = userImage;
    this.userName = userName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemConversationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemConversationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_conversation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemConversationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialCardView conversationCard = (MaterialCardView) rootView;

      id = R.id.lastMessage;
      TextView lastMessage = ViewBindings.findChildViewById(rootView, id);
      if (lastMessage == null) {
        break missingId;
      }

      id = R.id.muteIcon;
      ImageView muteIcon = ViewBindings.findChildViewById(rootView, id);
      if (muteIcon == null) {
        break missingId;
      }

      id = R.id.onlineIndicator;
      View onlineIndicator = ViewBindings.findChildViewById(rootView, id);
      if (onlineIndicator == null) {
        break missingId;
      }

      id = R.id.timeText;
      TextView timeText = ViewBindings.findChildViewById(rootView, id);
      if (timeText == null) {
        break missingId;
      }

      id = R.id.unreadBadge;
      TextView unreadBadge = ViewBindings.findChildViewById(rootView, id);
      if (unreadBadge == null) {
        break missingId;
      }

      id = R.id.userImage;
      ImageView userImage = ViewBindings.findChildViewById(rootView, id);
      if (userImage == null) {
        break missingId;
      }

      id = R.id.userName;
      TextView userName = ViewBindings.findChildViewById(rootView, id);
      if (userName == null) {
        break missingId;
      }

      return new ItemConversationBinding((MaterialCardView) rootView, conversationCard, lastMessage,
          muteIcon, onlineIndicator, timeText, unreadBadge, userImage, userName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
