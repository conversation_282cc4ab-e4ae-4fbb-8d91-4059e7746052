<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_product" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_add_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/activity_add_product_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="39"/></Target><Target id="@+id/productNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="16" startOffset="8" endLine="35" endOffset="63"/></Target><Target id="@+id/productNameInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="27" startOffset="12" endLine="33" endOffset="36"/></Target><Target id="@+id/barcodeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="38" startOffset="8" endLine="58" endOffset="63"/></Target><Target id="@+id/barcodeInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="12" endLine="56" endOffset="44"/></Target><Target id="@+id/categorySpinner" view="Spinner"><Expressions/><location startLine="69" startOffset="12" endLine="75" endOffset="40"/></Target><Target id="@+id/expiryDateInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="88" startOffset="12" endLine="96" endOffset="41"/></Target><Target id="@+id/quantityInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="117" startOffset="16" endLine="123" endOffset="36"/></Target><Target id="@+id/locationInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="137" startOffset="16" endLine="143" endOffset="41"/></Target><Target id="@+id/notesInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="158" startOffset="12" endLine="165" endOffset="60"/></Target><Target id="@+id/addProductButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="177" startOffset="12" endLine="188" endOffset="65"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="191" startOffset="12" endLine="201" endOffset="80"/></Target></Targets></Layout>