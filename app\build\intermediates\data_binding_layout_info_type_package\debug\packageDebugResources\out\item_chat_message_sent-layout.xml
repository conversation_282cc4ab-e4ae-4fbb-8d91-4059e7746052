<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_chat_message_sent" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_chat_message_sent.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_chat_message_sent_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="140" endOffset="14"/></Target><Target id="@+id/dateHeader" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="35"/></Target><Target id="@+id/messageCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="35" startOffset="8" endLine="136" endOffset="59"/></Target><Target id="@+id/replyContainer" view="LinearLayout"><Expressions/><location startLine="53" startOffset="16" endLine="79" endOffset="30"/></Target><Target id="@+id/replyToText" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="68" endOffset="49"/></Target><Target id="@+id/replyMessageText" view="TextView"><Expressions/><location startLine="70" startOffset="20" endLine="77" endOffset="49"/></Target><Target id="@+id/messageText" view="TextView"><Expressions/><location startLine="82" startOffset="16" endLine="90" endOffset="53"/></Target><Target id="@+id/editedText" view="TextView"><Expressions/><location startLine="101" startOffset="20" endLine="110" endOffset="51"/></Target><Target id="@+id/timeText" view="TextView"><Expressions/><location startLine="113" startOffset="20" endLine="121" endOffset="45"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="124" startOffset="20" endLine="130" endOffset="49"/></Target></Targets></Layout>