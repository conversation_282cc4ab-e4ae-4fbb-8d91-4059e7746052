package com.example.kpitrackerapp.persistence;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.kpitrackerapp.models.EnergyLevel;
import com.example.kpitrackerapp.models.Task;
import com.example.kpitrackerapp.models.TaskImportance;
import com.example.kpitrackerapp.models.TaskPriority;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TaskDao_Impl implements TaskDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Task> __insertionAdapterOfTask;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<Task> __deletionAdapterOfTask;

  private final EntityDeletionOrUpdateAdapter<Task> __updateAdapterOfTask;

  private final SharedSQLiteStatement __preparedStmtOfUpdateTaskProgress;

  private final SharedSQLiteStatement __preparedStmtOfUpdateTaskCompletion;

  private final SharedSQLiteStatement __preparedStmtOfUpdateActualHours;

  private final SharedSQLiteStatement __preparedStmtOfArchiveTask;

  private final SharedSQLiteStatement __preparedStmtOfUnarchiveTask;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldCompletedTasks;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllTasks;

  public TaskDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTask = new EntityInsertionAdapter<Task>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `tasks` (`id`,`name`,`description`,`expirationDate`,`creationDate`,`completionDate`,`isCompleted`,`reminderDaysBefore`,`priority`,`category`,`tags`,`progress`,`estimated_hours`,`actual_hours`,`assigned_user_id`,`parent_task_id`,`attachments`,`notes`,`location`,`is_recurring`,`recurring_pattern`,`color`,`is_archived`,`energy_level`,`focus_time_minutes`,`importance`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Task entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        final Long _tmp = __converters.dateToTimestamp(entity.getExpirationDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getCreationDate());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        final Long _tmp_2 = __converters.dateToTimestamp(entity.getCompletionDate());
        if (_tmp_2 == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp_2);
        }
        final int _tmp_3 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(7, _tmp_3);
        if (entity.getReminderDaysBefore() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getReminderDaysBefore());
        }
        statement.bindString(9, __TaskPriority_enumToString(entity.getPriority()));
        if (entity.getCategory() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getCategory());
        }
        if (entity.getTags() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getTags());
        }
        statement.bindLong(12, entity.getProgress());
        if (entity.getEstimatedHours() == null) {
          statement.bindNull(13);
        } else {
          statement.bindDouble(13, entity.getEstimatedHours());
        }
        if (entity.getActualHours() == null) {
          statement.bindNull(14);
        } else {
          statement.bindDouble(14, entity.getActualHours());
        }
        if (entity.getAssignedUserId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getAssignedUserId());
        }
        if (entity.getParentTaskId() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getParentTaskId());
        }
        if (entity.getAttachments() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getAttachments());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getNotes());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getLocation());
        }
        final int _tmp_4 = entity.isRecurring() ? 1 : 0;
        statement.bindLong(20, _tmp_4);
        if (entity.getRecurringPattern() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getRecurringPattern());
        }
        if (entity.getColor() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getColor());
        }
        final int _tmp_5 = entity.isArchived() ? 1 : 0;
        statement.bindLong(23, _tmp_5);
        statement.bindString(24, __EnergyLevel_enumToString(entity.getEnergyLevel()));
        if (entity.getFocusTimeMinutes() == null) {
          statement.bindNull(25);
        } else {
          statement.bindLong(25, entity.getFocusTimeMinutes());
        }
        statement.bindString(26, __TaskImportance_enumToString(entity.getImportance()));
      }
    };
    this.__deletionAdapterOfTask = new EntityDeletionOrUpdateAdapter<Task>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `tasks` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Task entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfTask = new EntityDeletionOrUpdateAdapter<Task>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `tasks` SET `id` = ?,`name` = ?,`description` = ?,`expirationDate` = ?,`creationDate` = ?,`completionDate` = ?,`isCompleted` = ?,`reminderDaysBefore` = ?,`priority` = ?,`category` = ?,`tags` = ?,`progress` = ?,`estimated_hours` = ?,`actual_hours` = ?,`assigned_user_id` = ?,`parent_task_id` = ?,`attachments` = ?,`notes` = ?,`location` = ?,`is_recurring` = ?,`recurring_pattern` = ?,`color` = ?,`is_archived` = ?,`energy_level` = ?,`focus_time_minutes` = ?,`importance` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Task entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        final Long _tmp = __converters.dateToTimestamp(entity.getExpirationDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getCreationDate());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        final Long _tmp_2 = __converters.dateToTimestamp(entity.getCompletionDate());
        if (_tmp_2 == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp_2);
        }
        final int _tmp_3 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(7, _tmp_3);
        if (entity.getReminderDaysBefore() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getReminderDaysBefore());
        }
        statement.bindString(9, __TaskPriority_enumToString(entity.getPriority()));
        if (entity.getCategory() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getCategory());
        }
        if (entity.getTags() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getTags());
        }
        statement.bindLong(12, entity.getProgress());
        if (entity.getEstimatedHours() == null) {
          statement.bindNull(13);
        } else {
          statement.bindDouble(13, entity.getEstimatedHours());
        }
        if (entity.getActualHours() == null) {
          statement.bindNull(14);
        } else {
          statement.bindDouble(14, entity.getActualHours());
        }
        if (entity.getAssignedUserId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getAssignedUserId());
        }
        if (entity.getParentTaskId() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getParentTaskId());
        }
        if (entity.getAttachments() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getAttachments());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getNotes());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getLocation());
        }
        final int _tmp_4 = entity.isRecurring() ? 1 : 0;
        statement.bindLong(20, _tmp_4);
        if (entity.getRecurringPattern() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getRecurringPattern());
        }
        if (entity.getColor() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getColor());
        }
        final int _tmp_5 = entity.isArchived() ? 1 : 0;
        statement.bindLong(23, _tmp_5);
        statement.bindString(24, __EnergyLevel_enumToString(entity.getEnergyLevel()));
        if (entity.getFocusTimeMinutes() == null) {
          statement.bindNull(25);
        } else {
          statement.bindLong(25, entity.getFocusTimeMinutes());
        }
        statement.bindString(26, __TaskImportance_enumToString(entity.getImportance()));
        statement.bindLong(27, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateTaskProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE tasks SET progress = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateTaskCompletion = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE tasks SET isCompleted = ?, completionDate = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateActualHours = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE tasks SET actual_hours = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfArchiveTask = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE tasks SET is_archived = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUnarchiveTask = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE tasks SET is_archived = 0 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldCompletedTasks = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM tasks WHERE isCompleted = 1 AND completionDate < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllTasks = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM tasks";
        return _query;
      }
    };
  }

  @Override
  public Object insertTask(final Task task, final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTask.insertAndReturnId(task);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insert(final Task task, final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTask.insertAndReturnId(task);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTask(final Task task, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTask.handle(task);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final Task task, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTask.handle(task);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTask(final Task task, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTask.handle(task);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object update(final Task task, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTask.handle(task);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTaskProgress(final int taskId, final int progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateTaskProgress.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, progress);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, taskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateTaskProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTaskCompletion(final int taskId, final boolean isCompleted,
      final Date completionDate, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateTaskCompletion.acquire();
        int _argIndex = 1;
        final int _tmp = isCompleted ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        final Long _tmp_1 = __converters.dateToTimestamp(completionDate);
        if (_tmp_1 == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp_1);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, taskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateTaskCompletion.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateActualHours(final int taskId, final double actualHours,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateActualHours.acquire();
        int _argIndex = 1;
        _stmt.bindDouble(_argIndex, actualHours);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, taskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateActualHours.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object archiveTask(final int taskId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfArchiveTask.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, taskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfArchiveTask.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object unarchiveTask(final int taskId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUnarchiveTask.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, taskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUnarchiveTask.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldCompletedTasks(final Date cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldCompletedTasks.acquire();
        int _argIndex = 1;
        final Long _tmp = __converters.dateToTimestamp(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldCompletedTasks.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllTasks(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllTasks.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllTasks.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<Task> getTaskById(final int taskId) {
    final String _sql = "SELECT * FROM tasks WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, taskId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<Task>() {
      @Override
      @Nullable
      public Task call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final Task _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _result = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getAllTasks() {
    final String _sql = "SELECT * FROM tasks WHERE is_archived = 0 ORDER BY priority DESC, expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getTasksDueAfter(final Date date) {
    final String _sql = "SELECT * FROM tasks WHERE expirationDate >= ? AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_2 = __converters.fromTimestamp(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_2;
            }
            final Date _tmpCreationDate;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_4 = __converters.fromTimestamp(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_4;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_5);
            final boolean _tmpIsCompleted;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_6 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_7 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_8 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> searchTasksByName(final String query) {
    final String _sql = "SELECT * FROM tasks WHERE name LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%' AND is_archived = 0 ORDER BY priority DESC, expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getActiveTasks() {
    final String _sql = "SELECT * FROM tasks WHERE isCompleted = 0 AND is_archived = 0 ORDER BY priority DESC, expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getCompletedTasks() {
    final String _sql = "SELECT * FROM tasks WHERE isCompleted = 1 AND is_archived = 0 ORDER BY completionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getOverdueTasks(final Date currentDate) {
    final String _sql = "SELECT * FROM tasks WHERE expirationDate < ? AND isCompleted = 0 AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(currentDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_2 = __converters.fromTimestamp(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_2;
            }
            final Date _tmpCreationDate;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_4 = __converters.fromTimestamp(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_4;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_5);
            final boolean _tmpIsCompleted;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_6 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_7 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_8 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getTasksByPriority(final TaskPriority priority) {
    final String _sql = "SELECT * FROM tasks WHERE priority = ? AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, __TaskPriority_enumToString(priority));
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getTasksByCategory(final String category) {
    final String _sql = "SELECT * FROM tasks WHERE category = ? AND is_archived = 0 ORDER BY priority DESC, expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getTasksByUser(final String userId) {
    final String _sql = "SELECT * FROM tasks WHERE assigned_user_id = ? AND is_archived = 0 ORDER BY priority DESC, expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getMainTasks() {
    final String _sql = "SELECT * FROM tasks WHERE parent_task_id IS NULL AND is_archived = 0 ORDER BY priority DESC, expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getSubtasks(final int parentId) {
    final String _sql = "SELECT * FROM tasks WHERE parent_task_id = ? ORDER BY creationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, parentId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getUrgentImportantTasks() {
    final String _sql = "SELECT * FROM tasks WHERE priority IN ('HIGH', 'URGENT') AND importance IN ('HIGH', 'CRITICAL') AND isCompleted = 0 AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getNotUrgentImportantTasks() {
    final String _sql = "SELECT * FROM tasks WHERE priority IN ('LOW', 'MEDIUM') AND importance IN ('HIGH', 'CRITICAL') AND isCompleted = 0 AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getUrgentNotImportantTasks() {
    final String _sql = "SELECT * FROM tasks WHERE priority IN ('HIGH', 'URGENT') AND importance IN ('LOW', 'MEDIUM') AND isCompleted = 0 AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getNotUrgentNotImportantTasks() {
    final String _sql = "SELECT * FROM tasks WHERE priority IN ('LOW', 'MEDIUM') AND importance IN ('LOW', 'MEDIUM') AND isCompleted = 0 AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getTotalTasksCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM tasks WHERE is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedTasksCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM tasks WHERE isCompleted = 1 AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getOverdueTasksCount(final Date currentDate,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM tasks WHERE expirationDate < ? AND isCompleted = 0 AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(currentDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(0);
            _result = _tmp_1;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTasksDueToday(final Date date, final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM tasks WHERE DATE(expirationDate) = DATE(?) AND isCompleted = 0 AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(0);
            _result = _tmp_1;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUrgentTasksCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM tasks WHERE priority = 'URGENT' AND isCompleted = 0 AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageCompletionTime(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(JULIANDAY(completionDate) - JULIANDAY(creationDate)) FROM tasks WHERE isCompleted = 1 AND completionDate IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCategoryBreakdown(final Continuation<? super List<CategoryCount>> $completion) {
    final String _sql = "SELECT category, COUNT(*) as count FROM tasks WHERE is_archived = 0 GROUP BY category";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CategoryCount>>() {
      @Override
      @NonNull
      public List<CategoryCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCategory = 0;
          final int _cursorIndexOfCount = 1;
          final List<CategoryCount> _result = new ArrayList<CategoryCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CategoryCount _item;
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new CategoryCount(_tmpCategory,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPriorityBreakdown(final Continuation<? super List<PriorityCount>> $completion) {
    final String _sql = "SELECT priority, COUNT(*) as count FROM tasks WHERE is_archived = 0 GROUP BY priority";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PriorityCount>>() {
      @Override
      @NonNull
      public List<PriorityCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPriority = 0;
          final int _cursorIndexOfCount = 1;
          final List<PriorityCount> _result = new ArrayList<PriorityCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PriorityCount _item;
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new PriorityCount(_tmpPriority,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Task>> getTasksForDate(final Date date) {
    final String _sql = "SELECT * FROM tasks WHERE DATE(expirationDate) = DATE(?) AND is_archived = 0 ORDER BY priority DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_2 = __converters.fromTimestamp(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_2;
            }
            final Date _tmpCreationDate;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_4 = __converters.fromTimestamp(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_4;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_5);
            final boolean _tmpIsCompleted;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_6 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_7 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_8 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getTasksInDateRange(final Date startDate, final Date endDate) {
    final String _sql = "SELECT * FROM tasks WHERE expirationDate BETWEEN ? AND ? AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_3;
            }
            final Date _tmpCreationDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_5 = __converters.fromTimestamp(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_5;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_6);
            final boolean _tmpIsCompleted;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_7 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_8 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_9 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getRecurringTasksFlow() {
    final String _sql = "SELECT * FROM tasks WHERE is_recurring = 1 AND is_archived = 0 ORDER BY expirationDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getArchivedTasks() {
    final String _sql = "SELECT * FROM tasks WHERE is_archived = 1 ORDER BY completionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Task>> getTasksByEnergyLevel(final EnergyLevel energyLevel) {
    final String _sql = "SELECT * FROM tasks WHERE energy_level = ? AND isCompleted = 0 AND is_archived = 0 ORDER BY priority DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, __EnergyLevel_enumToString(energyLevel));
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasks"}, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getTotalFocusTimeForDate(final Date date,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT SUM(focus_time_minutes) FROM tasks WHERE isCompleted = 1 AND DATE(completionDate) = DATE(?)";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp_1;
            if (_cursor.isNull(0)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(0);
            }
            _result = _tmp_1;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageFocusTime(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(focus_time_minutes) FROM tasks WHERE isCompleted = 1 AND focus_time_minutes IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecurringTasks(final Continuation<? super List<Task>> $completion) {
    final String _sql = "SELECT * FROM tasks WHERE is_recurring = 1 AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTaskByNameAndDate(final String name, final Date date,
      final Continuation<? super Task> $completion) {
    final String _sql = "SELECT * FROM tasks WHERE name = ? AND DATE(expirationDate) = DATE(?)";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, name);
    _argIndex = 2;
    final Long _tmp = __converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Task>() {
      @Override
      @Nullable
      public Task call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final Task _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_2 = __converters.fromTimestamp(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_2;
            }
            final Date _tmpCreationDate;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_4 = __converters.fromTimestamp(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_4;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_5);
            final boolean _tmpIsCompleted;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_6 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_7 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_8 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _result = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedRecurringTasks(final Continuation<? super List<Task>> $completion) {
    final String _sql = "SELECT * FROM tasks WHERE is_recurring = 1 AND isCompleted = 1 AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_1 = __converters.fromTimestamp(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_4);
            final boolean _tmpIsCompleted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_5 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_6 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_7 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUpcomingRecurringTasks(final Date currentDate,
      final Continuation<? super List<Task>> $completion) {
    final String _sql = "SELECT * FROM tasks WHERE is_recurring = 1 AND isCompleted = 0 AND expirationDate > ? AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(currentDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_2 = __converters.fromTimestamp(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_2;
            }
            final Date _tmpCreationDate;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_4 = __converters.fromTimestamp(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_4;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_5);
            final boolean _tmpIsCompleted;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_6 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_7 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_8 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getFutureTasksByPattern(final String name, final String pattern,
      final Date currentDate, final Continuation<? super List<Task>> $completion) {
    final String _sql = "SELECT * FROM tasks WHERE name = ? AND recurring_pattern = ? AND expirationDate > ? AND is_archived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, name);
    _argIndex = 2;
    _statement.bindString(_argIndex, pattern);
    _argIndex = 3;
    final Long _tmp = __converters.dateToTimestamp(currentDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Task>>() {
      @Override
      @NonNull
      public List<Task> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExpirationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "expirationDate");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creationDate");
          final int _cursorIndexOfCompletionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completionDate");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfReminderDaysBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderDaysBefore");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfActualHours = CursorUtil.getColumnIndexOrThrow(_cursor, "actual_hours");
          final int _cursorIndexOfAssignedUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "assigned_user_id");
          final int _cursorIndexOfParentTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "parent_task_id");
          final int _cursorIndexOfAttachments = CursorUtil.getColumnIndexOrThrow(_cursor, "attachments");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "is_recurring");
          final int _cursorIndexOfRecurringPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "recurring_pattern");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "is_archived");
          final int _cursorIndexOfEnergyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "energy_level");
          final int _cursorIndexOfFocusTimeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "focus_time_minutes");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final List<Task> _result = new ArrayList<Task>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Task _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final Date _tmpExpirationDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfExpirationDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfExpirationDate);
            }
            final Date _tmp_2 = __converters.fromTimestamp(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpExpirationDate = _tmp_2;
            }
            final Date _tmpCreationDate;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_4 = __converters.fromTimestamp(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_4;
            }
            final Date _tmpCompletionDate;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletionDate)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletionDate);
            }
            _tmpCompletionDate = __converters.fromTimestamp(_tmp_5);
            final boolean _tmpIsCompleted;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_6 != 0;
            final Integer _tmpReminderDaysBefore;
            if (_cursor.isNull(_cursorIndexOfReminderDaysBefore)) {
              _tmpReminderDaysBefore = null;
            } else {
              _tmpReminderDaysBefore = _cursor.getInt(_cursorIndexOfReminderDaysBefore);
            }
            final TaskPriority _tmpPriority;
            _tmpPriority = __TaskPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final Double _tmpEstimatedHours;
            if (_cursor.isNull(_cursorIndexOfEstimatedHours)) {
              _tmpEstimatedHours = null;
            } else {
              _tmpEstimatedHours = _cursor.getDouble(_cursorIndexOfEstimatedHours);
            }
            final Double _tmpActualHours;
            if (_cursor.isNull(_cursorIndexOfActualHours)) {
              _tmpActualHours = null;
            } else {
              _tmpActualHours = _cursor.getDouble(_cursorIndexOfActualHours);
            }
            final String _tmpAssignedUserId;
            if (_cursor.isNull(_cursorIndexOfAssignedUserId)) {
              _tmpAssignedUserId = null;
            } else {
              _tmpAssignedUserId = _cursor.getString(_cursorIndexOfAssignedUserId);
            }
            final Integer _tmpParentTaskId;
            if (_cursor.isNull(_cursorIndexOfParentTaskId)) {
              _tmpParentTaskId = null;
            } else {
              _tmpParentTaskId = _cursor.getInt(_cursorIndexOfParentTaskId);
            }
            final String _tmpAttachments;
            if (_cursor.isNull(_cursorIndexOfAttachments)) {
              _tmpAttachments = null;
            } else {
              _tmpAttachments = _cursor.getString(_cursorIndexOfAttachments);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_7 != 0;
            final String _tmpRecurringPattern;
            if (_cursor.isNull(_cursorIndexOfRecurringPattern)) {
              _tmpRecurringPattern = null;
            } else {
              _tmpRecurringPattern = _cursor.getString(_cursorIndexOfRecurringPattern);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsArchived;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_8 != 0;
            final EnergyLevel _tmpEnergyLevel;
            _tmpEnergyLevel = __EnergyLevel_stringToEnum(_cursor.getString(_cursorIndexOfEnergyLevel));
            final Integer _tmpFocusTimeMinutes;
            if (_cursor.isNull(_cursorIndexOfFocusTimeMinutes)) {
              _tmpFocusTimeMinutes = null;
            } else {
              _tmpFocusTimeMinutes = _cursor.getInt(_cursorIndexOfFocusTimeMinutes);
            }
            final TaskImportance _tmpImportance;
            _tmpImportance = __TaskImportance_stringToEnum(_cursor.getString(_cursorIndexOfImportance));
            _item = new Task(_tmpId,_tmpName,_tmpDescription,_tmpExpirationDate,_tmpCreationDate,_tmpCompletionDate,_tmpIsCompleted,_tmpReminderDaysBefore,_tmpPriority,_tmpCategory,_tmpTags,_tmpProgress,_tmpEstimatedHours,_tmpActualHours,_tmpAssignedUserId,_tmpParentTaskId,_tmpAttachments,_tmpNotes,_tmpLocation,_tmpIsRecurring,_tmpRecurringPattern,_tmpColor,_tmpIsArchived,_tmpEnergyLevel,_tmpFocusTimeMinutes,_tmpImportance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private String __TaskPriority_enumToString(@NonNull final TaskPriority _value) {
    switch (_value) {
      case LOW: return "LOW";
      case MEDIUM: return "MEDIUM";
      case HIGH: return "HIGH";
      case URGENT: return "URGENT";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private String __EnergyLevel_enumToString(@NonNull final EnergyLevel _value) {
    switch (_value) {
      case LOW: return "LOW";
      case MEDIUM: return "MEDIUM";
      case HIGH: return "HIGH";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private String __TaskImportance_enumToString(@NonNull final TaskImportance _value) {
    switch (_value) {
      case LOW: return "LOW";
      case MEDIUM: return "MEDIUM";
      case HIGH: return "HIGH";
      case CRITICAL: return "CRITICAL";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private TaskPriority __TaskPriority_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "LOW": return TaskPriority.LOW;
      case "MEDIUM": return TaskPriority.MEDIUM;
      case "HIGH": return TaskPriority.HIGH;
      case "URGENT": return TaskPriority.URGENT;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private EnergyLevel __EnergyLevel_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "LOW": return EnergyLevel.LOW;
      case "MEDIUM": return EnergyLevel.MEDIUM;
      case "HIGH": return EnergyLevel.HIGH;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private TaskImportance __TaskImportance_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "LOW": return TaskImportance.LOW;
      case "MEDIUM": return TaskImportance.MEDIUM;
      case "HIGH": return TaskImportance.HIGH;
      case "CRITICAL": return TaskImportance.CRITICAL;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }
}
