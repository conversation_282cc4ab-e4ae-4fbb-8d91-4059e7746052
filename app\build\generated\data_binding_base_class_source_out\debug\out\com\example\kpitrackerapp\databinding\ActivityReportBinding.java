// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.LineChart;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityReportBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final CompactReportTableBinding compactReportTable;

  @NonNull
  public final TextInputEditText endDateEditText;

  @NonNull
  public final TextInputLayout endDateInputLayout;

  @NonNull
  public final MaterialButton goButton;

  @NonNull
  public final TextView kpiNameLabel;

  @NonNull
  public final AutoCompleteTextView kpiSelectorAutoCompleteTextView;

  @NonNull
  public final TextInputLayout kpiSelectorLayout;

  @NonNull
  public final RadioButton radioAnnual;

  @NonNull
  public final RadioButton radioDaily;

  @NonNull
  public final RadioButton radioMonthly;

  @NonNull
  public final MaterialCardView reportTableCard;

  @NonNull
  public final TextInputEditText startDateEditText;

  @NonNull
  public final TextInputLayout startDateInputLayout;

  @NonNull
  public final BarChart summaryBarChart;

  @NonNull
  public final RadioGroup summaryChartFilterRadioGroup;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final LineChart trendChart;

  private ActivityReportBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull CompactReportTableBinding compactReportTable,
      @NonNull TextInputEditText endDateEditText, @NonNull TextInputLayout endDateInputLayout,
      @NonNull MaterialButton goButton, @NonNull TextView kpiNameLabel,
      @NonNull AutoCompleteTextView kpiSelectorAutoCompleteTextView,
      @NonNull TextInputLayout kpiSelectorLayout, @NonNull RadioButton radioAnnual,
      @NonNull RadioButton radioDaily, @NonNull RadioButton radioMonthly,
      @NonNull MaterialCardView reportTableCard, @NonNull TextInputEditText startDateEditText,
      @NonNull TextInputLayout startDateInputLayout, @NonNull BarChart summaryBarChart,
      @NonNull RadioGroup summaryChartFilterRadioGroup, @NonNull MaterialToolbar toolbar,
      @NonNull LineChart trendChart) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.compactReportTable = compactReportTable;
    this.endDateEditText = endDateEditText;
    this.endDateInputLayout = endDateInputLayout;
    this.goButton = goButton;
    this.kpiNameLabel = kpiNameLabel;
    this.kpiSelectorAutoCompleteTextView = kpiSelectorAutoCompleteTextView;
    this.kpiSelectorLayout = kpiSelectorLayout;
    this.radioAnnual = radioAnnual;
    this.radioDaily = radioDaily;
    this.radioMonthly = radioMonthly;
    this.reportTableCard = reportTableCard;
    this.startDateEditText = startDateEditText;
    this.startDateInputLayout = startDateInputLayout;
    this.summaryBarChart = summaryBarChart;
    this.summaryChartFilterRadioGroup = summaryChartFilterRadioGroup;
    this.toolbar = toolbar;
    this.trendChart = trendChart;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityReportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityReportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_report, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityReportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.compactReportTable;
      View compactReportTable = ViewBindings.findChildViewById(rootView, id);
      if (compactReportTable == null) {
        break missingId;
      }
      CompactReportTableBinding binding_compactReportTable = CompactReportTableBinding.bind(compactReportTable);

      id = R.id.endDateEditText;
      TextInputEditText endDateEditText = ViewBindings.findChildViewById(rootView, id);
      if (endDateEditText == null) {
        break missingId;
      }

      id = R.id.endDateInputLayout;
      TextInputLayout endDateInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (endDateInputLayout == null) {
        break missingId;
      }

      id = R.id.goButton;
      MaterialButton goButton = ViewBindings.findChildViewById(rootView, id);
      if (goButton == null) {
        break missingId;
      }

      id = R.id.kpiNameLabel;
      TextView kpiNameLabel = ViewBindings.findChildViewById(rootView, id);
      if (kpiNameLabel == null) {
        break missingId;
      }

      id = R.id.kpiSelectorAutoCompleteTextView;
      AutoCompleteTextView kpiSelectorAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiSelectorAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.kpiSelectorLayout;
      TextInputLayout kpiSelectorLayout = ViewBindings.findChildViewById(rootView, id);
      if (kpiSelectorLayout == null) {
        break missingId;
      }

      id = R.id.radioAnnual;
      RadioButton radioAnnual = ViewBindings.findChildViewById(rootView, id);
      if (radioAnnual == null) {
        break missingId;
      }

      id = R.id.radioDaily;
      RadioButton radioDaily = ViewBindings.findChildViewById(rootView, id);
      if (radioDaily == null) {
        break missingId;
      }

      id = R.id.radioMonthly;
      RadioButton radioMonthly = ViewBindings.findChildViewById(rootView, id);
      if (radioMonthly == null) {
        break missingId;
      }

      id = R.id.reportTableCard;
      MaterialCardView reportTableCard = ViewBindings.findChildViewById(rootView, id);
      if (reportTableCard == null) {
        break missingId;
      }

      id = R.id.startDateEditText;
      TextInputEditText startDateEditText = ViewBindings.findChildViewById(rootView, id);
      if (startDateEditText == null) {
        break missingId;
      }

      id = R.id.startDateInputLayout;
      TextInputLayout startDateInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (startDateInputLayout == null) {
        break missingId;
      }

      id = R.id.summaryBarChart;
      BarChart summaryBarChart = ViewBindings.findChildViewById(rootView, id);
      if (summaryBarChart == null) {
        break missingId;
      }

      id = R.id.summaryChartFilterRadioGroup;
      RadioGroup summaryChartFilterRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (summaryChartFilterRadioGroup == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.trendChart;
      LineChart trendChart = ViewBindings.findChildViewById(rootView, id);
      if (trendChart == null) {
        break missingId;
      }

      return new ActivityReportBinding((CoordinatorLayout) rootView, appBarLayout,
          binding_compactReportTable, endDateEditText, endDateInputLayout, goButton, kpiNameLabel,
          kpiSelectorAutoCompleteTextView, kpiSelectorLayout, radioAnnual, radioDaily, radioMonthly,
          reportTableCard, startDateEditText, startDateInputLayout, summaryBarChart,
          summaryChartFilterRadioGroup, toolbar, trendChart);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
