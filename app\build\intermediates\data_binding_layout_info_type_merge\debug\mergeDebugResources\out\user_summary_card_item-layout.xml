<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="user_summary_card_item" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\user_summary_card_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/userSummaryCardView"><Targets><Target id="@+id/userSummaryCardView" tag="layout/user_summary_card_item_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="51"/></Target><Target id="@+id/topBackgroundView" view="View"><Expressions/><location startLine="23" startOffset="8" endLine="31" endOffset="78"/></Target><Target id="@+id/topContentContainer" view="LinearLayout"><Expressions/><location startLine="34" startOffset="8" endLine="79" endOffset="22"/></Target><Target id="@+id/kpiDetailsContainer" view="LinearLayout"><Expressions/><location startLine="46" startOffset="12" endLine="51" endOffset="47"/></Target><Target id="@+id/expiryDateContainer" view="LinearLayout"><Expressions/><location startLine="54" startOffset="12" endLine="77" endOffset="26"/></Target><Target id="@+id/calendarIcon" view="ImageView"><Expressions/><location startLine="62" startOffset="16" endLine="67" endOffset="53"/></Target><Target id="@+id/expiryDateTextView" view="TextView"><Expressions/><location startLine="69" startOffset="16" endLine="76" endOffset="45"/></Target><Target id="@+id/bottomBackgroundView" view="View"><Expressions/><location startLine="82" startOffset="8" endLine="90" endOffset="76"/></Target><Target id="@+id/ownerImageView" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="93" startOffset="8" endLine="109" endOffset="61"/></Target><Target id="@+id/ownerNameTextView" view="TextView"><Expressions/><location startLine="112" startOffset="8" endLine="125" endOffset="35"/></Target><Target id="@+id/dragHandleImageView" view="ImageView"><Expressions/><location startLine="128" startOffset="8" endLine="141" endOffset="58"/></Target></Targets></Layout>