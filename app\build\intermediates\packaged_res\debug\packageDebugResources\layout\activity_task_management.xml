<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/soft_lavender_background"
    tools:context=".ui.TaskManagementActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayoutTaskManagement"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_accent"
        android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbarTaskManagement"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/my_tasks_title"
            app:titleTextColor="@color/white">

            <!-- Search and action buttons in toolbar -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end"
                android:layout_marginEnd="16dp">

                <ImageButton
                    android:id="@+id/btnSearch"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_baseline_search_24"
                    android:contentDescription="Search tasks"
                    app:tint="@color/white" />

                <ImageButton
                    android:id="@+id/btnFilter"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_baseline_filter_list_24"
                    android:contentDescription="Filter tasks"
                    app:tint="@color/white" />

                <ImageButton
                    android:id="@+id/btnViewMode"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_baseline_view_list_24"
                    android:contentDescription="Change view mode"
                    app:tint="@color/white" />

            </LinearLayout>

        </com.google.android.material.appbar.MaterialToolbar>

        <!-- Search View (initially hidden) -->
        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="@drawable/search_background"
            android:visibility="gone"
            app:queryHint="ابحث في المهام..."
            app:iconifiedByDefault="false" />

        <!-- Filter Chips -->
        <HorizontalScrollView
            android:id="@+id/scrollViewFilters"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:scrollbars="none">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chipGroupFilters"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:singleSelection="false"
                app:chipSpacingHorizontal="8dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipAll"
                    style="@style/Widget.App.Chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الكل"
                    android:checked="true" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipToday"
                    style="@style/Widget.App.Chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="اليوم" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipThisWeek"
                    style="@style/Widget.App.Chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="هذا الأسبوع" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipUrgent"
                    style="@style/Widget.App.Chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="عاجل" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipCompleted"
                    style="@style/Widget.App.Chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مكتملة" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipOverdue"
                    style="@style/Widget.App.Chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="متأخرة" />

            </com.google.android.material.chip.ChipGroup>
        </HorizontalScrollView>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Quick Stats Dashboard -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardQuickStats"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="12dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📊 ملخص سريع"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="4">

                        <!-- Total Tasks -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tvTotalTasks"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="12"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/purple_accent" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إجمالي المهام"
                                android:textSize="12sp"
                                android:textColor="@color/text_secondary"
                                android:gravity="center" />

                        </LinearLayout>

                        <!-- Today Tasks -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tvTodayTasks"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/today_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="مستحقة اليوم"
                                android:textSize="12sp"
                                android:textColor="@color/text_secondary"
                                android:gravity="center" />

                        </LinearLayout>

                        <!-- Urgent Tasks -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tvUrgentTasks"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="2"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/urgent_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="عاجلة"
                                android:textSize="12sp"
                                android:textColor="@color/text_secondary"
                                android:gravity="center" />

                        </LinearLayout>

                        <!-- Completion Rate -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tvCompletionRate"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="85%"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/success_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="معدل الإنجاز"
                                android:textSize="12sp"
                                android:textColor="@color/text_secondary"
                                android:gravity="center" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Progress Bar -->
                    <ProgressBar
                        android:id="@+id/progressOverall"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginTop="12dp"
                        android:progress="85"
                        android:progressTint="@color/success_color"
                        android:progressBackgroundTint="@color/progress_background" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Eisenhower Matrix Toggle -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardMatrixToggle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="8dp"
                app:cardBackgroundColor="@color/matrix_toggle_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🎯 عرض مصفوفة أيزنهاور"
                        android:textStyle="bold"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switchMatrixView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Eisenhower Matrix (initially hidden) -->
            <LinearLayout
                android:id="@+id/layoutEisenhowerMatrix"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_marginBottom="16dp">

                <!-- Matrix Grid -->
                <GridLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:columnCount="2"
                    android:rowCount="2"
                    android:layout_margin="4dp">

                    <!-- Urgent & Important -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:layout_margin="4dp"
                        app:cardElevation="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardBackgroundColor="@color/urgent_important_bg">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="🔥 عاجل ومهم"
                                android:textStyle="bold"
                                android:textSize="12sp"
                                android:textColor="@color/white"
                                android:gravity="center" />

                            <TextView
                                android:id="@+id/tvUrgentImportantCount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="0 مهام"
                                android:textSize="10sp"
                                android:textColor="@color/white"
                                android:gravity="center"
                                android:layout_marginTop="2dp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rvUrgentImportant"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Not Urgent & Important -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:layout_margin="4dp"
                        app:cardElevation="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardBackgroundColor="@color/not_urgent_important_bg">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="🎯 مهم وغير عاجل"
                                android:textStyle="bold"
                                android:textSize="12sp"
                                android:textColor="@color/white"
                                android:gravity="center" />

                            <TextView
                                android:id="@+id/tvNotUrgentImportantCount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="0 مهام"
                                android:textSize="10sp"
                                android:textColor="@color/white"
                                android:gravity="center"
                                android:layout_marginTop="2dp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rvNotUrgentImportant"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Urgent & Not Important -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:layout_margin="4dp"
                        app:cardElevation="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardBackgroundColor="@color/urgent_not_important_bg">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="⚡ عاجل وغير مهم"
                                android:textStyle="bold"
                                android:textSize="12sp"
                                android:textColor="@color/white"
                                android:gravity="center" />

                            <TextView
                                android:id="@+id/tvUrgentNotImportantCount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="0 مهام"
                                android:textSize="10sp"
                                android:textColor="@color/white"
                                android:gravity="center"
                                android:layout_marginTop="2dp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rvUrgentNotImportant"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Not Urgent & Not Important -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:layout_margin="4dp"
                        app:cardElevation="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardBackgroundColor="@color/not_urgent_not_important_bg">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="📝 غير مهم وغير عاجل"
                                android:textStyle="bold"
                                android:textSize="12sp"
                                android:textColor="@color/white"
                                android:gravity="center" />

                            <TextView
                                android:id="@+id/tvNotUrgentNotImportantCount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="0 مهام"
                                android:textSize="10sp"
                                android:textColor="@color/white"
                                android:gravity="center"
                                android:layout_marginTop="2dp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rvNotUrgentNotImportant"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </GridLayout>

            </LinearLayout>

            <!-- Tasks List Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="📋 قائمة المهام"
                    android:textStyle="bold"
                    android:textSize="18sp" />

                <ImageButton
                    android:id="@+id/btnSortTasks"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_baseline_sort_24"
                    android:contentDescription="Sort tasks" />

            </LinearLayout>

            <!-- Tasks RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvTasks"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_task_enhanced" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layoutEmptyState"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝"
                    android:textSize="48sp"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="لا توجد مهام"
                    android:textStyle="bold"
                    android:textSize="18sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ابدأ بإضافة مهمة جديدة"
                    android:textColor="@color/text_secondary"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Enhanced FAB -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabAddTask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:text="إضافة مهمة"
        android:contentDescription="@string/add_task_title"
        app:icon="@drawable/ic_baseline_add_24"
        android:backgroundTint="@color/purple_accent"
        app:iconTint="@color/white"
        android:textColor="@color/white"
        app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
