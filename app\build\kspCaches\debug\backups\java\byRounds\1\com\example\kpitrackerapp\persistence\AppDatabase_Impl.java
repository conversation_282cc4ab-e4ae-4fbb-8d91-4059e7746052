package com.example.kpitrackerapp.persistence;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile KpiDao _kpiDao;

  private volatile KpiProgressEntryDao _kpiProgressEntryDao;

  private volatile UserDao _userDao;

  private volatile UserKpiAssignmentDao _userKpiAssignmentDao;

  private volatile TaskDao _taskDao;

  private volatile TaskCategoryDao _taskCategoryDao;

  private volatile SubtaskDao _subtaskDao;

  private volatile ChatDao _chatDao;

  private volatile ProductDao _productDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(36) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `kpis` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `annual_target` REAL NOT NULL, `quarterly_target` REAL, `monthly_target` REAL, `daily_target` REAL, `unit` TEXT NOT NULL, `creation_date` INTEGER NOT NULL, `owner_type` TEXT, `owner_name` TEXT, `owner_image_uri` TEXT, `card_color_hex` TEXT, `bottom_card_color_hex` TEXT, `master_kpi_id` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `kpi_progress_entries` (`id` TEXT NOT NULL, `kpi_id` TEXT NOT NULL, `user_id` TEXT NOT NULL, `entry_date` INTEGER NOT NULL, `value` REAL NOT NULL, `user_identifier` TEXT, PRIMARY KEY(`id`), FOREIGN KEY(`kpi_id`) REFERENCES `kpis`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`user_id`) REFERENCES `users`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_kpi_progress_entries_kpi_id` ON `kpi_progress_entries` (`kpi_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_kpi_progress_entries_user_id` ON `kpi_progress_entries` (`user_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_kpi_progress_entries_entry_date` ON `kpi_progress_entries` (`entry_date`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `users` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `imagePath` TEXT, `topColor` INTEGER, `bottomColor` INTEGER, `email` TEXT, `phone` TEXT, `username` TEXT, `department` TEXT, `isActive` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `lastLoginAt` INTEGER, `role` TEXT NOT NULL, `permissions` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_kpi_assignments` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `user_id` TEXT NOT NULL, `kpi_id` TEXT NOT NULL, FOREIGN KEY(`user_id`) REFERENCES `users`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`kpi_id`) REFERENCES `kpis`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_user_kpi_assignments_user_id` ON `user_kpi_assignments` (`user_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_user_kpi_assignments_kpi_id` ON `user_kpi_assignments` (`kpi_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `tasks` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `expirationDate` INTEGER NOT NULL, `creationDate` INTEGER NOT NULL, `completionDate` INTEGER, `isCompleted` INTEGER NOT NULL, `reminderDaysBefore` INTEGER, `priority` TEXT NOT NULL, `category` TEXT, `tags` TEXT, `progress` INTEGER NOT NULL, `estimated_hours` REAL, `actual_hours` REAL, `assigned_user_id` TEXT, `parent_task_id` INTEGER, `attachments` TEXT, `notes` TEXT, `location` TEXT, `is_recurring` INTEGER NOT NULL, `recurring_pattern` TEXT, `color` TEXT, `is_archived` INTEGER NOT NULL, `energy_level` TEXT NOT NULL, `focus_time_minutes` INTEGER, `importance` TEXT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `task_categories` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `color` TEXT NOT NULL, `icon` TEXT, `is_default` INTEGER NOT NULL, `created_by` TEXT, `created_at` INTEGER NOT NULL, `is_active` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `subtasks` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `parent_task_id` INTEGER NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `is_completed` INTEGER NOT NULL, `order_index` INTEGER NOT NULL, `assigned_user_id` TEXT, `due_date` INTEGER, `created_at` INTEGER NOT NULL, `completed_at` INTEGER, `estimated_minutes` INTEGER, `actual_minutes` INTEGER, FOREIGN KEY(`parent_task_id`) REFERENCES `tasks`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_subtasks_parent_task_id` ON `subtasks` (`parent_task_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `chat_messages` (`id` TEXT NOT NULL, `conversationId` TEXT NOT NULL, `senderId` TEXT NOT NULL, `receiverId` TEXT NOT NULL, `message` TEXT NOT NULL, `messageType` TEXT NOT NULL, `attachmentPath` TEXT, `attachmentType` TEXT, `timestamp` INTEGER NOT NULL, `isRead` INTEGER NOT NULL, `isDelivered` INTEGER NOT NULL, `replyToMessageId` TEXT, `isEdited` INTEGER NOT NULL, `editedAt` INTEGER, PRIMARY KEY(`id`), FOREIGN KEY(`senderId`) REFERENCES `users`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`receiverId`) REFERENCES `users`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_chat_messages_senderId` ON `chat_messages` (`senderId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_chat_messages_receiverId` ON `chat_messages` (`receiverId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_chat_messages_conversationId` ON `chat_messages` (`conversationId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_chat_messages_timestamp` ON `chat_messages` (`timestamp`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `conversations` (`id` TEXT NOT NULL, `participant1Id` TEXT NOT NULL, `participant2Id` TEXT NOT NULL, `lastMessage` TEXT, `lastMessageTime` INTEGER NOT NULL, `lastMessageSenderId` TEXT, `unreadCount1` INTEGER NOT NULL, `unreadCount2` INTEGER NOT NULL, `isArchived1` INTEGER NOT NULL, `isArchived2` INTEGER NOT NULL, `isMuted1` INTEGER NOT NULL, `isMuted2` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_conversations_participant1Id` ON `conversations` (`participant1Id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_conversations_participant2Id` ON `conversations` (`participant2Id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_conversations_lastMessageTime` ON `conversations` (`lastMessageTime`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `products` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `category` TEXT NOT NULL, `expiry_date` INTEGER NOT NULL, `quantity` INTEGER NOT NULL, `location` TEXT, `notes` TEXT, `barcode` TEXT, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, `is_expired` INTEGER NOT NULL, `days_until_expiry` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '90e90716a7c693b7b097c6f27aa55580')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `kpis`");
        db.execSQL("DROP TABLE IF EXISTS `kpi_progress_entries`");
        db.execSQL("DROP TABLE IF EXISTS `users`");
        db.execSQL("DROP TABLE IF EXISTS `user_kpi_assignments`");
        db.execSQL("DROP TABLE IF EXISTS `tasks`");
        db.execSQL("DROP TABLE IF EXISTS `task_categories`");
        db.execSQL("DROP TABLE IF EXISTS `subtasks`");
        db.execSQL("DROP TABLE IF EXISTS `chat_messages`");
        db.execSQL("DROP TABLE IF EXISTS `conversations`");
        db.execSQL("DROP TABLE IF EXISTS `products`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsKpis = new HashMap<String, TableInfo.Column>(15);
        _columnsKpis.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("annual_target", new TableInfo.Column("annual_target", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("quarterly_target", new TableInfo.Column("quarterly_target", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("monthly_target", new TableInfo.Column("monthly_target", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("daily_target", new TableInfo.Column("daily_target", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("unit", new TableInfo.Column("unit", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("creation_date", new TableInfo.Column("creation_date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("owner_type", new TableInfo.Column("owner_type", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("owner_name", new TableInfo.Column("owner_name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("owner_image_uri", new TableInfo.Column("owner_image_uri", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("card_color_hex", new TableInfo.Column("card_color_hex", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("bottom_card_color_hex", new TableInfo.Column("bottom_card_color_hex", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpis.put("master_kpi_id", new TableInfo.Column("master_kpi_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysKpis = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesKpis = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoKpis = new TableInfo("kpis", _columnsKpis, _foreignKeysKpis, _indicesKpis);
        final TableInfo _existingKpis = TableInfo.read(db, "kpis");
        if (!_infoKpis.equals(_existingKpis)) {
          return new RoomOpenHelper.ValidationResult(false, "kpis(com.example.kpitrackerapp.models.Kpi).\n"
                  + " Expected:\n" + _infoKpis + "\n"
                  + " Found:\n" + _existingKpis);
        }
        final HashMap<String, TableInfo.Column> _columnsKpiProgressEntries = new HashMap<String, TableInfo.Column>(6);
        _columnsKpiProgressEntries.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpiProgressEntries.put("kpi_id", new TableInfo.Column("kpi_id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpiProgressEntries.put("user_id", new TableInfo.Column("user_id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpiProgressEntries.put("entry_date", new TableInfo.Column("entry_date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpiProgressEntries.put("value", new TableInfo.Column("value", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKpiProgressEntries.put("user_identifier", new TableInfo.Column("user_identifier", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysKpiProgressEntries = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysKpiProgressEntries.add(new TableInfo.ForeignKey("kpis", "CASCADE", "NO ACTION", Arrays.asList("kpi_id"), Arrays.asList("id")));
        _foreignKeysKpiProgressEntries.add(new TableInfo.ForeignKey("users", "CASCADE", "NO ACTION", Arrays.asList("user_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesKpiProgressEntries = new HashSet<TableInfo.Index>(3);
        _indicesKpiProgressEntries.add(new TableInfo.Index("index_kpi_progress_entries_kpi_id", false, Arrays.asList("kpi_id"), Arrays.asList("ASC")));
        _indicesKpiProgressEntries.add(new TableInfo.Index("index_kpi_progress_entries_user_id", false, Arrays.asList("user_id"), Arrays.asList("ASC")));
        _indicesKpiProgressEntries.add(new TableInfo.Index("index_kpi_progress_entries_entry_date", false, Arrays.asList("entry_date"), Arrays.asList("ASC")));
        final TableInfo _infoKpiProgressEntries = new TableInfo("kpi_progress_entries", _columnsKpiProgressEntries, _foreignKeysKpiProgressEntries, _indicesKpiProgressEntries);
        final TableInfo _existingKpiProgressEntries = TableInfo.read(db, "kpi_progress_entries");
        if (!_infoKpiProgressEntries.equals(_existingKpiProgressEntries)) {
          return new RoomOpenHelper.ValidationResult(false, "kpi_progress_entries(com.example.kpitrackerapp.models.KpiProgressEntry).\n"
                  + " Expected:\n" + _infoKpiProgressEntries + "\n"
                  + " Found:\n" + _existingKpiProgressEntries);
        }
        final HashMap<String, TableInfo.Column> _columnsUsers = new HashMap<String, TableInfo.Column>(14);
        _columnsUsers.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("imagePath", new TableInfo.Column("imagePath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("topColor", new TableInfo.Column("topColor", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("bottomColor", new TableInfo.Column("bottomColor", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("email", new TableInfo.Column("email", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("phone", new TableInfo.Column("phone", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("username", new TableInfo.Column("username", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("department", new TableInfo.Column("department", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("lastLoginAt", new TableInfo.Column("lastLoginAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("role", new TableInfo.Column("role", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("permissions", new TableInfo.Column("permissions", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUsers = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUsers = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUsers = new TableInfo("users", _columnsUsers, _foreignKeysUsers, _indicesUsers);
        final TableInfo _existingUsers = TableInfo.read(db, "users");
        if (!_infoUsers.equals(_existingUsers)) {
          return new RoomOpenHelper.ValidationResult(false, "users(com.example.kpitrackerapp.models.User).\n"
                  + " Expected:\n" + _infoUsers + "\n"
                  + " Found:\n" + _existingUsers);
        }
        final HashMap<String, TableInfo.Column> _columnsUserKpiAssignments = new HashMap<String, TableInfo.Column>(3);
        _columnsUserKpiAssignments.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserKpiAssignments.put("user_id", new TableInfo.Column("user_id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserKpiAssignments.put("kpi_id", new TableInfo.Column("kpi_id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserKpiAssignments = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysUserKpiAssignments.add(new TableInfo.ForeignKey("users", "CASCADE", "NO ACTION", Arrays.asList("user_id"), Arrays.asList("id")));
        _foreignKeysUserKpiAssignments.add(new TableInfo.ForeignKey("kpis", "CASCADE", "NO ACTION", Arrays.asList("kpi_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesUserKpiAssignments = new HashSet<TableInfo.Index>(2);
        _indicesUserKpiAssignments.add(new TableInfo.Index("index_user_kpi_assignments_user_id", false, Arrays.asList("user_id"), Arrays.asList("ASC")));
        _indicesUserKpiAssignments.add(new TableInfo.Index("index_user_kpi_assignments_kpi_id", false, Arrays.asList("kpi_id"), Arrays.asList("ASC")));
        final TableInfo _infoUserKpiAssignments = new TableInfo("user_kpi_assignments", _columnsUserKpiAssignments, _foreignKeysUserKpiAssignments, _indicesUserKpiAssignments);
        final TableInfo _existingUserKpiAssignments = TableInfo.read(db, "user_kpi_assignments");
        if (!_infoUserKpiAssignments.equals(_existingUserKpiAssignments)) {
          return new RoomOpenHelper.ValidationResult(false, "user_kpi_assignments(com.example.kpitrackerapp.models.UserKpiAssignment).\n"
                  + " Expected:\n" + _infoUserKpiAssignments + "\n"
                  + " Found:\n" + _existingUserKpiAssignments);
        }
        final HashMap<String, TableInfo.Column> _columnsTasks = new HashMap<String, TableInfo.Column>(26);
        _columnsTasks.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("expirationDate", new TableInfo.Column("expirationDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("creationDate", new TableInfo.Column("creationDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("completionDate", new TableInfo.Column("completionDate", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("reminderDaysBefore", new TableInfo.Column("reminderDaysBefore", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("priority", new TableInfo.Column("priority", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("category", new TableInfo.Column("category", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("tags", new TableInfo.Column("tags", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("progress", new TableInfo.Column("progress", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("estimated_hours", new TableInfo.Column("estimated_hours", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("actual_hours", new TableInfo.Column("actual_hours", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("assigned_user_id", new TableInfo.Column("assigned_user_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("parent_task_id", new TableInfo.Column("parent_task_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("attachments", new TableInfo.Column("attachments", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("location", new TableInfo.Column("location", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("is_recurring", new TableInfo.Column("is_recurring", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("recurring_pattern", new TableInfo.Column("recurring_pattern", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("color", new TableInfo.Column("color", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("is_archived", new TableInfo.Column("is_archived", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("energy_level", new TableInfo.Column("energy_level", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("focus_time_minutes", new TableInfo.Column("focus_time_minutes", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("importance", new TableInfo.Column("importance", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTasks = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTasks = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTasks = new TableInfo("tasks", _columnsTasks, _foreignKeysTasks, _indicesTasks);
        final TableInfo _existingTasks = TableInfo.read(db, "tasks");
        if (!_infoTasks.equals(_existingTasks)) {
          return new RoomOpenHelper.ValidationResult(false, "tasks(com.example.kpitrackerapp.models.Task).\n"
                  + " Expected:\n" + _infoTasks + "\n"
                  + " Found:\n" + _existingTasks);
        }
        final HashMap<String, TableInfo.Column> _columnsTaskCategories = new HashMap<String, TableInfo.Column>(9);
        _columnsTaskCategories.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("color", new TableInfo.Column("color", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("icon", new TableInfo.Column("icon", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("is_default", new TableInfo.Column("is_default", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("created_by", new TableInfo.Column("created_by", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskCategories.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTaskCategories = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTaskCategories = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTaskCategories = new TableInfo("task_categories", _columnsTaskCategories, _foreignKeysTaskCategories, _indicesTaskCategories);
        final TableInfo _existingTaskCategories = TableInfo.read(db, "task_categories");
        if (!_infoTaskCategories.equals(_existingTaskCategories)) {
          return new RoomOpenHelper.ValidationResult(false, "task_categories(com.example.kpitrackerapp.models.TaskCategory).\n"
                  + " Expected:\n" + _infoTaskCategories + "\n"
                  + " Found:\n" + _existingTaskCategories);
        }
        final HashMap<String, TableInfo.Column> _columnsSubtasks = new HashMap<String, TableInfo.Column>(12);
        _columnsSubtasks.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("parent_task_id", new TableInfo.Column("parent_task_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("is_completed", new TableInfo.Column("is_completed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("order_index", new TableInfo.Column("order_index", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("assigned_user_id", new TableInfo.Column("assigned_user_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("due_date", new TableInfo.Column("due_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("completed_at", new TableInfo.Column("completed_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("estimated_minutes", new TableInfo.Column("estimated_minutes", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubtasks.put("actual_minutes", new TableInfo.Column("actual_minutes", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSubtasks = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysSubtasks.add(new TableInfo.ForeignKey("tasks", "CASCADE", "NO ACTION", Arrays.asList("parent_task_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesSubtasks = new HashSet<TableInfo.Index>(1);
        _indicesSubtasks.add(new TableInfo.Index("index_subtasks_parent_task_id", false, Arrays.asList("parent_task_id"), Arrays.asList("ASC")));
        final TableInfo _infoSubtasks = new TableInfo("subtasks", _columnsSubtasks, _foreignKeysSubtasks, _indicesSubtasks);
        final TableInfo _existingSubtasks = TableInfo.read(db, "subtasks");
        if (!_infoSubtasks.equals(_existingSubtasks)) {
          return new RoomOpenHelper.ValidationResult(false, "subtasks(com.example.kpitrackerapp.models.Subtask).\n"
                  + " Expected:\n" + _infoSubtasks + "\n"
                  + " Found:\n" + _existingSubtasks);
        }
        final HashMap<String, TableInfo.Column> _columnsChatMessages = new HashMap<String, TableInfo.Column>(14);
        _columnsChatMessages.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("conversationId", new TableInfo.Column("conversationId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("senderId", new TableInfo.Column("senderId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("receiverId", new TableInfo.Column("receiverId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("message", new TableInfo.Column("message", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("messageType", new TableInfo.Column("messageType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("attachmentPath", new TableInfo.Column("attachmentPath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("attachmentType", new TableInfo.Column("attachmentType", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("isRead", new TableInfo.Column("isRead", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("isDelivered", new TableInfo.Column("isDelivered", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("replyToMessageId", new TableInfo.Column("replyToMessageId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("isEdited", new TableInfo.Column("isEdited", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsChatMessages.put("editedAt", new TableInfo.Column("editedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysChatMessages = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysChatMessages.add(new TableInfo.ForeignKey("users", "CASCADE", "NO ACTION", Arrays.asList("senderId"), Arrays.asList("id")));
        _foreignKeysChatMessages.add(new TableInfo.ForeignKey("users", "CASCADE", "NO ACTION", Arrays.asList("receiverId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesChatMessages = new HashSet<TableInfo.Index>(4);
        _indicesChatMessages.add(new TableInfo.Index("index_chat_messages_senderId", false, Arrays.asList("senderId"), Arrays.asList("ASC")));
        _indicesChatMessages.add(new TableInfo.Index("index_chat_messages_receiverId", false, Arrays.asList("receiverId"), Arrays.asList("ASC")));
        _indicesChatMessages.add(new TableInfo.Index("index_chat_messages_conversationId", false, Arrays.asList("conversationId"), Arrays.asList("ASC")));
        _indicesChatMessages.add(new TableInfo.Index("index_chat_messages_timestamp", false, Arrays.asList("timestamp"), Arrays.asList("ASC")));
        final TableInfo _infoChatMessages = new TableInfo("chat_messages", _columnsChatMessages, _foreignKeysChatMessages, _indicesChatMessages);
        final TableInfo _existingChatMessages = TableInfo.read(db, "chat_messages");
        if (!_infoChatMessages.equals(_existingChatMessages)) {
          return new RoomOpenHelper.ValidationResult(false, "chat_messages(com.example.kpitrackerapp.models.ChatMessage).\n"
                  + " Expected:\n" + _infoChatMessages + "\n"
                  + " Found:\n" + _existingChatMessages);
        }
        final HashMap<String, TableInfo.Column> _columnsConversations = new HashMap<String, TableInfo.Column>(13);
        _columnsConversations.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("participant1Id", new TableInfo.Column("participant1Id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("participant2Id", new TableInfo.Column("participant2Id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("lastMessage", new TableInfo.Column("lastMessage", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("lastMessageTime", new TableInfo.Column("lastMessageTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("lastMessageSenderId", new TableInfo.Column("lastMessageSenderId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("unreadCount1", new TableInfo.Column("unreadCount1", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("unreadCount2", new TableInfo.Column("unreadCount2", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("isArchived1", new TableInfo.Column("isArchived1", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("isArchived2", new TableInfo.Column("isArchived2", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("isMuted1", new TableInfo.Column("isMuted1", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("isMuted2", new TableInfo.Column("isMuted2", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysConversations = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesConversations = new HashSet<TableInfo.Index>(3);
        _indicesConversations.add(new TableInfo.Index("index_conversations_participant1Id", false, Arrays.asList("participant1Id"), Arrays.asList("ASC")));
        _indicesConversations.add(new TableInfo.Index("index_conversations_participant2Id", false, Arrays.asList("participant2Id"), Arrays.asList("ASC")));
        _indicesConversations.add(new TableInfo.Index("index_conversations_lastMessageTime", false, Arrays.asList("lastMessageTime"), Arrays.asList("ASC")));
        final TableInfo _infoConversations = new TableInfo("conversations", _columnsConversations, _foreignKeysConversations, _indicesConversations);
        final TableInfo _existingConversations = TableInfo.read(db, "conversations");
        if (!_infoConversations.equals(_existingConversations)) {
          return new RoomOpenHelper.ValidationResult(false, "conversations(com.example.kpitrackerapp.models.Conversation).\n"
                  + " Expected:\n" + _infoConversations + "\n"
                  + " Found:\n" + _existingConversations);
        }
        final HashMap<String, TableInfo.Column> _columnsProducts = new HashMap<String, TableInfo.Column>(12);
        _columnsProducts.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("expiry_date", new TableInfo.Column("expiry_date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("quantity", new TableInfo.Column("quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("location", new TableInfo.Column("location", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("barcode", new TableInfo.Column("barcode", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("is_expired", new TableInfo.Column("is_expired", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("days_until_expiry", new TableInfo.Column("days_until_expiry", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysProducts = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesProducts = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoProducts = new TableInfo("products", _columnsProducts, _foreignKeysProducts, _indicesProducts);
        final TableInfo _existingProducts = TableInfo.read(db, "products");
        if (!_infoProducts.equals(_existingProducts)) {
          return new RoomOpenHelper.ValidationResult(false, "products(com.example.kpitrackerapp.models.Product).\n"
                  + " Expected:\n" + _infoProducts + "\n"
                  + " Found:\n" + _existingProducts);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "90e90716a7c693b7b097c6f27aa55580", "753ffa8b969b490cc37ee69f20d828f1");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "kpis","kpi_progress_entries","users","user_kpi_assignments","tasks","task_categories","subtasks","chat_messages","conversations","products");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `kpis`");
      _db.execSQL("DELETE FROM `kpi_progress_entries`");
      _db.execSQL("DELETE FROM `users`");
      _db.execSQL("DELETE FROM `user_kpi_assignments`");
      _db.execSQL("DELETE FROM `tasks`");
      _db.execSQL("DELETE FROM `task_categories`");
      _db.execSQL("DELETE FROM `subtasks`");
      _db.execSQL("DELETE FROM `chat_messages`");
      _db.execSQL("DELETE FROM `conversations`");
      _db.execSQL("DELETE FROM `products`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(KpiDao.class, KpiDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(KpiProgressEntryDao.class, KpiProgressEntryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserDao.class, UserDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserKpiAssignmentDao.class, UserKpiAssignmentDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TaskDao.class, TaskDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TaskCategoryDao.class, TaskCategoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SubtaskDao.class, SubtaskDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ChatDao.class, ChatDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ProductDao.class, ProductDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public KpiDao kpiDao() {
    if (_kpiDao != null) {
      return _kpiDao;
    } else {
      synchronized(this) {
        if(_kpiDao == null) {
          _kpiDao = new KpiDao_Impl(this);
        }
        return _kpiDao;
      }
    }
  }

  @Override
  public KpiProgressEntryDao progressEntryDao() {
    if (_kpiProgressEntryDao != null) {
      return _kpiProgressEntryDao;
    } else {
      synchronized(this) {
        if(_kpiProgressEntryDao == null) {
          _kpiProgressEntryDao = new KpiProgressEntryDao_Impl(this);
        }
        return _kpiProgressEntryDao;
      }
    }
  }

  @Override
  public UserDao userDao() {
    if (_userDao != null) {
      return _userDao;
    } else {
      synchronized(this) {
        if(_userDao == null) {
          _userDao = new UserDao_Impl(this);
        }
        return _userDao;
      }
    }
  }

  @Override
  public UserKpiAssignmentDao userKpiAssignmentDao() {
    if (_userKpiAssignmentDao != null) {
      return _userKpiAssignmentDao;
    } else {
      synchronized(this) {
        if(_userKpiAssignmentDao == null) {
          _userKpiAssignmentDao = new UserKpiAssignmentDao_Impl(this);
        }
        return _userKpiAssignmentDao;
      }
    }
  }

  @Override
  public TaskDao taskDao() {
    if (_taskDao != null) {
      return _taskDao;
    } else {
      synchronized(this) {
        if(_taskDao == null) {
          _taskDao = new TaskDao_Impl(this);
        }
        return _taskDao;
      }
    }
  }

  @Override
  public TaskCategoryDao taskCategoryDao() {
    if (_taskCategoryDao != null) {
      return _taskCategoryDao;
    } else {
      synchronized(this) {
        if(_taskCategoryDao == null) {
          _taskCategoryDao = new TaskCategoryDao_Impl(this);
        }
        return _taskCategoryDao;
      }
    }
  }

  @Override
  public SubtaskDao subtaskDao() {
    if (_subtaskDao != null) {
      return _subtaskDao;
    } else {
      synchronized(this) {
        if(_subtaskDao == null) {
          _subtaskDao = new SubtaskDao_Impl(this);
        }
        return _subtaskDao;
      }
    }
  }

  @Override
  public ChatDao chatDao() {
    if (_chatDao != null) {
      return _chatDao;
    } else {
      synchronized(this) {
        if(_chatDao == null) {
          _chatDao = new ChatDao_Impl(this);
        }
        return _chatDao;
      }
    }
  }

  @Override
  public ProductDao productDao() {
    if (_productDao != null) {
      return _productDao;
    } else {
      synchronized(this) {
        if(_productDao == null) {
          _productDao = new ProductDao_Impl(this);
        }
        return _productDao;
      }
    }
  }
}
