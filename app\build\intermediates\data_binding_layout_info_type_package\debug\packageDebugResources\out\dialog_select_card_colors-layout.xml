<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_select_card_colors" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\dialog_select_card_colors.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_select_card_colors_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="14"/></Target><Target id="@+id/topColorPreview" view="View"><Expressions/><location startLine="23" startOffset="8" endLine="28" endOffset="61"/></Target><Target id="@+id/buttonPickTopColor" view="Button"><Expressions/><location startLine="30" startOffset="8" endLine="36" endOffset="68"/></Target><Target id="@+id/bottomColorPreview" view="View"><Expressions/><location startLine="54" startOffset="8" endLine="59" endOffset="61"/></Target><Target id="@+id/buttonPickBottomColor" view="Button"><Expressions/><location startLine="61" startOffset="8" endLine="67" endOffset="68"/></Target><Target id="@+id/buttonResetColors" view="Button"><Expressions/><location startLine="78" startOffset="8" endLine="83" endOffset="53"/></Target><Target id="@+id/buttonCancelColorSelection" view="Button"><Expressions/><location startLine="85" startOffset="8" endLine="91" endOffset="50"/></Target><Target id="@+id/buttonSaveColors" view="Button"><Expressions/><location startLine="98" startOffset="4" endLine="105" endOffset="64"/></Target></Targets></Layout>