<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_edit_kpi_original" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_add_edit_kpi_original.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_add_edit_kpi_original_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="221" endOffset="12"/></Target><Target id="@+id/kpiNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="16" startOffset="8" endLine="29" endOffset="63"/></Target><Target id="@+id/kpiNameEditText" view="com.google.android.material.textfield.MaterialAutoCompleteTextView"><Expressions/><location startLine="24" startOffset="12" endLine="28" endOffset="50"/></Target><Target id="@+id/kpiDescriptionInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="31" startOffset="8" endLine="46" endOffset="63"/></Target><Target id="@+id/kpiDescriptionEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="40" startOffset="12" endLine="45" endOffset="38"/></Target><Target id="@+id/kpiTargetInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="48" startOffset="8" endLine="62" endOffset="63"/></Target><Target id="@+id/kpiTargetEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="12" endLine="61" endOffset="51"/></Target><Target id="@+id/kpiQuarterlyTargetInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="65" startOffset="8" endLine="78" endOffset="63"/></Target><Target id="@+id/kpiQuarterlyTargetEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="73" startOffset="12" endLine="77" endOffset="51"/></Target><Target id="@+id/kpiMonthlyTargetInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="80" startOffset="8" endLine="93" endOffset="63"/></Target><Target id="@+id/kpiMonthlyTargetEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="88" startOffset="12" endLine="92" endOffset="51"/></Target><Target id="@+id/kpiDailyTargetInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="95" startOffset="8" endLine="108" endOffset="63"/></Target><Target id="@+id/kpiDailyTargetEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="103" startOffset="12" endLine="107" endOffset="51"/></Target><Target id="@+id/ownerTypeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="111" startOffset="8" endLine="124" endOffset="63"/></Target><Target id="@+id/ownerTypeAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="119" startOffset="12" endLine="123" endOffset="42"/></Target><Target id="@+id/ownerNameSelectionLayout" view="LinearLayout"><Expressions/><location startLine="127" startOffset="8" endLine="147" endOffset="22"/></Target><Target id="@+id/ownerNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="135" startOffset="12" endLine="146" endOffset="67"/></Target><Target id="@+id/ownerNameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="141" startOffset="16" endLine="145" endOffset="69"/></Target><Target id="@+id/ownerImageView" view="ImageView"><Expressions/><location startLine="149" startOffset="8" endLine="158" endOffset="39"/></Target><Target id="@+id/selectOwnerImageButton" view="Button"><Expressions/><location startLine="160" startOffset="8" endLine="169" endOffset="39"/></Target><Target id="@+id/kpiUnitLabelTextView" view="TextView"><Expressions/><location startLine="171" startOffset="8" endLine="177" endOffset="64"/></Target><Target id="@+id/kpiUnitRadioGroup" view="RadioGroup"><Expressions/><location startLine="179" startOffset="8" endLine="211" endOffset="20"/></Target><Target id="@+id/unitNumberRadioButton" view="RadioButton"><Expressions/><location startLine="186" startOffset="12" endLine="192" endOffset="52"/></Target><Target id="@+id/unitPercentageRadioButton" view="RadioButton"><Expressions/><location startLine="193" startOffset="12" endLine="198" endOffset="56"/></Target><Target id="@+id/unitCurrencyRadioButton" view="RadioButton"><Expressions/><location startLine="199" startOffset="12" endLine="204" endOffset="54"/></Target><Target id="@+id/unitPointRadioButton" view="RadioButton"><Expressions/><location startLine="205" startOffset="12" endLine="210" endOffset="51"/></Target><Target id="@+id/saveKpiButton" view="Button"><Expressions/><location startLine="213" startOffset="8" endLine="218" endOffset="52"/></Target></Targets></Layout>