<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="user_filter_dialog" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\user_filter_dialog.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/user_filter_dialog_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="75" endOffset="51"/></Target><Target id="@+id/dialogTitle" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="15" endOffset="51"/></Target><Target id="@+id/userFilterRadioGroup" view="RadioGroup"><Expressions/><location startLine="17" startOffset="4" endLine="38" endOffset="16"/></Target><Target id="@+id/radioAllUsers" view="RadioButton"><Expressions/><location startLine="24" startOffset="8" endLine="30" endOffset="41"/></Target><Target id="@+id/radioSelectUsers" view="RadioButton"><Expressions/><location startLine="32" startOffset="8" endLine="37" endOffset="41"/></Target><Target id="@+id/userCheckboxRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="40" startOffset="4" endLine="46" endOffset="72"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="56" startOffset="8" endLine="63" endOffset="41"/></Target><Target id="@+id/applyButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="65" startOffset="8" endLine="72" endOffset="36"/></Target></Targets></Layout>