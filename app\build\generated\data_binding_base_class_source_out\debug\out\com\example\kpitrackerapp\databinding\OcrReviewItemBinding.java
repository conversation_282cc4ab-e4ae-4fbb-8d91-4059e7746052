// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OcrReviewItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnDeleteReviewItem;

  @NonNull
  public final EditText etReviewValue;

  @NonNull
  public final TextView tvReviewDate;

  private OcrReviewItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageButton btnDeleteReviewItem, @NonNull EditText etReviewValue,
      @NonNull TextView tvReviewDate) {
    this.rootView = rootView;
    this.btnDeleteReviewItem = btnDeleteReviewItem;
    this.etReviewValue = etReviewValue;
    this.tvReviewDate = tvReviewDate;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static OcrReviewItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OcrReviewItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.ocr_review_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OcrReviewItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_delete_review_item;
      ImageButton btnDeleteReviewItem = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteReviewItem == null) {
        break missingId;
      }

      id = R.id.et_review_value;
      EditText etReviewValue = ViewBindings.findChildViewById(rootView, id);
      if (etReviewValue == null) {
        break missingId;
      }

      id = R.id.tv_review_date;
      TextView tvReviewDate = ViewBindings.findChildViewById(rootView, id);
      if (tvReviewDate == null) {
        break missingId;
      }

      return new OcrReviewItemBinding((ConstraintLayout) rootView, btnDeleteReviewItem,
          etReviewValue, tvReviewDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
