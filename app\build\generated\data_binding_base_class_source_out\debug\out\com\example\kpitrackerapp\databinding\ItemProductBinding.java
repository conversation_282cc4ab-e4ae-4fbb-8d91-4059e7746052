// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemProductBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView categoryText;

  @NonNull
  public final TextView daysText;

  @NonNull
  public final TextView expiryDateText;

  @NonNull
  public final TextView locationText;

  @NonNull
  public final TextView notesText;

  @NonNull
  public final TextView productNameText;

  @NonNull
  public final TextView quantityText;

  @NonNull
  public final MaterialCardView statusIndicator;

  private ItemProductBinding(@NonNull MaterialCardView rootView, @NonNull TextView categoryText,
      @NonNull TextView daysText, @NonNull TextView expiryDateText, @NonNull TextView locationText,
      @NonNull TextView notesText, @NonNull TextView productNameText,
      @NonNull TextView quantityText, @NonNull MaterialCardView statusIndicator) {
    this.rootView = rootView;
    this.categoryText = categoryText;
    this.daysText = daysText;
    this.expiryDateText = expiryDateText;
    this.locationText = locationText;
    this.notesText = notesText;
    this.productNameText = productNameText;
    this.quantityText = quantityText;
    this.statusIndicator = statusIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemProductBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemProductBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_product, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemProductBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryText;
      TextView categoryText = ViewBindings.findChildViewById(rootView, id);
      if (categoryText == null) {
        break missingId;
      }

      id = R.id.daysText;
      TextView daysText = ViewBindings.findChildViewById(rootView, id);
      if (daysText == null) {
        break missingId;
      }

      id = R.id.expiryDateText;
      TextView expiryDateText = ViewBindings.findChildViewById(rootView, id);
      if (expiryDateText == null) {
        break missingId;
      }

      id = R.id.locationText;
      TextView locationText = ViewBindings.findChildViewById(rootView, id);
      if (locationText == null) {
        break missingId;
      }

      id = R.id.notesText;
      TextView notesText = ViewBindings.findChildViewById(rootView, id);
      if (notesText == null) {
        break missingId;
      }

      id = R.id.productNameText;
      TextView productNameText = ViewBindings.findChildViewById(rootView, id);
      if (productNameText == null) {
        break missingId;
      }

      id = R.id.quantityText;
      TextView quantityText = ViewBindings.findChildViewById(rootView, id);
      if (quantityText == null) {
        break missingId;
      }

      id = R.id.statusIndicator;
      MaterialCardView statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      return new ItemProductBinding((MaterialCardView) rootView, categoryText, daysText,
          expiryDateText, locationText, notesText, productNameText, quantityText, statusIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
