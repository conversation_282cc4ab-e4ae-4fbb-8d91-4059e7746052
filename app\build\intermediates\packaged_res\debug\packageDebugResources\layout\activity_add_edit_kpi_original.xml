<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.AddEditKpiOriginalActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- KPI Name (Template or New) -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/kpiNameInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="@string/kpi_name_hint">

            <com.google.android.material.textfield.MaterialAutoCompleteTextView
                android:id="@+id/kpiNameEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textCapWords" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/kpiDescriptionInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:hint="@string/kpi_description_hint">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/kpiDescriptionEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine|textCapSentences"
                android:minLines="2" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/kpiTargetInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:hint="@string/kpi_annual_target_hint">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/kpiTargetEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Optional Targets -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/kpiQuarterlyTargetInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:hint="@string/kpi_quarterly_target_hint">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/kpiQuarterlyTargetEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/kpiMonthlyTargetInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:hint="@string/kpi_monthly_target_hint">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/kpiMonthlyTargetEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/kpiDailyTargetInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:hint="@string/kpi_daily_target_hint">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/kpiDailyTargetEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Owner Type Selection -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/ownerTypeInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:hint="@string/owner_type_hint">
            <AutoCompleteTextView
                android:id="@+id/ownerTypeAutoCompleteTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Owner Name Input / Selection -->
        <LinearLayout
            android:id="@+id/ownerNameSelectionLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:visibility="gone"> <!-- Initially hidden, shown based on Owner Type -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/ownerNameInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/owner_name_hint">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/ownerNameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName|textCapWords" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/ownerImageView"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="16dp"
            android:layout_gravity="center_horizontal"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_baseline_person_24"
            android:contentDescription="@string/owner_image_description"
            android:visibility="gone" /> <!-- Initially hidden -->

        <Button
            android:id="@+id/selectOwnerImageButton"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:text="@string/select_owner_image_button"
            android:visibility="gone" /> <!-- Initially hidden -->

        <TextView
            android:id="@+id/kpiUnitLabelTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/kpi_unit_label"
            android:textAppearance="?attr/textAppearanceBody1" />

        <RadioGroup
            android:id="@+id/kpiUnitRadioGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:orientation="horizontal">
            <RadioButton
                android:id="@+id/unitNumberRadioButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:checked="true"
                android:text="@string/unit_number" />
            <RadioButton
                android:id="@+id/unitPercentageRadioButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/unit_percentage" />
            <RadioButton
                android:id="@+id/unitCurrencyRadioButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/unit_currency" />
            <RadioButton
                android:id="@+id/unitPointRadioButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/unit_point" />
        </RadioGroup>

        <Button
            android:id="@+id/saveKpiButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/save_kpi_button" />

    </LinearLayout>
</ScrollView>
