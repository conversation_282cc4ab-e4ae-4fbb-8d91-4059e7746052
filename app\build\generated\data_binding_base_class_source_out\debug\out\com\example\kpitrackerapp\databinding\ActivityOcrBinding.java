// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityOcrBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnCopyText;

  @NonNull
  public final Button btnSelectImage;

  @NonNull
  public final ImageView ivSelectedImage;

  @NonNull
  public final ScrollView scrollViewOcrResult;

  @NonNull
  public final TextView tvOcrResult;

  @NonNull
  public final TextView tvOcrResultLabel;

  private ActivityOcrBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnCopyText,
      @NonNull Button btnSelectImage, @NonNull ImageView ivSelectedImage,
      @NonNull ScrollView scrollViewOcrResult, @NonNull TextView tvOcrResult,
      @NonNull TextView tvOcrResultLabel) {
    this.rootView = rootView;
    this.btnCopyText = btnCopyText;
    this.btnSelectImage = btnSelectImage;
    this.ivSelectedImage = ivSelectedImage;
    this.scrollViewOcrResult = scrollViewOcrResult;
    this.tvOcrResult = tvOcrResult;
    this.tvOcrResultLabel = tvOcrResultLabel;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityOcrBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityOcrBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_ocr, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityOcrBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCopyText;
      Button btnCopyText = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyText == null) {
        break missingId;
      }

      id = R.id.btnSelectImage;
      Button btnSelectImage = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectImage == null) {
        break missingId;
      }

      id = R.id.ivSelectedImage;
      ImageView ivSelectedImage = ViewBindings.findChildViewById(rootView, id);
      if (ivSelectedImage == null) {
        break missingId;
      }

      id = R.id.scrollViewOcrResult;
      ScrollView scrollViewOcrResult = ViewBindings.findChildViewById(rootView, id);
      if (scrollViewOcrResult == null) {
        break missingId;
      }

      id = R.id.tvOcrResult;
      TextView tvOcrResult = ViewBindings.findChildViewById(rootView, id);
      if (tvOcrResult == null) {
        break missingId;
      }

      id = R.id.tvOcrResultLabel;
      TextView tvOcrResultLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvOcrResultLabel == null) {
        break missingId;
      }

      return new ActivityOcrBinding((ConstraintLayout) rootView, btnCopyText, btnSelectImage,
          ivSelectedImage, scrollViewOcrResult, tvOcrResult, tvOcrResultLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
