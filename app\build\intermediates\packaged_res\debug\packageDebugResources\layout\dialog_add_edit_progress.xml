<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:id="@+id/dialogTitleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/add_progress_dialog_title"
        android:textAppearance="?attr/textAppearanceHeadline6"
        android:layout_marginBottom="8dp"/>

    <!-- TextView for Last Entry Date -->
    <TextView
        android:id="@+id/dialogLastEntryDateTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceBody2"
        android:textColor="?android:attr/textColorSecondary"
        android:layout_marginBottom="16dp"
        tools:text="Last Entry: 2025-04-27" />

    <EditText
        android:id="@+id/valueEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="numberDecimal"
        android:hint="@string/progress_value_hint"
        android:importantForAutofill="no" />

    <Button
        android:id="@+id/dateButton"
        style="?attr/materialButtonOutlinedStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Select Date" />

</LinearLayout>
