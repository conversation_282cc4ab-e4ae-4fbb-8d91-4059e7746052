// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TableRow;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ReportTableRowColoredBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView reportAnnualAchieved;

  @NonNull
  public final TextView reportAnnualPercentage;

  @NonNull
  public final TableRow reportAnnualRow;

  @NonNull
  public final TextView reportAnnualTarget;

  @NonNull
  public final TextView reportDailyAchieved;

  @NonNull
  public final TextView reportDailyPercentage;

  @NonNull
  public final TableRow reportDailyRow;

  @NonNull
  public final TextView reportDailyTarget;

  @NonNull
  public final TextView reportMonthlyAchieved;

  @NonNull
  public final TextView reportMonthlyPercentage;

  @NonNull
  public final TableRow reportMonthlyRow;

  @NonNull
  public final TextView reportMonthlyTarget;

  @NonNull
  public final TextView reportQuarterlyAchieved;

  @NonNull
  public final TextView reportQuarterlyPercentage;

  @NonNull
  public final TableRow reportQuarterlyRow;

  @NonNull
  public final TextView reportQuarterlyTarget;

  @NonNull
  public final TextView reportUserNameTextView;

  private ReportTableRowColoredBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView reportAnnualAchieved, @NonNull TextView reportAnnualPercentage,
      @NonNull TableRow reportAnnualRow, @NonNull TextView reportAnnualTarget,
      @NonNull TextView reportDailyAchieved, @NonNull TextView reportDailyPercentage,
      @NonNull TableRow reportDailyRow, @NonNull TextView reportDailyTarget,
      @NonNull TextView reportMonthlyAchieved, @NonNull TextView reportMonthlyPercentage,
      @NonNull TableRow reportMonthlyRow, @NonNull TextView reportMonthlyTarget,
      @NonNull TextView reportQuarterlyAchieved, @NonNull TextView reportQuarterlyPercentage,
      @NonNull TableRow reportQuarterlyRow, @NonNull TextView reportQuarterlyTarget,
      @NonNull TextView reportUserNameTextView) {
    this.rootView = rootView;
    this.reportAnnualAchieved = reportAnnualAchieved;
    this.reportAnnualPercentage = reportAnnualPercentage;
    this.reportAnnualRow = reportAnnualRow;
    this.reportAnnualTarget = reportAnnualTarget;
    this.reportDailyAchieved = reportDailyAchieved;
    this.reportDailyPercentage = reportDailyPercentage;
    this.reportDailyRow = reportDailyRow;
    this.reportDailyTarget = reportDailyTarget;
    this.reportMonthlyAchieved = reportMonthlyAchieved;
    this.reportMonthlyPercentage = reportMonthlyPercentage;
    this.reportMonthlyRow = reportMonthlyRow;
    this.reportMonthlyTarget = reportMonthlyTarget;
    this.reportQuarterlyAchieved = reportQuarterlyAchieved;
    this.reportQuarterlyPercentage = reportQuarterlyPercentage;
    this.reportQuarterlyRow = reportQuarterlyRow;
    this.reportQuarterlyTarget = reportQuarterlyTarget;
    this.reportUserNameTextView = reportUserNameTextView;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ReportTableRowColoredBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ReportTableRowColoredBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.report_table_row_colored, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ReportTableRowColoredBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.reportAnnualAchieved;
      TextView reportAnnualAchieved = ViewBindings.findChildViewById(rootView, id);
      if (reportAnnualAchieved == null) {
        break missingId;
      }

      id = R.id.reportAnnualPercentage;
      TextView reportAnnualPercentage = ViewBindings.findChildViewById(rootView, id);
      if (reportAnnualPercentage == null) {
        break missingId;
      }

      id = R.id.reportAnnualRow;
      TableRow reportAnnualRow = ViewBindings.findChildViewById(rootView, id);
      if (reportAnnualRow == null) {
        break missingId;
      }

      id = R.id.reportAnnualTarget;
      TextView reportAnnualTarget = ViewBindings.findChildViewById(rootView, id);
      if (reportAnnualTarget == null) {
        break missingId;
      }

      id = R.id.reportDailyAchieved;
      TextView reportDailyAchieved = ViewBindings.findChildViewById(rootView, id);
      if (reportDailyAchieved == null) {
        break missingId;
      }

      id = R.id.reportDailyPercentage;
      TextView reportDailyPercentage = ViewBindings.findChildViewById(rootView, id);
      if (reportDailyPercentage == null) {
        break missingId;
      }

      id = R.id.reportDailyRow;
      TableRow reportDailyRow = ViewBindings.findChildViewById(rootView, id);
      if (reportDailyRow == null) {
        break missingId;
      }

      id = R.id.reportDailyTarget;
      TextView reportDailyTarget = ViewBindings.findChildViewById(rootView, id);
      if (reportDailyTarget == null) {
        break missingId;
      }

      id = R.id.reportMonthlyAchieved;
      TextView reportMonthlyAchieved = ViewBindings.findChildViewById(rootView, id);
      if (reportMonthlyAchieved == null) {
        break missingId;
      }

      id = R.id.reportMonthlyPercentage;
      TextView reportMonthlyPercentage = ViewBindings.findChildViewById(rootView, id);
      if (reportMonthlyPercentage == null) {
        break missingId;
      }

      id = R.id.reportMonthlyRow;
      TableRow reportMonthlyRow = ViewBindings.findChildViewById(rootView, id);
      if (reportMonthlyRow == null) {
        break missingId;
      }

      id = R.id.reportMonthlyTarget;
      TextView reportMonthlyTarget = ViewBindings.findChildViewById(rootView, id);
      if (reportMonthlyTarget == null) {
        break missingId;
      }

      id = R.id.reportQuarterlyAchieved;
      TextView reportQuarterlyAchieved = ViewBindings.findChildViewById(rootView, id);
      if (reportQuarterlyAchieved == null) {
        break missingId;
      }

      id = R.id.reportQuarterlyPercentage;
      TextView reportQuarterlyPercentage = ViewBindings.findChildViewById(rootView, id);
      if (reportQuarterlyPercentage == null) {
        break missingId;
      }

      id = R.id.reportQuarterlyRow;
      TableRow reportQuarterlyRow = ViewBindings.findChildViewById(rootView, id);
      if (reportQuarterlyRow == null) {
        break missingId;
      }

      id = R.id.reportQuarterlyTarget;
      TextView reportQuarterlyTarget = ViewBindings.findChildViewById(rootView, id);
      if (reportQuarterlyTarget == null) {
        break missingId;
      }

      id = R.id.reportUserNameTextView;
      TextView reportUserNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (reportUserNameTextView == null) {
        break missingId;
      }

      return new ReportTableRowColoredBinding((MaterialCardView) rootView, reportAnnualAchieved,
          reportAnnualPercentage, reportAnnualRow, reportAnnualTarget, reportDailyAchieved,
          reportDailyPercentage, reportDailyRow, reportDailyTarget, reportMonthlyAchieved,
          reportMonthlyPercentage, reportMonthlyRow, reportMonthlyTarget, reportQuarterlyAchieved,
          reportQuarterlyPercentage, reportQuarterlyRow, reportQuarterlyTarget,
          reportUserNameTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
