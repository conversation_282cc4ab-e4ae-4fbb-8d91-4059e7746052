1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.kpitrackerapp"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Required for notifications on Android 13+ -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:5-76
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:22-74
13    <!-- Required for running foreground services (used by WorkManager when setForeground is called) -->
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:22-74
15    <!-- Required for Camera -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:5-64
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:22-62
17    <!-- Required for Calendar Integration -->
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:5-72
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:5-73
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:22-70
20    <!-- Required for Speech Recognition -->
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:5-71
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:22-68
22    <uses-permission android:name="android.permission.INTERNET" />
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:5-67
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:22-64
23    <!-- Required for Location Services -->
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:5-79
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:22-76
25    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:5-81
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:22-78
26    <!-- Required for Setting Alarms -->
27    <uses-permission android:name="android.permission.SET_ALARM" />
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:5-68
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:22-65
28    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:5-78
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:22-75
29
30    <!-- Declare camera feature, but don't require it if app can function without -->
31    <uses-feature
31-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:5-84
32        android:name="android.hardware.camera"
32-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:19-57
33        android:required="false" />
33-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:58-82
34
35    <!-- Queries for specific apps we want to detect (Android 11+ requirement) -->
36    <queries>
36-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:28:5-40:15
37
38        <!-- WhatsApp Regular -->
39        <package android:name="com.whatsapp" />
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:9-48
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:18-45
40        <!-- WhatsApp Business -->
41        <package android:name="com.whatsapp.w4b" />
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:9-52
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:18-49
42        <!-- Gmail -->
43        <package android:name="com.google.android.gm" />
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:9-57
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:18-54
44        <!-- Email intent -->
45        <intent>
45-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:36:9-39:18
46            <action android:name="android.intent.action.SENDTO" />
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:13-67
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:21-64
47
48            <data android:scheme="mailto" />
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:13-45
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:19-42
49        </intent>
50    </queries>
51
52    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
53    <!-- <uses-sdk android:minSdkVersion="14"/> -->
54    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:5-79
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:22-76
55    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:22-65
56    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
57    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
60    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
61    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
62
63    <permission
63-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
64        android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
68
69    <uses-feature
69-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
70        android:name="android.hardware.camera.front"
70-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
71        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
71-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
72    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
73    <uses-feature
73-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
74        android:name="android.hardware.camera.autofocus"
74-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
75        android:required="false" />
75-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
76    <uses-feature
76-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
77        android:name="android.hardware.camera.flash"
77-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
78        android:required="false" />
78-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
79    <uses-feature
79-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
80        android:name="android.hardware.screen.landscape"
80-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
81        android:required="false" />
81-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
82    <uses-feature
82-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
83        android:name="android.hardware.wifi"
83-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
84        android:required="false" />
84-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
85
86    <application
86-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:42:5-288:19
87        android:name="com.example.kpitrackerapp.KpiTrackerApplication"
87-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:43:9-46
88        android:allowBackup="true"
88-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:44:9-35
89        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
89-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
90        android:dataExtractionRules="@xml/data_extraction_rules"
90-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:45:9-65
91        android:debuggable="true"
92        android:extractNativeLibs="false"
93        android:fullBackupContent="@xml/backup_rules"
93-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:46:9-54
94        android:icon="@mipmap/ic_launcher"
94-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:48:9-43
95        android:label="@string/app_name"
95-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:47:9-41
96        android:networkSecurityConfig="@xml/network_security_config"
96-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:52:9-69
97        android:roundIcon="@mipmap/ic_launcher"
97-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:49:9-48
98        android:supportsRtl="true"
98-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:50:9-35
99        android:testOnly="true"
100        android:theme="@style/Theme.KPITrackerApp" >
100-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:51:9-51
101
102        <!-- Optional: Request OCR module download on install/update -->
103        <meta-data
103-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:56:9-58:46
104            android:name="com.google.mlkit.vision.DEPENDENCIES"
104-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:57:13-64
105            android:value="ocr,ocr_arabic" />
105-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:58:13-43
106
107        <activity
107-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:60:9-68:20
108            android:name="com.example.kpitrackerapp.MainActivity"
108-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:61:13-41
109            android:exported="true"
109-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:62:13-36
110            android:theme="@style/Theme.KPITrackerApp.NoActionBar" > <!-- Apply NoActionBar theme here -->
110-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:63:13-67
111            <intent-filter>
111-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:64:13-67:29
112                <action android:name="android.intent.action.MAIN" />
112-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:17-69
112-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:25-66
113
114                <category android:name="android.intent.category.LAUNCHER" />
114-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:17-77
114-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:27-74
115            </intent-filter>
116        </activity>
117        <activity
117-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:69:9-73:70
118            android:name="com.example.kpitrackerapp.ui.AddEditKpiActivity"
118-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:70:13-50
119            android:label="@string/add_kpi_title"
119-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:71:13-50
120            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
120-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:72:13-55
121            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- This is now for Tasks -->
121-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:73:13-67
122        <activity
122-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:74:9-78:70
123            android:name="com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity"
123-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:75:13-58
124            android:label="@string/add_kpi_title"
124-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:76:13-50
125            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
125-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:77:13-55
126            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
126-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:78:13-67
127        <activity
127-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:79:9-82:58
128            android:name="com.example.kpitrackerapp.ui.KpiDetailActivity"
128-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:80:13-49
129            android:label="@string/kpi_detail_title"
129-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:81:13-53
130            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
130-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:82:13-55
131        <activity
131-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:83:9-87:70
132            android:name="com.example.kpitrackerapp.ui.ModernReportActivity"
132-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:84:13-52
133            android:label="Interactive Performance Report"
133-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:85:13-59
134            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
134-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:86:13-55
135            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
135-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:87:13-67
136        <activity
136-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:88:9-91:58
137            android:name="com.example.kpitrackerapp.ui.ExpireManagementActivity"
137-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:89:13-56
138            android:label="@string/action_expiry_management"
138-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:90:13-61
139            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
139-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:91:13-55
140        <activity
140-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:92:9-95:66
141            android:name="com.example.kpitrackerapp.ui.SearchEditProgressActivity"
141-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:93:13-58
142            android:label="@string/search_edit_progress_title"
142-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:94:13-63
143            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
143-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:95:13-63
144        <activity
144-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:96:9-99:58
145            android:name="com.example.kpitrackerapp.ui.OcrActivity"
145-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:97:13-43
146            android:label="@string/ocr_activity_title"
146-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:98:13-55
147            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
147-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:99:13-55
148        <activity
148-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:100:9-103:60
149            android:name="com.example.kpitrackerapp.ui.OcrReviewActivity"
149-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:101:13-49
150            android:label="@string/review_ocr_results_title"
150-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:102:13-61
151            android:parentActivityName="com.example.kpitrackerapp.ui.OcrActivity" /> <!-- Parent is OcrActivity -->
151-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:103:13-57
152        <!-- Removed SmartListAnalysisActivity declaration -->
153        <activity
153-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:105:9-108:66
154            android:name="com.example.kpitrackerapp.ui.ExcelImportActivity"
154-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:106:13-51
155            android:label="Import from Excel"
155-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:107:13-46
156            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
156-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:108:13-63
157        <activity
157-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:109:9-112:68
158            android:name="com.example.kpitrackerapp.ui.ExcelReviewActivity"
158-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:110:13-51
159            android:label="Review Excel Import"
159-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:111:13-48
160            android:parentActivityName="com.example.kpitrackerapp.ui.ExcelImportActivity" />
160-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:112:13-65
161        <activity
161-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:114:9-118:70
162            android:name="com.example.kpitrackerapp.ui.UserKpiListActivity"
162-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:115:13-51
163            android:label="@string/user_kpi_list_title"
163-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:116:13-56
164            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
164-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:117:13-55
165            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- Apply NoActionBar theme -->
165-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:118:13-67
166        <activity
166-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:119:9-123:70
167            android:name="com.example.kpitrackerapp.ui.TaskManagementActivity"
167-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:120:13-54
168            android:label="Task Management"
168-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:121:13-44
169            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
169-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:122:13-55
170            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
170-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:123:13-67
171        <activity
171-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:124:9-128:70
172            android:name="com.example.kpitrackerapp.ui.TaskReportActivity"
172-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:125:13-50
173            android:label="Task Reports"
173-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:126:13-41
174            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
174-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:127:13-68
175            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
175-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:128:13-67
176
177        <!-- Login and User Management Activities -->
178        <activity
178-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:131:9-135:40
179            android:name="com.example.kpitrackerapp.ui.LoginActivity"
179-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:132:13-45
180            android:exported="false"
180-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:135:13-37
181            android:label="Login"
181-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:133:13-34
182            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
182-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:134:13-67
183        <activity
183-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:136:9-141:40
184            android:name="com.example.kpitrackerapp.ui.CreateUserActivity"
184-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:137:13-50
185            android:exported="false"
185-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:141:13-37
186            android:label="Create User"
186-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:138:13-40
187            android:parentActivityName="com.example.kpitrackerapp.ui.LoginActivity"
187-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:139:13-59
188            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
188-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:140:13-67
189
190        <!-- Admin Dashboard Activity -->
191        <activity
191-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:144:9-149:40
192            android:name="com.example.kpitrackerapp.AdminDashboardActivity"
192-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:145:13-51
193            android:exported="false"
193-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:149:13-37
194            android:label="Admin Dashboard"
194-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:146:13-44
195            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
195-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:147:13-55
196            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
196-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:148:13-67
197
198        <!-- Product Management Activities -->
199        <activity
199-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:152:9-157:40
200            android:name="com.example.kpitrackerapp.ui.AddProductActivity"
200-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:153:13-50
201            android:exported="false"
201-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:157:13-37
202            android:label="Add Product"
202-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:154:13-40
203            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
203-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:155:13-70
204            android:theme="@style/Theme.KPITrackerApp" />
204-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:156:13-55
205        <activity
205-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:158:9-163:40
206            android:name="com.example.kpitrackerapp.ui.EditProductActivity"
206-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:159:13-51
207            android:exported="false"
207-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:163:13-37
208            android:label="Edit Product"
208-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:160:13-41
209            android:parentActivityName="com.example.kpitrackerapp.ui.ProductListActivity"
209-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:161:13-65
210            android:theme="@style/Theme.KPITrackerApp" />
210-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:162:13-55
211        <activity
211-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:164:9-169:40
212            android:name="com.example.kpitrackerapp.ui.ProductListActivity"
212-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:165:13-51
213            android:exported="false"
213-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:169:13-37
214            android:label="Product List"
214-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:166:13-41
215            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
215-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:167:13-70
216            android:theme="@style/Theme.KPITrackerApp" />
216-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:168:13-55
217        <activity
217-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:170:9-175:40
218            android:name="com.example.kpitrackerapp.ui.ExpiryTrackingActivity"
218-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:171:13-54
219            android:exported="false"
219-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:175:13-37
220            android:label="Expiry Tracking"
220-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:172:13-44
221            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
221-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:173:13-70
222            android:theme="@style/Theme.KPITrackerApp" />
222-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:174:13-55
223
224        <!-- Chat Activities -->
225        <activity
225-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:178:9-183:40
226            android:name="com.example.kpitrackerapp.ui.ChatListActivity"
226-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:179:13-48
227            android:exported="false"
227-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:183:13-37
228            android:label="Messages"
228-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:180:13-37
229            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
229-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:181:13-55
230            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
230-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:182:13-67
231        <activity
231-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:185:9-190:40
232            android:name="com.example.kpitrackerapp.ui.ChatActivity"
232-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:186:13-44
233            android:exported="false"
233-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:190:13-37
234            android:label="Chat"
234-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:187:13-33
235            android:parentActivityName="com.example.kpitrackerapp.ui.ChatListActivity"
235-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:188:13-62
236            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
236-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:189:13-67
237
238        <!-- Date Converter Activity -->
239        <activity
239-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:193:9-198:40
240            android:name="com.example.kpitrackerapp.ui.DateConverterActivity"
240-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:194:13-53
241            android:exported="false"
241-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:198:13-37
242            android:label="محول التاريخ"
242-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:195:13-41
243            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
243-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:196:13-55
244            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
244-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:197:13-67
245
246        <!-- Task Reminder Settings Activity -->
247        <activity
247-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:201:9-206:40
248            android:name="com.example.kpitrackerapp.ui.TaskReminderSettingsActivity"
248-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:202:13-60
249            android:exported="false"
249-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:206:13-37
250            android:label="Task Reminder Settings"
250-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:203:13-51
251            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
251-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:204:13-68
252            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
252-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:205:13-67
253
254        <!-- Auto Send Settings Activity -->
255        <activity
255-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:209:9-214:40
256            android:name="com.example.kpitrackerapp.ui.AutoSendSettingsActivity"
256-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:210:13-56
257            android:exported="false"
257-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:214:13-37
258            android:label="Auto Send Settings"
258-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:211:13-47
259            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
259-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:212:13-55
260            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
260-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:213:13-67
261
262        <!-- Advanced Task Activity -->
263        <activity
263-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:217:9-222:40
264            android:name="com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity"
264-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:218:13-59
265            android:exported="false"
265-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:222:13-37
266            android:label="إضافة مهمة متقدمة"
266-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:219:13-46
267            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
267-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:220:13-55
268            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
268-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:221:13-67
269
270        <!-- Modern Add Task Activity -->
271        <activity
271-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:225:9-230:40
272            android:name="com.example.kpitrackerapp.ui.ModernAddTaskActivity"
272-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:226:13-53
273            android:exported="false"
273-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:230:13-37
274            android:label="Add New Task"
274-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:227:13-41
275            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
275-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:228:13-55
276            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
276-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:229:13-67
277
278        <!-- Pomodoro Timer Activity -->
279        <activity
279-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:233:9-239:52
280            android:name="com.example.kpitrackerapp.ui.PomodoroTimerActivity"
280-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:234:13-53
281            android:exported="false"
281-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:238:13-37
282            android:label="Pomodoro Timer"
282-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:235:13-43
283            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
283-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:236:13-68
284            android:screenOrientation="portrait"
284-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:239:13-49
285            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
285-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:237:13-67
286
287        <!-- Add other activities, services, etc. here -->
288        <activity
288-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:242:9-245:58
289            android:name="com.example.kpitrackerapp.ui.NotificationsActivity"
289-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:243:13-53
290            android:exported="false"
290-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:244:13-37
291            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
291-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:245:13-55
292
293        <!-- Firebase Messaging Service -->
294        <service
294-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:248:9-254:19
295            android:name="com.example.kpitrackerapp.services.KPIFirebaseMessagingService"
295-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:249:13-65
296            android:exported="false" >
296-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:250:13-37
297            <intent-filter>
297-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:251:13-253:29
298                <action android:name="com.google.firebase.MESSAGING_EVENT" />
298-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:252:17-78
298-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:252:25-75
299            </intent-filter>
300        </service>
301
302        <!-- Firebase Messaging default notification icon -->
303        <meta-data
303-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:257:9-259:60
304            android:name="com.google.firebase.messaging.default_notification_icon"
304-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:258:13-83
305            android:resource="@drawable/ic_notification" />
305-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:259:13-57
306
307        <!-- Firebase Messaging default notification color -->
308        <meta-data
308-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:262:9-264:52
309            android:name="com.google.firebase.messaging.default_notification_color"
309-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:263:13-84
310            android:resource="@color/purple_500" />
310-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:264:13-49
311
312        <!-- FileProvider for sharing camera image URI -->
313        <provider
314            android:name="androidx.core.content.FileProvider"
314-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:268:13-62
315            android:authorities="com.example.kpitrackerapp.provider"
315-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:269:13-60
316            android:exported="false"
316-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:270:13-37
317            android:grantUriPermissions="true" >
317-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:271:13-47
318            <meta-data
318-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:272:13-274:54
319                android:name="android.support.FILE_PROVIDER_PATHS"
319-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:273:17-67
320                android:resource="@xml/file_paths" />
320-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:274:17-51
321        </provider>
322
323        <!-- Disable WorkManager automatic initialization since we use Configuration.Provider -->
324        <provider
325            android:name="androidx.startup.InitializationProvider"
325-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:279:13-67
326            android:authorities="com.example.kpitrackerapp.androidx-startup"
326-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:280:13-68
327            android:exported="false" >
327-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:281:13-37
328            <meta-data
328-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
329                android:name="androidx.emoji2.text.EmojiCompatInitializer"
329-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
330                android:value="androidx.startup" />
330-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
331            <meta-data
331-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
332                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
332-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
333                android:value="androidx.startup" />
333-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
334            <meta-data
334-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
335                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
335-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
336                android:value="androidx.startup" />
336-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
337        </provider>
338
339        <service
339-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
340            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
340-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
341            android:directBootAware="true"
341-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:17:13-43
342            android:exported="false" >
342-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
343            <meta-data
343-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
344                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
344-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
345                android:value="com.google.firebase.components.ComponentRegistrar" />
345-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
346            <meta-data
346-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
347                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
347-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
348                android:value="com.google.firebase.components.ComponentRegistrar" />
348-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
349            <meta-data
349-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:20:13-22:85
350                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
350-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:21:17-120
351                android:value="com.google.firebase.components.ComponentRegistrar" />
351-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:22:17-82
352        </service>
353
354        <provider
354-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:9:9-13:38
355            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
355-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:10:13-78
356            android:authorities="com.example.kpitrackerapp.mlkitinitprovider"
356-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:11:13-69
357            android:exported="false"
357-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:12:13-37
358            android:initOrder="99" />
358-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:13:13-35
359
360        <service
360-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:9:9-15:19
361            android:name="com.google.firebase.components.ComponentDiscoveryService"
361-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:10:13-84
362            android:directBootAware="true"
362-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
363            android:exported="false" >
363-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:11:13-37
364            <meta-data
364-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
365                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
365-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:13:17-127
366                android:value="com.google.firebase.components.ComponentRegistrar" />
366-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:14:17-82
367            <meta-data
367-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
368                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
368-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:30:17-120
369                android:value="com.google.firebase.components.ComponentRegistrar" />
369-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:31:17-82
370            <meta-data
370-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
371                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
371-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:33:17-109
372                android:value="com.google.firebase.components.ComponentRegistrar" />
372-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:34:17-82
373            <meta-data
373-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
374                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
374-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
375                android:value="com.google.firebase.components.ComponentRegistrar" />
375-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
376            <meta-data
376-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
377                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
377-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
378                android:value="com.google.firebase.components.ComponentRegistrar" />
378-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
379            <meta-data
379-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
380                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
380-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
381                android:value="com.google.firebase.components.ComponentRegistrar" />
381-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
382            <meta-data
382-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
383                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
383-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
384                android:value="com.google.firebase.components.ComponentRegistrar" />
384-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
385            <meta-data
385-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
386                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
386-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
387                android:value="com.google.firebase.components.ComponentRegistrar" />
387-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
388            <meta-data
388-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
389                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
389-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
390                android:value="com.google.firebase.components.ComponentRegistrar" />
390-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
391            <meta-data
391-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
392                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
392-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
393                android:value="com.google.firebase.components.ComponentRegistrar" />
393-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
394            <meta-data
394-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
395                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
395-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
396                android:value="com.google.firebase.components.ComponentRegistrar" />
396-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
397            <meta-data
397-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
398                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
398-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
399                android:value="com.google.firebase.components.ComponentRegistrar" />
399-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
400            <meta-data
400-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
401                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
401-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
402                android:value="com.google.firebase.components.ComponentRegistrar" />
402-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
403        </service>
404
405        <receiver
405-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
406            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
406-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
407            android:exported="true"
407-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
408            android:permission="com.google.android.c2dm.permission.SEND" >
408-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
409            <intent-filter>
409-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
410                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
410-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
410-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
411            </intent-filter>
412
413            <meta-data
413-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
414                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
414-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
415                android:value="true" />
415-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
416        </receiver>
417        <!--
418             FirebaseMessagingService performs security checks at runtime,
419             but set to not exported to explicitly avoid allowing another app to call it.
420        -->
421        <service
421-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
422            android:name="com.google.firebase.messaging.FirebaseMessagingService"
422-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
423            android:directBootAware="true"
423-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
424            android:exported="false" >
424-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
425            <intent-filter android:priority="-500" >
425-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:251:13-253:29
426                <action android:name="com.google.firebase.MESSAGING_EVENT" />
426-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:252:17-78
426-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:252:25-75
427            </intent-filter>
428        </service>
429
430        <activity
430-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
431            android:name="com.google.android.gms.common.api.GoogleApiActivity"
431-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
432            android:exported="false"
432-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
433            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
433-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
434
435        <property
435-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
436            android:name="android.adservices.AD_SERVICES_CONFIG"
436-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
437            android:resource="@xml/ga_ad_services_config" />
437-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
438
439        <provider
439-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
440            android:name="com.google.firebase.provider.FirebaseInitProvider"
440-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
441            android:authorities="com.example.kpitrackerapp.firebaseinitprovider"
441-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
442            android:directBootAware="true"
442-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
443            android:exported="false"
443-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
444            android:initOrder="100" />
444-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
445
446        <service
446-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
447            android:name="androidx.room.MultiInstanceInvalidationService"
447-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
448            android:directBootAware="true"
448-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
449            android:exported="false" />
449-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
450        <service
450-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
451            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
451-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
452            android:directBootAware="false"
452-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
453            android:enabled="@bool/enable_system_alarm_service_default"
453-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
454            android:exported="false" />
454-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
455        <service
455-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
456            android:name="androidx.work.impl.background.systemjob.SystemJobService"
456-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
457            android:directBootAware="false"
457-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
458            android:enabled="@bool/enable_system_job_service_default"
458-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
459            android:exported="true"
459-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
460            android:permission="android.permission.BIND_JOB_SERVICE" />
460-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
461        <service
461-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
462            android:name="androidx.work.impl.foreground.SystemForegroundService"
462-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
463            android:directBootAware="false"
463-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
464            android:enabled="@bool/enable_system_foreground_service_default"
464-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
465            android:exported="false" />
465-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
466
467        <receiver
467-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
468            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
468-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
469            android:directBootAware="false"
469-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
470            android:enabled="true"
470-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
471            android:exported="false" />
471-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
472        <receiver
472-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
473            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
473-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
474            android:directBootAware="false"
474-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
475            android:enabled="false"
475-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
476            android:exported="false" >
476-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
477            <intent-filter>
477-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
478                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
478-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
478-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
479                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
479-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
479-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
480            </intent-filter>
481        </receiver>
482        <receiver
482-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
483            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
483-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
484            android:directBootAware="false"
484-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
485            android:enabled="false"
485-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
486            android:exported="false" >
486-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
487            <intent-filter>
487-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
488                <action android:name="android.intent.action.BATTERY_OKAY" />
488-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
488-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
489                <action android:name="android.intent.action.BATTERY_LOW" />
489-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
489-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
490            </intent-filter>
491        </receiver>
492        <receiver
492-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
493            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
493-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
494            android:directBootAware="false"
494-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
495            android:enabled="false"
495-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
496            android:exported="false" >
496-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
497            <intent-filter>
497-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
498                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
498-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
498-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
499                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
499-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
499-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
500            </intent-filter>
501        </receiver>
502        <receiver
502-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
503            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
503-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
504            android:directBootAware="false"
504-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
505            android:enabled="false"
505-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
506            android:exported="false" >
506-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
507            <intent-filter>
507-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
508                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
508-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
508-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
509            </intent-filter>
510        </receiver>
511        <receiver
511-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
512            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
512-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
513            android:directBootAware="false"
513-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
514            android:enabled="false"
514-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
515            android:exported="false" >
515-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
516            <intent-filter>
516-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
517                <action android:name="android.intent.action.BOOT_COMPLETED" />
517-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
517-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
518                <action android:name="android.intent.action.TIME_SET" />
518-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
518-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
519                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
519-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
519-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
520            </intent-filter>
521        </receiver>
522        <receiver
522-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
523            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
523-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
524            android:directBootAware="false"
524-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
525            android:enabled="@bool/enable_system_alarm_service_default"
525-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
526            android:exported="false" >
526-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
527            <intent-filter>
527-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
528                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
528-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
528-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
529            </intent-filter>
530        </receiver>
531        <receiver
531-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
532            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
532-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
533            android:directBootAware="false"
533-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
534            android:enabled="true"
534-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
535            android:exported="true"
535-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
536            android:permission="android.permission.DUMP" >
536-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
537            <intent-filter>
537-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
538                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
538-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
538-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
539            </intent-filter>
540        </receiver>
541        <receiver
541-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
542            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
542-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
543            android:enabled="true"
543-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
544            android:exported="false" >
544-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
545        </receiver>
546
547        <service
547-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
548            android:name="com.google.android.gms.measurement.AppMeasurementService"
548-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
549            android:enabled="true"
549-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
550            android:exported="false" />
550-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
551        <service
551-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
552            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
552-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
553            android:enabled="true"
553-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
554            android:exported="false"
554-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
555            android:permission="android.permission.BIND_JOB_SERVICE" />
555-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
556
557        <uses-library
557-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
558            android:name="android.ext.adservices"
558-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
559            android:required="false" />
559-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
560
561        <meta-data
561-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
562            android:name="com.google.android.gms.version"
562-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
563            android:value="@integer/google_play_services_version" />
563-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
564
565        <receiver
565-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
566            android:name="androidx.profileinstaller.ProfileInstallReceiver"
566-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
567            android:directBootAware="false"
567-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
568            android:enabled="true"
568-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
569            android:exported="true"
569-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
570            android:permission="android.permission.DUMP" >
570-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
571            <intent-filter>
571-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
572                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
572-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
572-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
573            </intent-filter>
574            <intent-filter>
574-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
575                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
575-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
575-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
576            </intent-filter>
577            <intent-filter>
577-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
578                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
578-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
578-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
579            </intent-filter>
580            <intent-filter>
580-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
581                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
581-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
581-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
582            </intent-filter>
583        </receiver>
584
585        <service
585-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
586            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
586-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
587            android:exported="false" >
587-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
588            <meta-data
588-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
589                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
589-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
590                android:value="cct" />
590-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
591        </service>
592        <service
592-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
593            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
593-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
594            android:exported="false"
594-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
595            android:permission="android.permission.BIND_JOB_SERVICE" >
595-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
596        </service>
597
598        <receiver
598-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
599            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
599-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
600            android:exported="false" />
600-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
601
602        <activity
602-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
603            android:name="com.journeyapps.barcodescanner.CaptureActivity"
603-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
604            android:clearTaskOnLaunch="true"
604-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
605            android:screenOrientation="sensorLandscape"
605-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
606            android:stateNotNeeded="true"
606-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
607            android:theme="@style/zxing_CaptureTheme"
607-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
608            android:windowSoftInputMode="stateAlwaysHidden" />
608-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
609    </application>
610
611</manifest>
