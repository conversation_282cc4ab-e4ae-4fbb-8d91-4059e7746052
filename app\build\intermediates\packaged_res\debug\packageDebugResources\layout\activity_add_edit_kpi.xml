<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/soft_lavender_background"
    tools:context=".ui.AddEditKpiActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_accent"
        android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="إضافة مهمة جديدة"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Quick Actions Bottom Bar -->
    <com.google.android.material.bottomappbar.BottomAppBar
        android:id="@+id/bottomAppBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:backgroundTint="@color/white"
        app:elevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:padding="8dp">

            <ImageButton
                android:id="@+id/btnVoiceNote"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_baseline_mic_24"
                android:contentDescription="تسجيل صوتي"
                app:tint="@color/purple_accent" />

            <ImageButton
                android:id="@+id/btnCamera"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_baseline_camera_alt_24"
                android:contentDescription="كاميرا"
                app:tint="@color/purple_accent" />

            <ImageButton
                android:id="@+id/btnLocation"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_baseline_location_on_24"
                android:contentDescription="موقع"
                app:tint="@color/purple_accent" />

            <ImageButton
                android:id="@+id/btnTimer"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_baseline_timer_24"
                android:contentDescription="مؤقت"
                app:tint="@color/purple_accent" />

            <ImageButton
                android:id="@+id/btnTemplate"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_baseline_content_copy_24"
                android:contentDescription="قوالب"
                app:tint="@color/purple_accent" />

        </LinearLayout>
    </com.google.android.material.bottomappbar.BottomAppBar>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:fillViewport="true"
        android:layout_marginBottom="80dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 🤖 AI Suggestions Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardAiSuggestions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/ai_suggestion_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🤖 اقتراحات ذكية"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💡 ننصح بتنفيذ هذه المهمة في الصباح"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="⏱️ الوقت المتوقع: 45 دقيقة"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔗 مهام مشابهة: تحديث التقارير"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- 📋 Quick Templates -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📋 قوالب جاهزة"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/templateMeeting"
                        android:layout_width="120dp"
                        android:layout_height="100dp"
                        android:layout_marginEnd="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="💼"
                                android:textSize="32sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="اجتماع"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/templateStudy"
                        android:layout_width="120dp"
                        android:layout_height="100dp"
                        android:layout_marginEnd="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📚"
                                android:textSize="32sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="دراسة"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/templateCall"
                        android:layout_width="120dp"
                        android:layout_height="100dp"
                        android:layout_marginEnd="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📞"
                                android:textSize="32sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="مكالمة"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/templateExercise"
                        android:layout_width="120dp"
                        android:layout_height="100dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🏃‍♂️"
                                android:textSize="32sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="رياضة"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>
            </HorizontalScrollView>

            <!-- ✅ Section 1: Basic Information (Always Visible) -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="✅ المعلومات الأساسية"
                        android:textStyle="bold"
                        android:textSize="18sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_up_24"
                        android:drawableTint="@color/purple_accent" />

                    <!-- Task Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskNameInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="📝 اسم المهمة"
                        app:startIconDrawable="@drawable/ic_baseline_task_24"
                        app:startIconTint="@color/purple_accent"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskNameEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapSentences" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Task Description -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskDescriptionInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="📄 وصف المهمة (اختياري)"
                        app:startIconDrawable="@drawable/ic_baseline_description_24"
                        app:startIconTint="@color/purple_accent"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskDescriptionEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:lines="3"
                            android:maxLines="5" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Expiration Date -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskExpirationDateInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="📅 تاريخ الانتهاء"
                        app:startIconDrawable="@drawable/ic_baseline_calendar_today_24"
                        app:startIconTint="@color/purple_accent"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:endIconMode="custom"
                        app:endIconDrawable="@drawable/ic_baseline_calendar_today_24"
                        app:endIconContentDescription="Select date">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskExpirationDateEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true"
                            android:inputType="date" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Expiration Time (Optional) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskExpirationTimeInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="⏰ وقت الانتهاء (اختياري)"
                        app:startIconDrawable="@drawable/ic_baseline_access_time_24"
                        app:startIconTint="@color/purple_accent"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:endIconMode="custom"
                        app:endIconDrawable="@drawable/ic_baseline_access_time_24"
                        app:endIconContentDescription="Select time">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskExpirationTimeEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true"
                            android:inputType="time" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Priority -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="⭐ الأولوية"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp"
                            android:drawableStart="@drawable/ic_baseline_priority_high_24"
                            android:drawableTint="@color/purple_accent"
                            android:drawablePadding="8dp"
                            android:gravity="center_vertical" />

                        <LinearLayout
                            android:id="@+id/taskPrioritySelector"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:background="@drawable/rounded_background"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="12dp"
                            android:layout_marginBottom="8dp"
                            android:clickable="true"
                            android:focusable="true">

                            <TextView
                                android:id="@+id/taskPriorityText"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="🟡 متوسطة - مهمة عادية"
                                android:textSize="16sp"
                                android:textColor="@color/text_primary" />

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_baseline_keyboard_arrow_down_24"
                                app:tint="@color/purple_accent" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Category -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="📂 الفئة"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp"
                            android:drawableStart="@drawable/ic_baseline_category_24"
                            android:drawableTint="@color/purple_accent"
                            android:drawablePadding="8dp"
                            android:gravity="center_vertical" />

                        <LinearLayout
                            android:id="@+id/taskCategorySelector"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:background="@drawable/rounded_background"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="12dp"
                            android:layout_marginBottom="8dp"
                            android:clickable="true"
                            android:focusable="true">

                            <TextView
                                android:id="@+id/taskCategoryText"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="💼 عمل - مهام العمل والوظيفة"
                                android:textSize="16sp"
                                android:textColor="@color/text_primary" />

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_baseline_keyboard_arrow_down_24"
                                app:tint="@color/purple_accent" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Estimated Time -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/taskEstimatedTimeInputLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="⏱️ الوقت المتوقع (بالساعات)"
                        app:startIconDrawable="@drawable/ic_baseline_timer_24"
                        app:startIconTint="@color/purple_accent"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/taskEstimatedTimeEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:text="1.0" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- 📊 Section 2: Advanced Details (Collapsible) -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tvAdvancedDetailsHeader"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📊 التفاصيل المتقدمة"
                        android:textStyle="bold"
                        android:textSize="18sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground"
                        android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_down_24"
                        android:drawableTint="@color/purple_accent" />

                    <LinearLayout
                        android:id="@+id/layoutAdvancedDetails"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- Energy Level -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="⚡ مستوى الطاقة المطلوب"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp" />

                        <com.google.android.material.chip.ChipGroup
                            android:id="@+id/chipGroupEnergyLevel"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            app:singleSelection="true">

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipEnergyLow"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🔋 منخفض"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipEnergyMedium"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⚡ متوسط"
                                android:checkable="true"
                                android:checked="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipEnergyHigh"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🚀 عالي"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                        </com.google.android.material.chip.ChipGroup>

                        <!-- Progress Slider -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📊 التقدم: "
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/tvProgressValue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/purple_accent"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <com.google.android.material.slider.Slider
                            android:id="@+id/sliderProgress"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:valueFrom="0"
                            android:valueTo="100"
                            android:stepSize="5"
                            android:value="0"
                            android:layout_marginBottom="16dp"
                            app:thumbColor="@color/purple_accent"
                            app:trackColorActive="@color/purple_accent" />

                        <!-- Location -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/taskLocationInputLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:hint="📍 الموقع (اختياري)"
                            app:startIconDrawable="@drawable/ic_baseline_location_on_24"
                            app:startIconTint="@color/purple_accent"
                            app:endIconMode="custom"
                            app:endIconDrawable="@drawable/ic_baseline_my_location_24"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/taskLocationEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Context -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🏠 السياق"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp" />

                        <com.google.android.material.chip.ChipGroup
                            android:id="@+id/chipGroupContext"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            app:singleSelection="true">

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipContextHome"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🏠 في المنزل"
                                android:checkable="true"
                                android:checked="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipContextOffice"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🏢 في المكتب"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipContextOnline"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📱 أونلاين"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipContextTravel"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🚗 في الطريق"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                        </com.google.android.material.chip.ChipGroup>

                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- ⚙️ Section 3: Settings & Customization (Collapsible) -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tvSettingsHeader"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="⚙️ الإعدادات والتخصيص"
                        android:textStyle="bold"
                        android:textSize="18sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground"
                        android:drawableEnd="@drawable/ic_baseline_keyboard_arrow_down_24"
                        android:drawableTint="@color/purple_accent" />

                    <LinearLayout
                        android:id="@+id/layoutSettings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- Reminder Settings -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/taskReminderInputLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:hint="🔔 التذكير (أيام قبل الموعد)"
                            app:startIconDrawable="@drawable/ic_baseline_notifications_24"
                            app:startIconTint="@color/purple_accent"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.MaterialAutoCompleteTextView
                                android:id="@+id/taskReminderAutoCompleteTextView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:cursorVisible="false"
                                android:text="🔕 لا يوجد تذكير" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Recurring Task -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="16dp"
                            android:gravity="center_vertical">

                            <com.google.android.material.materialswitch.MaterialSwitch
                                android:id="@+id/switchRecurring"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="12dp" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="🔄 مهمة متكررة"
                                android:textSize="16sp"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>

                        <!-- Notifications -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="16dp"
                            android:gravity="center_vertical">

                            <com.google.android.material.materialswitch.MaterialSwitch
                                android:id="@+id/switchNotifications"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="12dp"
                                android:checked="true" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="🔔 تفعيل الإشعارات"
                                android:textSize="16sp"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>

                        <!-- Break Down Task -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="16dp"
                            android:gravity="center_vertical">

                            <com.google.android.material.materialswitch.MaterialSwitch
                                android:id="@+id/switchBreakDown"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="12dp" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="🧩 تقسيم إلى مهام فرعية تلقائياً"
                                android:textSize="16sp"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>

                        <!-- Task Color -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🎨 لون المهمة"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="16dp"
                            android:gravity="center">

                            <View
                                android:id="@+id/colorRed"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/color_selector_red"
                                android:clickable="true"
                                android:focusable="true" />

                            <View
                                android:id="@+id/colorBlue"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/color_selector_blue"
                                android:clickable="true"
                                android:focusable="true" />

                            <View
                                android:id="@+id/colorGreen"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/color_selector_green"
                                android:clickable="true"
                                android:focusable="true" />

                            <View
                                android:id="@+id/colorOrange"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/color_selector_orange"
                                android:clickable="true"
                                android:focusable="true" />

                            <View
                                android:id="@+id/colorPurple"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:background="@drawable/color_selector_purple"
                                android:clickable="true"
                                android:focusable="true" />

                        </LinearLayout>

                        <!-- Task Icon -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="📱 أيقونة المهمة"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp" />

                        <com.google.android.material.chip.ChipGroup
                            android:id="@+id/chipGroupTaskIcon"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            app:singleSelection="true">

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipIconWork"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="💼"
                                android:checkable="true"
                                android:checked="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipIconStudy"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📚"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipIconSport"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🏃‍♂️"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipIconHealth"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🍎"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipIconIdea"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="💡"
                                android:checkable="true"
                                style="@style/Widget.App.Chip.Advanced" />

                        </com.google.android.material.chip.ChipGroup>

                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="16dp">

                <!-- Save as Draft -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSaveDraft"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="💾 حفظ كمسودة"
                    android:textColor="@color/purple_accent"
                    app:strokeColor="@color/purple_accent"
                    app:cornerRadius="8dp" />

                <!-- Add and Create Another -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAddAnother"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="➕ إضافة وإنشاء أخرى"
                    android:textColor="@color/purple_accent"
                    app:strokeColor="@color/purple_accent"
                    app:cornerRadius="8dp" />

            </LinearLayout>

            <!-- Main Add Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/addTaskButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="✅ إضافة المهمة"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/purple_accent"
                app:cornerRadius="12dp"
                android:elevation="8dp"
                app:icon="@drawable/ic_baseline_add_24"
                app:iconGravity="textStart"
                app:iconTint="@color/white" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
