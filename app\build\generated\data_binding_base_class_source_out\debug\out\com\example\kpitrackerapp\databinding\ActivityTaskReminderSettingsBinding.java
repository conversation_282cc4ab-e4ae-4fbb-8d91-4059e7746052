// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTaskReminderSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton buttonCheckApps;

  @NonNull
  public final MaterialButton buttonSave;

  @NonNull
  public final MaterialButton buttonTestEmail;

  @NonNull
  public final MaterialButton buttonTestWhatsapp;

  @NonNull
  public final TextInputEditText editTextEmail;

  @NonNull
  public final TextInputEditText editTextPhone;

  @NonNull
  public final RadioButton radio1Day;

  @NonNull
  public final RadioButton radio1Week;

  @NonNull
  public final RadioButton radio3Days;

  @NonNull
  public final RadioButton radioNoReminder;

  @NonNull
  public final SwitchMaterial switchEmailReminders;

  @NonNull
  public final SwitchMaterial switchLocalNotifications;

  @NonNull
  public final SwitchMaterial switchWhatsappReminders;

  @NonNull
  public final Toolbar toolbar;

  private ActivityTaskReminderSettingsBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton buttonCheckApps, @NonNull MaterialButton buttonSave,
      @NonNull MaterialButton buttonTestEmail, @NonNull MaterialButton buttonTestWhatsapp,
      @NonNull TextInputEditText editTextEmail, @NonNull TextInputEditText editTextPhone,
      @NonNull RadioButton radio1Day, @NonNull RadioButton radio1Week,
      @NonNull RadioButton radio3Days, @NonNull RadioButton radioNoReminder,
      @NonNull SwitchMaterial switchEmailReminders,
      @NonNull SwitchMaterial switchLocalNotifications,
      @NonNull SwitchMaterial switchWhatsappReminders, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonCheckApps = buttonCheckApps;
    this.buttonSave = buttonSave;
    this.buttonTestEmail = buttonTestEmail;
    this.buttonTestWhatsapp = buttonTestWhatsapp;
    this.editTextEmail = editTextEmail;
    this.editTextPhone = editTextPhone;
    this.radio1Day = radio1Day;
    this.radio1Week = radio1Week;
    this.radio3Days = radio3Days;
    this.radioNoReminder = radioNoReminder;
    this.switchEmailReminders = switchEmailReminders;
    this.switchLocalNotifications = switchLocalNotifications;
    this.switchWhatsappReminders = switchWhatsappReminders;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTaskReminderSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTaskReminderSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_task_reminder_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTaskReminderSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonCheckApps;
      MaterialButton buttonCheckApps = ViewBindings.findChildViewById(rootView, id);
      if (buttonCheckApps == null) {
        break missingId;
      }

      id = R.id.buttonSave;
      MaterialButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.buttonTestEmail;
      MaterialButton buttonTestEmail = ViewBindings.findChildViewById(rootView, id);
      if (buttonTestEmail == null) {
        break missingId;
      }

      id = R.id.buttonTestWhatsapp;
      MaterialButton buttonTestWhatsapp = ViewBindings.findChildViewById(rootView, id);
      if (buttonTestWhatsapp == null) {
        break missingId;
      }

      id = R.id.editTextEmail;
      TextInputEditText editTextEmail = ViewBindings.findChildViewById(rootView, id);
      if (editTextEmail == null) {
        break missingId;
      }

      id = R.id.editTextPhone;
      TextInputEditText editTextPhone = ViewBindings.findChildViewById(rootView, id);
      if (editTextPhone == null) {
        break missingId;
      }

      id = R.id.radio1Day;
      RadioButton radio1Day = ViewBindings.findChildViewById(rootView, id);
      if (radio1Day == null) {
        break missingId;
      }

      id = R.id.radio1Week;
      RadioButton radio1Week = ViewBindings.findChildViewById(rootView, id);
      if (radio1Week == null) {
        break missingId;
      }

      id = R.id.radio3Days;
      RadioButton radio3Days = ViewBindings.findChildViewById(rootView, id);
      if (radio3Days == null) {
        break missingId;
      }

      id = R.id.radioNoReminder;
      RadioButton radioNoReminder = ViewBindings.findChildViewById(rootView, id);
      if (radioNoReminder == null) {
        break missingId;
      }

      id = R.id.switchEmailReminders;
      SwitchMaterial switchEmailReminders = ViewBindings.findChildViewById(rootView, id);
      if (switchEmailReminders == null) {
        break missingId;
      }

      id = R.id.switchLocalNotifications;
      SwitchMaterial switchLocalNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchLocalNotifications == null) {
        break missingId;
      }

      id = R.id.switchWhatsappReminders;
      SwitchMaterial switchWhatsappReminders = ViewBindings.findChildViewById(rootView, id);
      if (switchWhatsappReminders == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityTaskReminderSettingsBinding((LinearLayout) rootView, buttonCheckApps,
          buttonSave, buttonTestEmail, buttonTestWhatsapp, editTextEmail, editTextPhone, radio1Day,
          radio1Week, radio3Days, radioNoReminder, switchEmailReminders, switchLocalNotifications,
          switchWhatsappReminders, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
