<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_list" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_product_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_product_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="66"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="57" startOffset="20" endLine="66" endOffset="71"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="73" startOffset="12" endLine="78" endOffset="55"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="81" startOffset="12" endLine="90" endOffset="43"/></Target><Target id="@+id/addProductFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="97" startOffset="4" endLine="105" endOffset="51"/></Target></Targets></Layout>