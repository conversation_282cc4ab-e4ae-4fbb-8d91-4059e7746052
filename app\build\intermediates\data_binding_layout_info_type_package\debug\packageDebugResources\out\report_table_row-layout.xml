<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="report_table_row" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\report_table_row.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/report_table_row_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="236" endOffset="14"/></Target><Target id="@+id/reportUserNameTextView" view="TextView"><Expressions/><location startLine="11" startOffset="4" endLine="16" endOffset="32"/></Target><Target id="@+id/reportKpiNameTextView" view="TextView"><Expressions/><location startLine="18" startOffset="4" endLine="23" endOffset="31"/></Target><Target id="@+id/reportDailyRow" view="LinearLayout"><Expressions/><location startLine="67" startOffset="4" endLine="105" endOffset="18"/></Target><Target id="@+id/reportDailyTarget" view="TextView"><Expressions/><location startLine="81" startOffset="8" endLine="88" endOffset="29"/></Target><Target id="@+id/reportDailyAchieved" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="96" endOffset="29"/></Target><Target id="@+id/reportDailyPercentage" view="TextView"><Expressions/><location startLine="97" startOffset="8" endLine="104" endOffset="29"/></Target><Target id="@+id/reportMonthlyRow" view="LinearLayout"><Expressions/><location startLine="108" startOffset="4" endLine="146" endOffset="18"/></Target><Target id="@+id/reportMonthlyTarget" view="TextView"><Expressions/><location startLine="122" startOffset="8" endLine="129" endOffset="31"/></Target><Target id="@+id/reportMonthlyAchieved" view="TextView"><Expressions/><location startLine="130" startOffset="8" endLine="137" endOffset="31"/></Target><Target id="@+id/reportMonthlyPercentage" view="TextView"><Expressions/><location startLine="138" startOffset="8" endLine="145" endOffset="29"/></Target><Target id="@+id/reportQuarterlyRow" view="LinearLayout"><Expressions/><location startLine="149" startOffset="5" endLine="187" endOffset="18"/></Target><Target id="@+id/reportQuarterlyTarget" view="TextView"><Expressions/><location startLine="163" startOffset="8" endLine="170" endOffset="32"/></Target><Target id="@+id/reportQuarterlyAchieved" view="TextView"><Expressions/><location startLine="171" startOffset="8" endLine="178" endOffset="32"/></Target><Target id="@+id/reportQuarterlyPercentage" view="TextView"><Expressions/><location startLine="179" startOffset="8" endLine="186" endOffset="29"/></Target><Target id="@+id/reportAnnualRow" view="LinearLayout"><Expressions/><location startLine="190" startOffset="5" endLine="228" endOffset="18"/></Target><Target id="@+id/reportAnnualTarget" view="TextView"><Expressions/><location startLine="204" startOffset="8" endLine="211" endOffset="33"/></Target><Target id="@+id/reportAnnualAchieved" view="TextView"><Expressions/><location startLine="212" startOffset="8" endLine="219" endOffset="32"/></Target><Target id="@+id/reportAnnualPercentage" view="TextView"><Expressions/><location startLine="220" startOffset="8" endLine="227" endOffset="29"/></Target></Targets></Layout>