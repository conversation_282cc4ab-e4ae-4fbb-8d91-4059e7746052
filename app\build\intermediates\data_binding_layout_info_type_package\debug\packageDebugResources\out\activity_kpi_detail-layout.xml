<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_kpi_detail" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_kpi_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_kpi_detail_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="581" endOffset="51"/></Target><Target id="@+id/detailKpiNameTextView" view="TextView"><Expressions/><location startLine="11" startOffset="4" endLine="19" endOffset="33"/></Target><Target id="@+id/targetsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="22" startOffset="4" endLine="212" endOffset="55"/></Target><Target id="@+id/targetsTitleTextView" view="TextView"><Expressions/><location startLine="48" startOffset="16" endLine="56" endOffset="61"/></Target><Target id="@+id/progressSummaryTextView" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="66" endOffset="47"/></Target><Target id="@+id/detailKpiTargetValueTextView" view="TextView"><Expressions/><location startLine="85" startOffset="16" endLine="93" endOffset="49"/></Target><Target id="@+id/annualProgressPercentageTextView" view="TextView"><Expressions/><location startLine="96" startOffset="16" endLine="103" endOffset="38"/></Target><Target id="@+id/detailKpiMonthlyTargetValueTextView" view="TextView"><Expressions/><location startLine="122" startOffset="16" endLine="130" endOffset="49"/></Target><Target id="@+id/monthlyProgressPercentageTextView" view="TextView"><Expressions/><location startLine="133" startOffset="16" endLine="140" endOffset="38"/></Target><Target id="@+id/detailKpiDailyTargetValueTextView" view="TextView"><Expressions/><location startLine="159" startOffset="16" endLine="167" endOffset="45"/></Target><Target id="@+id/dailyProgressPercentageTextView" view="TextView"><Expressions/><location startLine="170" startOffset="16" endLine="177" endOffset="38"/></Target><Target id="@+id/lastEntryDateTextView" view="TextView"><Expressions/><location startLine="198" startOffset="16" endLine="206" endOffset="57"/></Target><Target id="@+id/scrollView" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="215" startOffset="4" endLine="579" endOffset="43"/></Target><Target id="@+id/progressIndicatorsLayout" view="LinearLayout"><Expressions/><location startLine="231" startOffset="12" endLine="372" endOffset="26"/></Target><Target id="@+id/dailyProgressLayout" view="LinearLayout"><Expressions/><location startLine="244" startOffset="16" endLine="284" endOffset="30"/></Target><Target id="@+id/dailyProgressIndicatorContainer" view="FrameLayout"><Expressions/><location startLine="254" startOffset="20" endLine="277" endOffset="33"/></Target><Target id="@+id/dailyCircularProgressIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="259" startOffset="24" endLine="267" endOffset="78"/></Target><Target id="@+id/dailyProgressPercentageText" view="TextView"><Expressions/><location startLine="269" startOffset="24" endLine="276" endOffset="45"/></Target><Target id="@+id/monthlyProgressLayout" view="LinearLayout"><Expressions/><location startLine="287" startOffset="16" endLine="327" endOffset="30"/></Target><Target id="@+id/monthlyProgressIndicatorContainer" view="FrameLayout"><Expressions/><location startLine="297" startOffset="20" endLine="320" endOffset="33"/></Target><Target id="@+id/monthlyCircularProgressIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="302" startOffset="24" endLine="310" endOffset="78"/></Target><Target id="@+id/monthlyProgressPercentageText" view="TextView"><Expressions/><location startLine="312" startOffset="24" endLine="319" endOffset="46"/></Target><Target id="@+id/annualProgressLayout" view="LinearLayout"><Expressions/><location startLine="330" startOffset="17" endLine="370" endOffset="30"/></Target><Target id="@+id/annualProgressIndicatorContainer" view="FrameLayout"><Expressions/><location startLine="339" startOffset="20" endLine="363" endOffset="33"/></Target><Target id="@+id/annualCircularProgressIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="344" startOffset="24" endLine="353" endOffset="78"/></Target><Target id="@+id/annualProgressPercentageText" view="TextView"><Expressions/><location startLine="355" startOffset="24" endLine="362" endOffset="46"/></Target><Target id="@+id/quickStatsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="375" startOffset="12" endLine="486" endOffset="63"/></Target><Target id="@+id/detailKpiProgressLabelTextView" view="TextView"><Expressions/><location startLine="411" startOffset="24" endLine="418" endOffset="69"/></Target><Target id="@+id/detailKpiCurrentValueTextView" view="TextView"><Expressions/><location startLine="420" startOffset="24" endLine="427" endOffset="50"/></Target><Target id="@+id/thisWeekProgressTextView" view="TextView"><Expressions/><location startLine="447" startOffset="24" endLine="454" endOffset="48"/></Target><Target id="@+id/averagePerDayTextView" view="TextView"><Expressions/><location startLine="473" startOffset="24" endLine="480" endOffset="46"/></Target><Target id="@+id/addProgressButtonLayout" view="LinearLayout"><Expressions/><location startLine="489" startOffset="12" endLine="540" endOffset="26"/></Target><Target id="@+id/quickAdd10Button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="501" startOffset="16" endLine="511" endOffset="72"/></Target><Target id="@+id/quickAdd50Button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="513" startOffset="16" endLine="523" endOffset="72"/></Target><Target id="@+id/addProgressButton" view="Button"><Expressions/><location startLine="526" startOffset="16" endLine="538" endOffset="57"/></Target><Target id="@+id/lineChartTitle" view="TextView"><Expressions/><location startLine="543" startOffset="12" endLine="553" endOffset="83"/></Target><Target id="@+id/monthFilterSpinner" view="Spinner"><Expressions/><location startLine="556" startOffset="12" endLine="564" endOffset="59"/></Target><Target id="@+id/kpiLineChart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="566" startOffset="12" endLine="574" endOffset="64"/></Target></Targets></Layout>