// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.SwitchCompat;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTaskManagementBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayoutTaskManagement;

  @NonNull
  public final ImageButton btnFilter;

  @NonNull
  public final ImageButton btnSearch;

  @NonNull
  public final ImageButton btnSortTasks;

  @NonNull
  public final ImageButton btnViewMode;

  @NonNull
  public final MaterialCardView cardMatrixToggle;

  @NonNull
  public final MaterialCardView cardQuickStats;

  @NonNull
  public final Chip chipAll;

  @NonNull
  public final Chip chipCompleted;

  @NonNull
  public final ChipGroup chipGroupFilters;

  @NonNull
  public final Chip chipOverdue;

  @NonNull
  public final Chip chipThisWeek;

  @NonNull
  public final Chip chipToday;

  @NonNull
  public final Chip chipUrgent;

  @NonNull
  public final ExtendedFloatingActionButton fabAddTask;

  @NonNull
  public final LinearLayout layoutEisenhowerMatrix;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final ProgressBar progressOverall;

  @NonNull
  public final RecyclerView rvNotUrgentImportant;

  @NonNull
  public final RecyclerView rvNotUrgentNotImportant;

  @NonNull
  public final RecyclerView rvTasks;

  @NonNull
  public final RecyclerView rvUrgentImportant;

  @NonNull
  public final RecyclerView rvUrgentNotImportant;

  @NonNull
  public final HorizontalScrollView scrollViewFilters;

  @NonNull
  public final SearchView searchView;

  @NonNull
  public final SwitchCompat switchMatrixView;

  @NonNull
  public final MaterialToolbar toolbarTaskManagement;

  @NonNull
  public final TextView tvCompletionRate;

  @NonNull
  public final TextView tvNotUrgentImportantCount;

  @NonNull
  public final TextView tvNotUrgentNotImportantCount;

  @NonNull
  public final TextView tvTodayTasks;

  @NonNull
  public final TextView tvTotalTasks;

  @NonNull
  public final TextView tvUrgentImportantCount;

  @NonNull
  public final TextView tvUrgentNotImportantCount;

  @NonNull
  public final TextView tvUrgentTasks;

  private ActivityTaskManagementBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayoutTaskManagement, @NonNull ImageButton btnFilter,
      @NonNull ImageButton btnSearch, @NonNull ImageButton btnSortTasks,
      @NonNull ImageButton btnViewMode, @NonNull MaterialCardView cardMatrixToggle,
      @NonNull MaterialCardView cardQuickStats, @NonNull Chip chipAll, @NonNull Chip chipCompleted,
      @NonNull ChipGroup chipGroupFilters, @NonNull Chip chipOverdue, @NonNull Chip chipThisWeek,
      @NonNull Chip chipToday, @NonNull Chip chipUrgent,
      @NonNull ExtendedFloatingActionButton fabAddTask,
      @NonNull LinearLayout layoutEisenhowerMatrix, @NonNull LinearLayout layoutEmptyState,
      @NonNull ProgressBar progressOverall, @NonNull RecyclerView rvNotUrgentImportant,
      @NonNull RecyclerView rvNotUrgentNotImportant, @NonNull RecyclerView rvTasks,
      @NonNull RecyclerView rvUrgentImportant, @NonNull RecyclerView rvUrgentNotImportant,
      @NonNull HorizontalScrollView scrollViewFilters, @NonNull SearchView searchView,
      @NonNull SwitchCompat switchMatrixView, @NonNull MaterialToolbar toolbarTaskManagement,
      @NonNull TextView tvCompletionRate, @NonNull TextView tvNotUrgentImportantCount,
      @NonNull TextView tvNotUrgentNotImportantCount, @NonNull TextView tvTodayTasks,
      @NonNull TextView tvTotalTasks, @NonNull TextView tvUrgentImportantCount,
      @NonNull TextView tvUrgentNotImportantCount, @NonNull TextView tvUrgentTasks) {
    this.rootView = rootView;
    this.appBarLayoutTaskManagement = appBarLayoutTaskManagement;
    this.btnFilter = btnFilter;
    this.btnSearch = btnSearch;
    this.btnSortTasks = btnSortTasks;
    this.btnViewMode = btnViewMode;
    this.cardMatrixToggle = cardMatrixToggle;
    this.cardQuickStats = cardQuickStats;
    this.chipAll = chipAll;
    this.chipCompleted = chipCompleted;
    this.chipGroupFilters = chipGroupFilters;
    this.chipOverdue = chipOverdue;
    this.chipThisWeek = chipThisWeek;
    this.chipToday = chipToday;
    this.chipUrgent = chipUrgent;
    this.fabAddTask = fabAddTask;
    this.layoutEisenhowerMatrix = layoutEisenhowerMatrix;
    this.layoutEmptyState = layoutEmptyState;
    this.progressOverall = progressOverall;
    this.rvNotUrgentImportant = rvNotUrgentImportant;
    this.rvNotUrgentNotImportant = rvNotUrgentNotImportant;
    this.rvTasks = rvTasks;
    this.rvUrgentImportant = rvUrgentImportant;
    this.rvUrgentNotImportant = rvUrgentNotImportant;
    this.scrollViewFilters = scrollViewFilters;
    this.searchView = searchView;
    this.switchMatrixView = switchMatrixView;
    this.toolbarTaskManagement = toolbarTaskManagement;
    this.tvCompletionRate = tvCompletionRate;
    this.tvNotUrgentImportantCount = tvNotUrgentImportantCount;
    this.tvNotUrgentNotImportantCount = tvNotUrgentNotImportantCount;
    this.tvTodayTasks = tvTodayTasks;
    this.tvTotalTasks = tvTotalTasks;
    this.tvUrgentImportantCount = tvUrgentImportantCount;
    this.tvUrgentNotImportantCount = tvUrgentNotImportantCount;
    this.tvUrgentTasks = tvUrgentTasks;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTaskManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTaskManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_task_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTaskManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayoutTaskManagement;
      AppBarLayout appBarLayoutTaskManagement = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayoutTaskManagement == null) {
        break missingId;
      }

      id = R.id.btnFilter;
      ImageButton btnFilter = ViewBindings.findChildViewById(rootView, id);
      if (btnFilter == null) {
        break missingId;
      }

      id = R.id.btnSearch;
      ImageButton btnSearch = ViewBindings.findChildViewById(rootView, id);
      if (btnSearch == null) {
        break missingId;
      }

      id = R.id.btnSortTasks;
      ImageButton btnSortTasks = ViewBindings.findChildViewById(rootView, id);
      if (btnSortTasks == null) {
        break missingId;
      }

      id = R.id.btnViewMode;
      ImageButton btnViewMode = ViewBindings.findChildViewById(rootView, id);
      if (btnViewMode == null) {
        break missingId;
      }

      id = R.id.cardMatrixToggle;
      MaterialCardView cardMatrixToggle = ViewBindings.findChildViewById(rootView, id);
      if (cardMatrixToggle == null) {
        break missingId;
      }

      id = R.id.cardQuickStats;
      MaterialCardView cardQuickStats = ViewBindings.findChildViewById(rootView, id);
      if (cardQuickStats == null) {
        break missingId;
      }

      id = R.id.chipAll;
      Chip chipAll = ViewBindings.findChildViewById(rootView, id);
      if (chipAll == null) {
        break missingId;
      }

      id = R.id.chipCompleted;
      Chip chipCompleted = ViewBindings.findChildViewById(rootView, id);
      if (chipCompleted == null) {
        break missingId;
      }

      id = R.id.chipGroupFilters;
      ChipGroup chipGroupFilters = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupFilters == null) {
        break missingId;
      }

      id = R.id.chipOverdue;
      Chip chipOverdue = ViewBindings.findChildViewById(rootView, id);
      if (chipOverdue == null) {
        break missingId;
      }

      id = R.id.chipThisWeek;
      Chip chipThisWeek = ViewBindings.findChildViewById(rootView, id);
      if (chipThisWeek == null) {
        break missingId;
      }

      id = R.id.chipToday;
      Chip chipToday = ViewBindings.findChildViewById(rootView, id);
      if (chipToday == null) {
        break missingId;
      }

      id = R.id.chipUrgent;
      Chip chipUrgent = ViewBindings.findChildViewById(rootView, id);
      if (chipUrgent == null) {
        break missingId;
      }

      id = R.id.fabAddTask;
      ExtendedFloatingActionButton fabAddTask = ViewBindings.findChildViewById(rootView, id);
      if (fabAddTask == null) {
        break missingId;
      }

      id = R.id.layoutEisenhowerMatrix;
      LinearLayout layoutEisenhowerMatrix = ViewBindings.findChildViewById(rootView, id);
      if (layoutEisenhowerMatrix == null) {
        break missingId;
      }

      id = R.id.layoutEmptyState;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.progressOverall;
      ProgressBar progressOverall = ViewBindings.findChildViewById(rootView, id);
      if (progressOverall == null) {
        break missingId;
      }

      id = R.id.rvNotUrgentImportant;
      RecyclerView rvNotUrgentImportant = ViewBindings.findChildViewById(rootView, id);
      if (rvNotUrgentImportant == null) {
        break missingId;
      }

      id = R.id.rvNotUrgentNotImportant;
      RecyclerView rvNotUrgentNotImportant = ViewBindings.findChildViewById(rootView, id);
      if (rvNotUrgentNotImportant == null) {
        break missingId;
      }

      id = R.id.rvTasks;
      RecyclerView rvTasks = ViewBindings.findChildViewById(rootView, id);
      if (rvTasks == null) {
        break missingId;
      }

      id = R.id.rvUrgentImportant;
      RecyclerView rvUrgentImportant = ViewBindings.findChildViewById(rootView, id);
      if (rvUrgentImportant == null) {
        break missingId;
      }

      id = R.id.rvUrgentNotImportant;
      RecyclerView rvUrgentNotImportant = ViewBindings.findChildViewById(rootView, id);
      if (rvUrgentNotImportant == null) {
        break missingId;
      }

      id = R.id.scrollViewFilters;
      HorizontalScrollView scrollViewFilters = ViewBindings.findChildViewById(rootView, id);
      if (scrollViewFilters == null) {
        break missingId;
      }

      id = R.id.searchView;
      SearchView searchView = ViewBindings.findChildViewById(rootView, id);
      if (searchView == null) {
        break missingId;
      }

      id = R.id.switchMatrixView;
      SwitchCompat switchMatrixView = ViewBindings.findChildViewById(rootView, id);
      if (switchMatrixView == null) {
        break missingId;
      }

      id = R.id.toolbarTaskManagement;
      MaterialToolbar toolbarTaskManagement = ViewBindings.findChildViewById(rootView, id);
      if (toolbarTaskManagement == null) {
        break missingId;
      }

      id = R.id.tvCompletionRate;
      TextView tvCompletionRate = ViewBindings.findChildViewById(rootView, id);
      if (tvCompletionRate == null) {
        break missingId;
      }

      id = R.id.tvNotUrgentImportantCount;
      TextView tvNotUrgentImportantCount = ViewBindings.findChildViewById(rootView, id);
      if (tvNotUrgentImportantCount == null) {
        break missingId;
      }

      id = R.id.tvNotUrgentNotImportantCount;
      TextView tvNotUrgentNotImportantCount = ViewBindings.findChildViewById(rootView, id);
      if (tvNotUrgentNotImportantCount == null) {
        break missingId;
      }

      id = R.id.tvTodayTasks;
      TextView tvTodayTasks = ViewBindings.findChildViewById(rootView, id);
      if (tvTodayTasks == null) {
        break missingId;
      }

      id = R.id.tvTotalTasks;
      TextView tvTotalTasks = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalTasks == null) {
        break missingId;
      }

      id = R.id.tvUrgentImportantCount;
      TextView tvUrgentImportantCount = ViewBindings.findChildViewById(rootView, id);
      if (tvUrgentImportantCount == null) {
        break missingId;
      }

      id = R.id.tvUrgentNotImportantCount;
      TextView tvUrgentNotImportantCount = ViewBindings.findChildViewById(rootView, id);
      if (tvUrgentNotImportantCount == null) {
        break missingId;
      }

      id = R.id.tvUrgentTasks;
      TextView tvUrgentTasks = ViewBindings.findChildViewById(rootView, id);
      if (tvUrgentTasks == null) {
        break missingId;
      }

      return new ActivityTaskManagementBinding((CoordinatorLayout) rootView,
          appBarLayoutTaskManagement, btnFilter, btnSearch, btnSortTasks, btnViewMode,
          cardMatrixToggle, cardQuickStats, chipAll, chipCompleted, chipGroupFilters, chipOverdue,
          chipThisWeek, chipToday, chipUrgent, fabAddTask, layoutEisenhowerMatrix, layoutEmptyState,
          progressOverall, rvNotUrgentImportant, rvNotUrgentNotImportant, rvTasks,
          rvUrgentImportant, rvUrgentNotImportant, scrollViewFilters, searchView, switchMatrixView,
          toolbarTaskManagement, tvCompletionRate, tvNotUrgentImportantCount,
          tvNotUrgentNotImportantCount, tvTodayTasks, tvTotalTasks, tvUrgentImportantCount,
          tvUrgentNotImportantCount, tvUrgentTasks);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
