// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAutoSendSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton buttonCancelSchedules;

  @NonNull
  public final MaterialButton buttonCheckApps;

  @NonNull
  public final MaterialButton buttonSave;

  @NonNull
  public final MaterialButton buttonScheduleNow;

  @NonNull
  public final MaterialButton buttonTestMonthlyReport;

  @NonNull
  public final MaterialButton buttonTestWeeklyReport;

  @NonNull
  public final TextInputEditText editTextEmail;

  @NonNull
  public final TextInputEditText editTextPhone;

  @NonNull
  public final SwitchMaterial switchAutoReminders;

  @NonNull
  public final SwitchMaterial switchEmailEnabled;

  @NonNull
  public final SwitchMaterial switchMonthlyReports;

  @NonNull
  public final SwitchMaterial switchReminderEmail;

  @NonNull
  public final SwitchMaterial switchReminderWhatsapp;

  @NonNull
  public final SwitchMaterial switchWeeklyReports;

  @NonNull
  public final SwitchMaterial switchWhatsappEnabled;

  @NonNull
  public final TextView textEmailStatus;

  @NonNull
  public final TextView textWhatsappStatus;

  @NonNull
  public final Toolbar toolbar;

  private ActivityAutoSendSettingsBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton buttonCancelSchedules, @NonNull MaterialButton buttonCheckApps,
      @NonNull MaterialButton buttonSave, @NonNull MaterialButton buttonScheduleNow,
      @NonNull MaterialButton buttonTestMonthlyReport,
      @NonNull MaterialButton buttonTestWeeklyReport, @NonNull TextInputEditText editTextEmail,
      @NonNull TextInputEditText editTextPhone, @NonNull SwitchMaterial switchAutoReminders,
      @NonNull SwitchMaterial switchEmailEnabled, @NonNull SwitchMaterial switchMonthlyReports,
      @NonNull SwitchMaterial switchReminderEmail, @NonNull SwitchMaterial switchReminderWhatsapp,
      @NonNull SwitchMaterial switchWeeklyReports, @NonNull SwitchMaterial switchWhatsappEnabled,
      @NonNull TextView textEmailStatus, @NonNull TextView textWhatsappStatus,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonCancelSchedules = buttonCancelSchedules;
    this.buttonCheckApps = buttonCheckApps;
    this.buttonSave = buttonSave;
    this.buttonScheduleNow = buttonScheduleNow;
    this.buttonTestMonthlyReport = buttonTestMonthlyReport;
    this.buttonTestWeeklyReport = buttonTestWeeklyReport;
    this.editTextEmail = editTextEmail;
    this.editTextPhone = editTextPhone;
    this.switchAutoReminders = switchAutoReminders;
    this.switchEmailEnabled = switchEmailEnabled;
    this.switchMonthlyReports = switchMonthlyReports;
    this.switchReminderEmail = switchReminderEmail;
    this.switchReminderWhatsapp = switchReminderWhatsapp;
    this.switchWeeklyReports = switchWeeklyReports;
    this.switchWhatsappEnabled = switchWhatsappEnabled;
    this.textEmailStatus = textEmailStatus;
    this.textWhatsappStatus = textWhatsappStatus;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAutoSendSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAutoSendSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_auto_send_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAutoSendSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonCancelSchedules;
      MaterialButton buttonCancelSchedules = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancelSchedules == null) {
        break missingId;
      }

      id = R.id.buttonCheckApps;
      MaterialButton buttonCheckApps = ViewBindings.findChildViewById(rootView, id);
      if (buttonCheckApps == null) {
        break missingId;
      }

      id = R.id.buttonSave;
      MaterialButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.buttonScheduleNow;
      MaterialButton buttonScheduleNow = ViewBindings.findChildViewById(rootView, id);
      if (buttonScheduleNow == null) {
        break missingId;
      }

      id = R.id.buttonTestMonthlyReport;
      MaterialButton buttonTestMonthlyReport = ViewBindings.findChildViewById(rootView, id);
      if (buttonTestMonthlyReport == null) {
        break missingId;
      }

      id = R.id.buttonTestWeeklyReport;
      MaterialButton buttonTestWeeklyReport = ViewBindings.findChildViewById(rootView, id);
      if (buttonTestWeeklyReport == null) {
        break missingId;
      }

      id = R.id.editTextEmail;
      TextInputEditText editTextEmail = ViewBindings.findChildViewById(rootView, id);
      if (editTextEmail == null) {
        break missingId;
      }

      id = R.id.editTextPhone;
      TextInputEditText editTextPhone = ViewBindings.findChildViewById(rootView, id);
      if (editTextPhone == null) {
        break missingId;
      }

      id = R.id.switchAutoReminders;
      SwitchMaterial switchAutoReminders = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoReminders == null) {
        break missingId;
      }

      id = R.id.switchEmailEnabled;
      SwitchMaterial switchEmailEnabled = ViewBindings.findChildViewById(rootView, id);
      if (switchEmailEnabled == null) {
        break missingId;
      }

      id = R.id.switchMonthlyReports;
      SwitchMaterial switchMonthlyReports = ViewBindings.findChildViewById(rootView, id);
      if (switchMonthlyReports == null) {
        break missingId;
      }

      id = R.id.switchReminderEmail;
      SwitchMaterial switchReminderEmail = ViewBindings.findChildViewById(rootView, id);
      if (switchReminderEmail == null) {
        break missingId;
      }

      id = R.id.switchReminderWhatsapp;
      SwitchMaterial switchReminderWhatsapp = ViewBindings.findChildViewById(rootView, id);
      if (switchReminderWhatsapp == null) {
        break missingId;
      }

      id = R.id.switchWeeklyReports;
      SwitchMaterial switchWeeklyReports = ViewBindings.findChildViewById(rootView, id);
      if (switchWeeklyReports == null) {
        break missingId;
      }

      id = R.id.switchWhatsappEnabled;
      SwitchMaterial switchWhatsappEnabled = ViewBindings.findChildViewById(rootView, id);
      if (switchWhatsappEnabled == null) {
        break missingId;
      }

      id = R.id.textEmailStatus;
      TextView textEmailStatus = ViewBindings.findChildViewById(rootView, id);
      if (textEmailStatus == null) {
        break missingId;
      }

      id = R.id.textWhatsappStatus;
      TextView textWhatsappStatus = ViewBindings.findChildViewById(rootView, id);
      if (textWhatsappStatus == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAutoSendSettingsBinding((LinearLayout) rootView, buttonCancelSchedules,
          buttonCheckApps, buttonSave, buttonScheduleNow, buttonTestMonthlyReport,
          buttonTestWeeklyReport, editTextEmail, editTextPhone, switchAutoReminders,
          switchEmailEnabled, switchMonthlyReports, switchReminderEmail, switchReminderWhatsapp,
          switchWeeklyReports, switchWhatsappEnabled, textEmailStatus, textWhatsappStatus, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
