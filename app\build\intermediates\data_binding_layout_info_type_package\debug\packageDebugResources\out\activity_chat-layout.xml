<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_chat" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_chat.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_chat_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="104" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="26" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="17" startOffset="8" endLine="24" endOffset="58"/></Target><Target id="@+id/messagesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="36" startOffset="8" endLine="43" endOffset="61"/></Target><Target id="@+id/attachButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="55" startOffset="12" endLine="63" endOffset="50"/></Target><Target id="@+id/messageEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="78" startOffset="16" endLine="86" endOffset="44"/></Target><Target id="@+id/sendButton" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="91" startOffset="12" endLine="98" endOffset="49"/></Target></Targets></Layout>