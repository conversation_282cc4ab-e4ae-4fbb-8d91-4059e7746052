<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_report" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_report.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_report_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="214" endOffset="53"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="133" startOffset="16" endLine="155" endOffset="30"/></Target><Target id="@+id/compactReportTable" tag="binding_1" include="compact_report_table"><Expressions/><location startLine="149" startOffset="20" endLine="153" endOffset="62"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="24" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/kpiSelectorLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="36" startOffset="12" endLine="54" endOffset="67"/></Target><Target id="@+id/kpiSelectorAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="46" startOffset="16" endLine="52" endOffset="46"/></Target><Target id="@+id/startDateInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="56" startOffset="12" endLine="75" endOffset="67"/></Target><Target id="@+id/startDateEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="67" startOffset="16" endLine="73" endOffset="45"/></Target><Target id="@+id/endDateInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="77" startOffset="12" endLine="103" endOffset="67"/></Target><Target id="@+id/endDateEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="95" startOffset="16" endLine="101" endOffset="45"/></Target><Target id="@+id/goButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="105" startOffset="12" endLine="120" endOffset="80"/></Target><Target id="@+id/reportTableCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="122" startOffset="12" endLine="157" endOffset="63"/></Target><Target id="@+id/kpiNameLabel" view="TextView"><Expressions/><location startLine="138" startOffset="20" endLine="147" endOffset="52"/></Target><Target id="@+id/trendChart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="159" startOffset="12" endLine="166" endOffset="75"/></Target><Target id="@+id/summaryChartFilterRadioGroup" view="RadioGroup"><Expressions/><location startLine="169" startOffset="12" endLine="200" endOffset="24"/></Target><Target id="@+id/radioDaily" view="RadioButton"><Expressions/><location startLine="180" startOffset="16" endLine="185" endOffset="52"/></Target><Target id="@+id/radioMonthly" view="RadioButton"><Expressions/><location startLine="187" startOffset="16" endLine="192" endOffset="52"/></Target><Target id="@+id/radioAnnual" view="RadioButton"><Expressions/><location startLine="194" startOffset="16" endLine="199" endOffset="43"/></Target><Target id="@+id/summaryBarChart" view="com.github.mikephil.charting.charts.BarChart"><Expressions/><location startLine="202" startOffset="12" endLine="209" endOffset="88"/></Target></Targets></Layout>