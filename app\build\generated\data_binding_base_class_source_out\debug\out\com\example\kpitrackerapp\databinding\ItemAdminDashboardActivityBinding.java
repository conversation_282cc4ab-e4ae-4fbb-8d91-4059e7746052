// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAdminDashboardActivityBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView activityAction;

  @NonNull
  public final MaterialCardView activityCard;

  @NonNull
  public final TextView activityTime;

  @NonNull
  public final TextView activityUser;

  private ItemAdminDashboardActivityBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView activityAction, @NonNull MaterialCardView activityCard,
      @NonNull TextView activityTime, @NonNull TextView activityUser) {
    this.rootView = rootView;
    this.activityAction = activityAction;
    this.activityCard = activityCard;
    this.activityTime = activityTime;
    this.activityUser = activityUser;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAdminDashboardActivityBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAdminDashboardActivityBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_admin_dashboard_activity, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAdminDashboardActivityBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.activityAction;
      TextView activityAction = ViewBindings.findChildViewById(rootView, id);
      if (activityAction == null) {
        break missingId;
      }

      MaterialCardView activityCard = (MaterialCardView) rootView;

      id = R.id.activityTime;
      TextView activityTime = ViewBindings.findChildViewById(rootView, id);
      if (activityTime == null) {
        break missingId;
      }

      id = R.id.activityUser;
      TextView activityUser = ViewBindings.findChildViewById(rootView, id);
      if (activityUser == null) {
        break missingId;
      }

      return new ItemAdminDashboardActivityBinding((MaterialCardView) rootView, activityAction,
          activityCard, activityTime, activityUser);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
