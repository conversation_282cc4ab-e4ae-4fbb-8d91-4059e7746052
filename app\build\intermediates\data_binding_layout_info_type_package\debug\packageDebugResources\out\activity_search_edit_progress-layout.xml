<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_search_edit_progress" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_search_edit_progress.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_search_edit_progress_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="51"/></Target><Target id="@+id/searchPromptTextView" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="42"/></Target><Target id="@+id/dateSpinner" view="Spinner"><Expressions/><location startLine="20" startOffset="4" endLine="28" endOffset="51"/></Target><Target id="@+id/valueInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="30" startOffset="4" endLine="47" endOffset="59"/></Target><Target id="@+id/valueEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="41" startOffset="8" endLine="46" endOffset="37"/></Target><Target id="@+id/saveButton" view="Button"><Expressions/><location startLine="49" startOffset="4" endLine="57" endOffset="51"/></Target><Target id="@+id/deleteButton" view="Button"><Expressions/><location startLine="59" startOffset="4" endLine="70" endOffset="61"/></Target></Targets></Layout>