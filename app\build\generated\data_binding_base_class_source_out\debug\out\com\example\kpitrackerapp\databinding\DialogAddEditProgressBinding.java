// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddEditProgressBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button dateButton;

  @NonNull
  public final TextView dialogLastEntryDateTextView;

  @NonNull
  public final TextView dialogTitleTextView;

  @NonNull
  public final EditText valueEditText;

  private DialogAddEditProgressBinding(@NonNull LinearLayout rootView, @NonNull Button dateButton,
      @NonNull TextView dialogLastEntryDateTextView, @NonNull TextView dialogTitleTextView,
      @NonNull EditText valueEditText) {
    this.rootView = rootView;
    this.dateButton = dateButton;
    this.dialogLastEntryDateTextView = dialogLastEntryDateTextView;
    this.dialogTitleTextView = dialogTitleTextView;
    this.valueEditText = valueEditText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddEditProgressBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddEditProgressBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_edit_progress, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddEditProgressBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.dateButton;
      Button dateButton = ViewBindings.findChildViewById(rootView, id);
      if (dateButton == null) {
        break missingId;
      }

      id = R.id.dialogLastEntryDateTextView;
      TextView dialogLastEntryDateTextView = ViewBindings.findChildViewById(rootView, id);
      if (dialogLastEntryDateTextView == null) {
        break missingId;
      }

      id = R.id.dialogTitleTextView;
      TextView dialogTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitleTextView == null) {
        break missingId;
      }

      id = R.id.valueEditText;
      EditText valueEditText = ViewBindings.findChildViewById(rootView, id);
      if (valueEditText == null) {
        break missingId;
      }

      return new DialogAddEditProgressBinding((LinearLayout) rootView, dateButton,
          dialogLastEntryDateTextView, dialogTitleTextView, valueEditText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
