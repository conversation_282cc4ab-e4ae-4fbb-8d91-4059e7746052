package com.example.kpitrackerapp.ui

import android.Manifest
import android.app.DatePickerDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityAddProductBinding
import com.example.kpitrackerapp.models.Product
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.viewmodels.ProductViewModel
import com.example.kpitrackerapp.viewmodels.ProductViewModelFactory
import com.example.kpitrackerapp.persistence.AppDatabase
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class AddProductActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAddProductBinding
    private val productViewModel: ProductViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        ProductViewModelFactory(
            application,
            database.productDao()
        )
    }
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private var selectedExpiryDate: Date? = null

    // Barcode scanner launcher
    private val barcodeLauncher = registerForActivityResult(ScanContract()) { result ->
        if (result.contents != null) {
            binding.barcodeInput.setText(result.contents)
        }
    }

    // Camera permission launcher
    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startBarcodeScanner()
        } else {
            Toast.makeText(this, "إذن الكاميرا مطلوب لمسح الباركود", Toast.LENGTH_SHORT).show()
        }
    }

    // OCR variables
    private val textRecognizer by lazy {
        com.google.mlkit.vision.text.TextRecognition.getClient(
            com.google.mlkit.vision.text.latin.TextRecognizerOptions.Builder().build()
        )
    }
    private var photoUri: Uri? = null

    // Camera launcher for OCR
    private val cameraOCRLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && photoUri != null) {
            processImageForOCR(photoUri!!)
        }
    }

    // Gallery launcher for OCR
    private val galleryOCRLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { processImageForOCR(it) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddProductBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupActionBar()
        setupCategorySpinner()
        setupClickListeners()
        observeViewModel()
    }

    private fun setupActionBar() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = "إضافة منتج جديد"
    }

    private fun setupCategorySpinner() {
        val categories = com.example.kpitrackerapp.models.ProductCategory.values().map { it.name }
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categories)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.categorySpinner.adapter = adapter
    }

    private fun setupClickListeners() {
        binding.expiryDateInput.setOnClickListener {
            showDatePickerDialog()
        }

        binding.addProductButton.setOnClickListener {
            addProduct()
        }

        binding.cancelButton.setOnClickListener {
            finish()
        }

        // Barcode scanner click listener
        val barcodeLayout = findViewById<com.google.android.material.textfield.TextInputLayout>(R.id.barcodeInputLayout)
        barcodeLayout?.setEndIconOnClickListener {
            checkCameraPermissionAndScan()
        }

        // OCR for product name
        val nameLayout = findViewById<com.google.android.material.textfield.TextInputLayout>(R.id.productNameInputLayout)
        nameLayout?.setEndIconOnClickListener {
            startOCRActivity()
        }
    }

    private fun showDatePickerDialog() {
        val calendar = Calendar.getInstance()

        val dateSetListener = DatePickerDialog.OnDateSetListener { _, year, month, dayOfMonth ->
            calendar.set(Calendar.YEAR, year)
            calendar.set(Calendar.MONTH, month)
            calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

            selectedExpiryDate = calendar.time
            binding.expiryDateInput.setText(dateFormat.format(calendar.time))
        }

        DatePickerDialog(
            this,
            dateSetListener,
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun addProduct() {
        val name = binding.productNameInput.text.toString().trim()
        val quantityText = binding.quantityInput.text.toString().trim()
        val location = binding.locationInput.text.toString().trim()
        val notes = binding.notesInput.text.toString().trim()
        val barcode = binding.barcodeInput.text.toString().trim()

        // Validation
        if (name.isEmpty()) {
            binding.productNameInput.error = "اسم المنتج مطلوب"
            return
        }

        if (quantityText.isEmpty()) {
            binding.quantityInput.error = "الكمية مطلوبة"
            return
        }

        val quantity = quantityText.toIntOrNull()
        if (quantity == null || quantity <= 0) {
            binding.quantityInput.error = "الكمية يجب أن تكون رقم صحيح أكبر من صفر"
            return
        }

        if (selectedExpiryDate == null) {
            Toast.makeText(this, "تاريخ انتهاء الصلاحية مطلوب", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedCategory = com.example.kpitrackerapp.models.ProductCategory.values()[binding.categorySpinner.selectedItemPosition]

        val product = Product(
            name = name,
            category = selectedCategory,
            expiryDate = selectedExpiryDate!!,
            quantity = quantity,
            location = location.ifEmpty { null },
            notes = notes.ifEmpty { null },
            barcode = barcode.ifEmpty { null }
        )

        productViewModel.insertProduct(product)
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            productViewModel.uiState.collect { state ->
                if (state.isLoading) {
                    binding.addProductButton.isEnabled = false
                    binding.addProductButton.text = "جاري الإضافة..."
                } else {
                    binding.addProductButton.isEnabled = true
                    binding.addProductButton.text = "إضافة المنتج"
                }

                state.message?.let { message ->
                    Toast.makeText(this@AddProductActivity, message, Toast.LENGTH_SHORT).show()
                    productViewModel.clearMessage()
                    finish() // Go back to dashboard
                }

                state.error?.let { error ->
                    Toast.makeText(this@AddProductActivity, error, Toast.LENGTH_LONG).show()
                    productViewModel.clearError()
                }
            }
        }
    }

    private fun checkCameraPermissionAndScan() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                startBarcodeScanner()
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    private fun startBarcodeScanner() {
        val options = ScanOptions().apply {
            setDesiredBarcodeFormats(ScanOptions.ALL_CODE_TYPES)
            setPrompt("امسح الباركود أو QR Code")
            setCameraId(0)
            setBeepEnabled(true)
            setBarcodeImageEnabled(true)
            setOrientationLocked(false)
        }
        barcodeLauncher.launch(options)
    }

    private fun startOCRActivity() {
        // بدلاً من استخدام OcrActivity المعقد، سنستخدم OCR مباشر هنا
        startSimpleOCR()
    }

    private fun startSimpleOCR() {
        // استخدام OCR مباشر بدون الحاجة لـ OcrActivity
        val options = arrayOf(
            "📷 التقاط صورة ومسح النص",
            "🖼️ اختيار صورة من المعرض"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔍 مسح اسم المنتج")
            .setMessage("اختر طريقة مسح النص")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> capturePhotoForOCR()
                    1 -> selectImageForOCR()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun capturePhotoForOCR() {
        val photoFile = createImageFile()
        photoUri = androidx.core.content.FileProvider.getUriForFile(
            this,
            "${packageName}.provider",
            photoFile
        )
        cameraOCRLauncher.launch(photoUri)
    }

    private fun selectImageForOCR() {
        galleryOCRLauncher.launch("image/*")
    }

    private fun createImageFile(): File {
        val timeStamp = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(java.util.Date())
        val storageDir = getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES)
        return File.createTempFile("OCR_${timeStamp}_", ".jpg", storageDir)
    }

    private fun processImageForOCR(imageUri: Uri) {
        try {
            val inputImage = com.google.mlkit.vision.common.InputImage.fromFilePath(this, imageUri)
            textRecognizer.process(inputImage)
                .addOnSuccessListener { visionText ->
                    val extractedText = visionText.text.trim()
                    if (extractedText.isNotEmpty()) {
                        binding.productNameInput.setText(extractedText)
                        Toast.makeText(this, "تم استخراج النص بنجاح", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "لم يتم العثور على نص في الصورة", Toast.LENGTH_SHORT).show()
                    }
                }
                .addOnFailureListener { e ->
                    Toast.makeText(this, "فشل في مسح النص: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
                }
        } catch (e: Exception) {
            Toast.makeText(this, "خطأ في معالجة الصورة: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
