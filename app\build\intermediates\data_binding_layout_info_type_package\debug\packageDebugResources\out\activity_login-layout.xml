<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="221" endOffset="51"/></Target><Target id="@+id/appLogoImageView" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/appTitleTextView" view="TextView"><Expressions/><location startLine="23" startOffset="4" endLine="34" endOffset="69"/></Target><Target id="@+id/welcomeTextView" view="TextView"><Expressions/><location startLine="37" startOffset="4" endLine="47" endOffset="69"/></Target><Target id="@+id/loginFormCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="50" startOffset="4" endLine="182" endOffset="55"/></Target><Target id="@+id/usernameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="68" startOffset="12" endLine="83" endOffset="67"/></Target><Target id="@+id/usernameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="16" endLine="81" endOffset="42"/></Target><Target id="@+id/orTextView" view="TextView"><Expressions/><location startLine="86" startOffset="12" endLine="95" endOffset="41"/></Target><Target id="@+id/userSelectionSpinner" view="Spinner"><Expressions/><location startLine="97" startOffset="12" endLine="102" endOffset="67"/></Target><Target id="@+id/adminModeCheckBox" view="CheckBox"><Expressions/><location startLine="112" startOffset="16" endLine="118" endOffset="46"/></Target><Target id="@+id/adminRoleInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="131" startOffset="12" endLine="148" endOffset="67"/></Target><Target id="@+id/adminRoleAutoComplete" view="AutoCompleteTextView"><Expressions/><location startLine="141" startOffset="16" endLine="146" endOffset="42"/></Target><Target id="@+id/rememberMeCheckBox" view="CheckBox"><Expressions/><location startLine="151" startOffset="12" endLine="157" endOffset="53"/></Target><Target id="@+id/loginButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="160" startOffset="12" endLine="167" endOffset="41"/></Target><Target id="@+id/createUserButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="170" startOffset="12" endLine="178" endOffset="41"/></Target><Target id="@+id/quickAccessTextView" view="TextView"><Expressions/><location startLine="185" startOffset="4" endLine="195" endOffset="66"/></Target><Target id="@+id/recentUsersRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="198" startOffset="4" endLine="208" endOffset="51"/></Target><Target id="@+id/loadingProgressBar" view="ProgressBar"><Expressions/><location startLine="211" startOffset="4" endLine="219" endOffset="51"/></Target></Targets></Layout>