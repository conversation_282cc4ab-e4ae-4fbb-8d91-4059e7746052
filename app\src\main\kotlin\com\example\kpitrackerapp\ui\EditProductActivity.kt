package com.example.kpitrackerapp.ui

import android.Manifest
import android.app.DatePickerDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityEditProductBinding
import com.example.kpitrackerapp.models.Product
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.viewmodels.ProductViewModel
import com.example.kpitrackerapp.viewmodels.ProductViewModelFactory
import com.example.kpitrackerapp.persistence.AppDatabase
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class EditProductActivity : AppCompatActivity() {

    private lateinit var binding: ActivityEditProductBinding
    private val productViewModel: ProductViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        ProductViewModelFactory(
            application,
            database.productDao()
        )
    }
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private var selectedExpiryDate: Date? = null
    private var currentProduct: Product? = null

    // Barcode scanner launcher
    private val barcodeLauncher = registerForActivityResult(ScanContract()) { result ->
        if (result.contents != null) {
            binding.barcodeInput.setText(result.contents)
        }
    }

    // Camera permission launcher
    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startBarcodeScanner()
        } else {
            Toast.makeText(this, "إذن الكاميرا مطلوب لمسح الباركود", Toast.LENGTH_SHORT).show()
        }
    }

    // OCR variables
    private val textRecognizer by lazy {
        com.google.mlkit.vision.text.TextRecognition.getClient(
            com.google.mlkit.vision.text.latin.TextRecognizerOptions.Builder().build()
        )
    }
    private var photoUri: Uri? = null

    // Camera launcher for OCR
    private val cameraOCRLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && photoUri != null) {
            processImageForOCR(photoUri!!)
        }
    }

    // Gallery launcher for OCR
    private val galleryOCRLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { processImageForOCR(it) }
    }

    companion object {
        const val EXTRA_PRODUCT_ID = "product_id"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditProductBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val productId = intent.getStringExtra(EXTRA_PRODUCT_ID)
        if (productId.isNullOrEmpty()) {
            Toast.makeText(this, "خطأ: معرف المنتج غير صحيح", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        setupActionBar()
        setupCategorySpinner()
        setupClickListeners()
        loadProduct(productId)
        observeViewModel()
    }

    private fun setupActionBar() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = "تعديل المنتج"
    }

    private fun setupCategorySpinner() {
        val categories = ProductCategory.values().map { it.displayName }
        val spinnerAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categories)
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.categorySpinner.setAdapter(spinnerAdapter)
    }

    private fun setupClickListeners() {
        binding.expiryDateInput.setOnClickListener {
            showDatePickerDialog()
        }

        binding.updateProductButton.setOnClickListener {
            updateProduct()
        }

        binding.cancelButton.setOnClickListener {
            finish()
        }

        binding.deleteButton.setOnClickListener {
            confirmDeleteProduct()
        }

        // Barcode scanner click listener
        val barcodeLayout = findViewById<com.google.android.material.textfield.TextInputLayout>(R.id.barcodeInputLayout)
        barcodeLayout?.setEndIconOnClickListener {
            checkCameraPermissionAndScan()
        }

        // OCR for product name
        val nameLayout = findViewById<com.google.android.material.textfield.TextInputLayout>(R.id.productNameInputLayout)
        nameLayout?.setEndIconOnClickListener {
            startOCRActivity()
        }
    }

    private fun loadProduct(productId: String) {
        lifecycleScope.launch {
            productViewModel.getProductById(productId).collect { product ->
                if (product != null) {
                    currentProduct = product
                    populateFields(product)
                } else {
                    Toast.makeText(this@EditProductActivity, "المنتج غير موجود", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        }
    }

    private fun populateFields(product: Product) {
        binding.apply {
            productNameInput.setText(product.name)
            quantityInput.setText(product.quantity.toString())
            locationInput.setText(product.location ?: "")
            notesInput.setText(product.notes ?: "")
            barcodeInput.setText(product.barcode ?: "")
            
            // Set expiry date
            selectedExpiryDate = product.expiryDate
            expiryDateInput.setText(dateFormat.format(product.expiryDate))
            
            // Set category
            val categoryIndex = ProductCategory.values().indexOf(product.category)
            if (categoryIndex >= 0) {
                categorySpinner.setText(ProductCategory.values()[categoryIndex].displayName, false)
            }
        }
    }

    private fun showDatePickerDialog() {
        val calendar = Calendar.getInstance()
        selectedExpiryDate?.let { calendar.time = it }

        DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                calendar.set(year, month, dayOfMonth)
                selectedExpiryDate = calendar.time
                binding.expiryDateInput.setText(dateFormat.format(selectedExpiryDate!!))
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun updateProduct() {
        val name = binding.productNameInput.text.toString().trim()
        val quantityText = binding.quantityInput.text.toString().trim()
        val location = binding.locationInput.text.toString().trim()
        val notes = binding.notesInput.text.toString().trim()
        val barcode = binding.barcodeInput.text.toString().trim()

        // Validation
        if (name.isEmpty()) {
            binding.productNameInput.error = "اسم المنتج مطلوب"
            return
        }

        if (quantityText.isEmpty()) {
            binding.quantityInput.error = "الكمية مطلوبة"
            return
        }

        val quantity = quantityText.toIntOrNull()
        if (quantity == null || quantity <= 0) {
            binding.quantityInput.error = "الكمية يجب أن تكون رقم صحيح أكبر من صفر"
            return
        }

        if (selectedExpiryDate == null) {
            Toast.makeText(this, "تاريخ انتهاء الصلاحية مطلوب", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedCategoryName = binding.categorySpinner.text.toString()
        val selectedCategory = ProductCategory.values().find { it.displayName == selectedCategoryName }
            ?: ProductCategory.MEDICINE

        val updatedProduct = currentProduct!!.copy(
            name = name,
            category = selectedCategory,
            expiryDate = selectedExpiryDate!!,
            quantity = quantity,
            location = location.ifEmpty { null },
            notes = notes.ifEmpty { null },
            barcode = barcode.ifEmpty { null }
        )

        productViewModel.updateProduct(updatedProduct)
    }

    private fun confirmDeleteProduct() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("حذف المنتج")
            .setMessage("هل أنت متأكد من حذف هذا المنتج؟")
            .setPositiveButton("حذف") { _, _ ->
                currentProduct?.let { productViewModel.deleteProduct(it) }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            productViewModel.uiState.collect { state ->
                if (state.isLoading) {
                    binding.updateProductButton.isEnabled = false
                    binding.updateProductButton.text = "جاري التحديث..."
                } else {
                    binding.updateProductButton.isEnabled = true
                    binding.updateProductButton.text = "تحديث المنتج"
                }

                state.message?.let { message ->
                    Toast.makeText(this@EditProductActivity, message, Toast.LENGTH_SHORT).show()
                    productViewModel.clearMessage()
                    finish() // Go back to previous screen
                }

                state.error?.let { error ->
                    Toast.makeText(this@EditProductActivity, error, Toast.LENGTH_LONG).show()
                    productViewModel.clearError()
                }
            }
        }
    }

    private fun checkCameraPermissionAndScan() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                startBarcodeScanner()
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    private fun startBarcodeScanner() {
        val options = ScanOptions().apply {
            setDesiredBarcodeFormats(ScanOptions.ALL_CODE_TYPES)
            setPrompt("امسح الباركود أو QR Code")
            setCameraId(0)
            setBeepEnabled(true)
            setBarcodeImageEnabled(true)
            setOrientationLocked(false)
        }
        barcodeLauncher.launch(options)
    }

    private fun startOCRActivity() {
        startSimpleOCR()
    }

    private fun startSimpleOCR() {
        val options = arrayOf(
            "📷 التقاط صورة ومسح النص",
            "🖼️ اختيار صورة من المعرض"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔍 مسح اسم المنتج")
            .setMessage("اختر طريقة مسح النص")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> capturePhotoForOCR()
                    1 -> selectImageForOCR()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun capturePhotoForOCR() {
        val photoFile = createImageFile()
        photoUri = androidx.core.content.FileProvider.getUriForFile(
            this,
            "${packageName}.provider",
            photoFile
        )
        cameraOCRLauncher.launch(photoUri)
    }

    private fun selectImageForOCR() {
        galleryOCRLauncher.launch("image/*")
    }

    private fun createImageFile(): File {
        val timeStamp = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(java.util.Date())
        val storageDir = getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES)
        return File.createTempFile("OCR_${timeStamp}_", ".jpg", storageDir)
    }

    private fun processImageForOCR(imageUri: Uri) {
        try {
            val inputImage = com.google.mlkit.vision.common.InputImage.fromFilePath(this, imageUri)
            textRecognizer.process(inputImage)
                .addOnSuccessListener { visionText ->
                    val extractedText = visionText.text.trim()
                    if (extractedText.isNotEmpty()) {
                        binding.productNameInput.setText(extractedText)
                        Toast.makeText(this, "تم استخراج النص بنجاح", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "لم يتم العثور على نص في الصورة", Toast.LENGTH_SHORT).show()
                    }
                }
                .addOnFailureListener { e ->
                    Toast.makeText(this, "فشل في مسح النص: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
                }
        } catch (e: Exception) {
            Toast.makeText(this, "خطأ في معالجة الصورة: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
