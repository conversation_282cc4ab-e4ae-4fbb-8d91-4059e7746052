// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.materialswitch.MaterialSwitch;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAddEditTaskEnhancedBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton addTaskButton;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final MaterialSwitch switchRecurringTask;

  @NonNull
  public final AutoCompleteTextView taskCategoryAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskCategoryInputLayout;

  @NonNull
  public final TextInputEditText taskDescriptionEditText;

  @NonNull
  public final TextInputLayout taskDescriptionInputLayout;

  @NonNull
  public final AutoCompleteTextView taskEnergyLevelAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskEnergyLevelInputLayout;

  @NonNull
  public final TextInputEditText taskEstimatedHoursEditText;

  @NonNull
  public final TextInputLayout taskEstimatedHoursInputLayout;

  @NonNull
  public final TextInputEditText taskExpirationDateEditText;

  @NonNull
  public final TextInputLayout taskExpirationDateInputLayout;

  @NonNull
  public final TextInputEditText taskExpirationTimeEditText;

  @NonNull
  public final TextInputLayout taskExpirationTimeInputLayout;

  @NonNull
  public final AutoCompleteTextView taskImportanceAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskImportanceInputLayout;

  @NonNull
  public final TextInputEditText taskNameEditText;

  @NonNull
  public final TextInputLayout taskNameInputLayout;

  @NonNull
  public final TextInputEditText taskNotesEditText;

  @NonNull
  public final TextInputLayout taskNotesInputLayout;

  @NonNull
  public final AutoCompleteTextView taskPriorityAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskPriorityInputLayout;

  @NonNull
  public final AutoCompleteTextView taskReminderAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskReminderInputLayout;

  @NonNull
  public final TextInputEditText taskTagsEditText;

  @NonNull
  public final TextInputLayout taskTagsInputLayout;

  @NonNull
  public final MaterialToolbar toolbar;

  private ActivityAddEditTaskEnhancedBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton addTaskButton, @NonNull AppBarLayout appBarLayout,
      @NonNull MaterialButton cancelButton, @NonNull MaterialSwitch switchRecurringTask,
      @NonNull AutoCompleteTextView taskCategoryAutoCompleteTextView,
      @NonNull TextInputLayout taskCategoryInputLayout,
      @NonNull TextInputEditText taskDescriptionEditText,
      @NonNull TextInputLayout taskDescriptionInputLayout,
      @NonNull AutoCompleteTextView taskEnergyLevelAutoCompleteTextView,
      @NonNull TextInputLayout taskEnergyLevelInputLayout,
      @NonNull TextInputEditText taskEstimatedHoursEditText,
      @NonNull TextInputLayout taskEstimatedHoursInputLayout,
      @NonNull TextInputEditText taskExpirationDateEditText,
      @NonNull TextInputLayout taskExpirationDateInputLayout,
      @NonNull TextInputEditText taskExpirationTimeEditText,
      @NonNull TextInputLayout taskExpirationTimeInputLayout,
      @NonNull AutoCompleteTextView taskImportanceAutoCompleteTextView,
      @NonNull TextInputLayout taskImportanceInputLayout,
      @NonNull TextInputEditText taskNameEditText, @NonNull TextInputLayout taskNameInputLayout,
      @NonNull TextInputEditText taskNotesEditText, @NonNull TextInputLayout taskNotesInputLayout,
      @NonNull AutoCompleteTextView taskPriorityAutoCompleteTextView,
      @NonNull TextInputLayout taskPriorityInputLayout,
      @NonNull AutoCompleteTextView taskReminderAutoCompleteTextView,
      @NonNull TextInputLayout taskReminderInputLayout, @NonNull TextInputEditText taskTagsEditText,
      @NonNull TextInputLayout taskTagsInputLayout, @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.addTaskButton = addTaskButton;
    this.appBarLayout = appBarLayout;
    this.cancelButton = cancelButton;
    this.switchRecurringTask = switchRecurringTask;
    this.taskCategoryAutoCompleteTextView = taskCategoryAutoCompleteTextView;
    this.taskCategoryInputLayout = taskCategoryInputLayout;
    this.taskDescriptionEditText = taskDescriptionEditText;
    this.taskDescriptionInputLayout = taskDescriptionInputLayout;
    this.taskEnergyLevelAutoCompleteTextView = taskEnergyLevelAutoCompleteTextView;
    this.taskEnergyLevelInputLayout = taskEnergyLevelInputLayout;
    this.taskEstimatedHoursEditText = taskEstimatedHoursEditText;
    this.taskEstimatedHoursInputLayout = taskEstimatedHoursInputLayout;
    this.taskExpirationDateEditText = taskExpirationDateEditText;
    this.taskExpirationDateInputLayout = taskExpirationDateInputLayout;
    this.taskExpirationTimeEditText = taskExpirationTimeEditText;
    this.taskExpirationTimeInputLayout = taskExpirationTimeInputLayout;
    this.taskImportanceAutoCompleteTextView = taskImportanceAutoCompleteTextView;
    this.taskImportanceInputLayout = taskImportanceInputLayout;
    this.taskNameEditText = taskNameEditText;
    this.taskNameInputLayout = taskNameInputLayout;
    this.taskNotesEditText = taskNotesEditText;
    this.taskNotesInputLayout = taskNotesInputLayout;
    this.taskPriorityAutoCompleteTextView = taskPriorityAutoCompleteTextView;
    this.taskPriorityInputLayout = taskPriorityInputLayout;
    this.taskReminderAutoCompleteTextView = taskReminderAutoCompleteTextView;
    this.taskReminderInputLayout = taskReminderInputLayout;
    this.taskTagsEditText = taskTagsEditText;
    this.taskTagsInputLayout = taskTagsInputLayout;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAddEditTaskEnhancedBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAddEditTaskEnhancedBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_add_edit_task_enhanced, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAddEditTaskEnhancedBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addTaskButton;
      MaterialButton addTaskButton = ViewBindings.findChildViewById(rootView, id);
      if (addTaskButton == null) {
        break missingId;
      }

      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.switchRecurringTask;
      MaterialSwitch switchRecurringTask = ViewBindings.findChildViewById(rootView, id);
      if (switchRecurringTask == null) {
        break missingId;
      }

      id = R.id.taskCategoryAutoCompleteTextView;
      AutoCompleteTextView taskCategoryAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskCategoryAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskCategoryInputLayout;
      TextInputLayout taskCategoryInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskCategoryInputLayout == null) {
        break missingId;
      }

      id = R.id.taskDescriptionEditText;
      TextInputEditText taskDescriptionEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionEditText == null) {
        break missingId;
      }

      id = R.id.taskDescriptionInputLayout;
      TextInputLayout taskDescriptionInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionInputLayout == null) {
        break missingId;
      }

      id = R.id.taskEnergyLevelAutoCompleteTextView;
      AutoCompleteTextView taskEnergyLevelAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskEnergyLevelAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskEnergyLevelInputLayout;
      TextInputLayout taskEnergyLevelInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskEnergyLevelInputLayout == null) {
        break missingId;
      }

      id = R.id.taskEstimatedHoursEditText;
      TextInputEditText taskEstimatedHoursEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskEstimatedHoursEditText == null) {
        break missingId;
      }

      id = R.id.taskEstimatedHoursInputLayout;
      TextInputLayout taskEstimatedHoursInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskEstimatedHoursInputLayout == null) {
        break missingId;
      }

      id = R.id.taskExpirationDateEditText;
      TextInputEditText taskExpirationDateEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationDateEditText == null) {
        break missingId;
      }

      id = R.id.taskExpirationDateInputLayout;
      TextInputLayout taskExpirationDateInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationDateInputLayout == null) {
        break missingId;
      }

      id = R.id.taskExpirationTimeEditText;
      TextInputEditText taskExpirationTimeEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationTimeEditText == null) {
        break missingId;
      }

      id = R.id.taskExpirationTimeInputLayout;
      TextInputLayout taskExpirationTimeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskExpirationTimeInputLayout == null) {
        break missingId;
      }

      id = R.id.taskImportanceAutoCompleteTextView;
      AutoCompleteTextView taskImportanceAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskImportanceAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskImportanceInputLayout;
      TextInputLayout taskImportanceInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskImportanceInputLayout == null) {
        break missingId;
      }

      id = R.id.taskNameEditText;
      TextInputEditText taskNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskNameEditText == null) {
        break missingId;
      }

      id = R.id.taskNameInputLayout;
      TextInputLayout taskNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskNameInputLayout == null) {
        break missingId;
      }

      id = R.id.taskNotesEditText;
      TextInputEditText taskNotesEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskNotesEditText == null) {
        break missingId;
      }

      id = R.id.taskNotesInputLayout;
      TextInputLayout taskNotesInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskNotesInputLayout == null) {
        break missingId;
      }

      id = R.id.taskPriorityAutoCompleteTextView;
      AutoCompleteTextView taskPriorityAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskPriorityAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskPriorityInputLayout;
      TextInputLayout taskPriorityInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskPriorityInputLayout == null) {
        break missingId;
      }

      id = R.id.taskReminderAutoCompleteTextView;
      AutoCompleteTextView taskReminderAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskReminderAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskReminderInputLayout;
      TextInputLayout taskReminderInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskReminderInputLayout == null) {
        break missingId;
      }

      id = R.id.taskTagsEditText;
      TextInputEditText taskTagsEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskTagsEditText == null) {
        break missingId;
      }

      id = R.id.taskTagsInputLayout;
      TextInputLayout taskTagsInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskTagsInputLayout == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAddEditTaskEnhancedBinding((CoordinatorLayout) rootView, addTaskButton,
          appBarLayout, cancelButton, switchRecurringTask, taskCategoryAutoCompleteTextView,
          taskCategoryInputLayout, taskDescriptionEditText, taskDescriptionInputLayout,
          taskEnergyLevelAutoCompleteTextView, taskEnergyLevelInputLayout,
          taskEstimatedHoursEditText, taskEstimatedHoursInputLayout, taskExpirationDateEditText,
          taskExpirationDateInputLayout, taskExpirationTimeEditText, taskExpirationTimeInputLayout,
          taskImportanceAutoCompleteTextView, taskImportanceInputLayout, taskNameEditText,
          taskNameInputLayout, taskNotesEditText, taskNotesInputLayout,
          taskPriorityAutoCompleteTextView, taskPriorityInputLayout,
          taskReminderAutoCompleteTextView, taskReminderInputLayout, taskTagsEditText,
          taskTagsInputLayout, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
