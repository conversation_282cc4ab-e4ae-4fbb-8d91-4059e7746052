<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="report_table_row_tabular" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\report_table_row_tabular.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/report_table_row_tabular_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="215" endOffset="14"/></Target><Target id="@+id/reportUserNameTextView" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="32"/></Target><Target id="@+id/reportDailyRow" view="TableRow"><Expressions/><location startLine="64" startOffset="8" endLine="97" endOffset="18"/></Target><Target id="@+id/reportDailyTarget" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="80" endOffset="33"/></Target><Target id="@+id/reportDailyAchieved" view="TextView"><Expressions/><location startLine="81" startOffset="12" endLine="88" endOffset="31"/></Target><Target id="@+id/reportDailyPercentage" view="TextView"><Expressions/><location startLine="89" startOffset="12" endLine="96" endOffset="32"/></Target><Target id="@+id/reportMonthlyRow" view="TableRow"><Expressions/><location startLine="100" startOffset="8" endLine="133" endOffset="18"/></Target><Target id="@+id/reportMonthlyTarget" view="TextView"><Expressions/><location startLine="109" startOffset="12" endLine="116" endOffset="35"/></Target><Target id="@+id/reportMonthlyAchieved" view="TextView"><Expressions/><location startLine="117" startOffset="12" endLine="124" endOffset="33"/></Target><Target id="@+id/reportMonthlyPercentage" view="TextView"><Expressions/><location startLine="125" startOffset="12" endLine="132" endOffset="33"/></Target><Target id="@+id/reportQuarterlyRow" view="TableRow"><Expressions/><location startLine="136" startOffset="8" endLine="170" endOffset="18"/></Target><Target id="@+id/reportQuarterlyTarget" view="TextView"><Expressions/><location startLine="146" startOffset="12" endLine="153" endOffset="35"/></Target><Target id="@+id/reportQuarterlyAchieved" view="TextView"><Expressions/><location startLine="154" startOffset="12" endLine="161" endOffset="35"/></Target><Target id="@+id/reportQuarterlyPercentage" view="TextView"><Expressions/><location startLine="162" startOffset="12" endLine="169" endOffset="33"/></Target><Target id="@+id/reportAnnualRow" view="TableRow"><Expressions/><location startLine="173" startOffset="8" endLine="206" endOffset="18"/></Target><Target id="@+id/reportAnnualTarget" view="TextView"><Expressions/><location startLine="182" startOffset="12" endLine="189" endOffset="36"/></Target><Target id="@+id/reportAnnualAchieved" view="TextView"><Expressions/><location startLine="190" startOffset="12" endLine="197" endOffset="33"/></Target><Target id="@+id/reportAnnualPercentage" view="TextView"><Expressions/><location startLine="198" startOffset="12" endLine="205" endOffset="32"/></Target></Targets></Layout>