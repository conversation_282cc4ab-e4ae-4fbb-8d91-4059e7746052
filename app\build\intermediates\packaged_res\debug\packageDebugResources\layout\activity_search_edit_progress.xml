<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.SearchEditProgressActivity">

    <TextView
        android:id="@+id/searchPromptTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/search_edit_prompt"
        android:textAppearance="?attr/textAppearanceListItem"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="8dp"/>

    <Spinner
        android:id="@+id/dateSpinner"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:minHeight="48dp"
        app:layout_constraintTop_toBottomOf="@id/searchPromptTextView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/valueInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="@string/value_hint"
        app:layout_constraintTop_toBottomOf="@id/dateSpinner"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/valueEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:enabled="false" />
    </com.google.android.material.textfield.TextInputLayout>

    <Button
        android:id="@+id/saveButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/save_button"
        android:enabled="false"
        app:layout_constraintTop_toBottomOf="@id/valueInputLayout"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/deleteButton"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:text="@string/delete_entry_button"
        android:enabled="false"
        android:textColor="?attr/colorError"
        app:layout_constraintTop_toTopOf="@id/saveButton"
        app:layout_constraintBottom_toBottomOf="@id/saveButton"
        app:layout_constraintEnd_toStartOf="@id/saveButton" />

</androidx.constraintlayout.widget.ConstraintLayout>
