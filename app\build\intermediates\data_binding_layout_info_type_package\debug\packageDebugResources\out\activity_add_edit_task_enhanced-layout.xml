<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_edit_task_enhanced" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_add_edit_task_enhanced.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_add_edit_task_enhanced_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="461" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="23" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="47"/></Target><Target id="@+id/taskNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="60" startOffset="20" endLine="77" endOffset="75"/></Target><Target id="@+id/taskNameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="72" startOffset="24" endLine="76" endOffset="66"/></Target><Target id="@+id/taskDescriptionInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="80" startOffset="20" endLine="98" endOffset="75"/></Target><Target id="@+id/taskDescriptionEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="92" startOffset="24" endLine="97" endOffset="50"/></Target><Target id="@+id/taskCategoryInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="101" startOffset="20" endLine="118" endOffset="75"/></Target><Target id="@+id/taskCategoryAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="113" startOffset="24" endLine="117" endOffset="54"/></Target><Target id="@+id/taskPriorityInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="153" startOffset="24" endLine="171" endOffset="79"/></Target><Target id="@+id/taskPriorityAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="166" startOffset="28" endLine="170" endOffset="58"/></Target><Target id="@+id/taskImportanceInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="174" startOffset="24" endLine="192" endOffset="79"/></Target><Target id="@+id/taskImportanceAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="187" startOffset="28" endLine="191" endOffset="58"/></Target><Target id="@+id/taskEnergyLevelInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="197" startOffset="20" endLine="214" endOffset="75"/></Target><Target id="@+id/taskEnergyLevelAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="209" startOffset="24" endLine="213" endOffset="54"/></Target><Target id="@+id/taskExpirationDateInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="243" startOffset="20" endLine="265" endOffset="75"/></Target><Target id="@+id/taskExpirationDateEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="258" startOffset="24" endLine="264" endOffset="54"/></Target><Target id="@+id/taskExpirationTimeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="268" startOffset="20" endLine="290" endOffset="75"/></Target><Target id="@+id/taskExpirationTimeEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="283" startOffset="24" endLine="289" endOffset="54"/></Target><Target id="@+id/taskEstimatedHoursInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="293" startOffset="20" endLine="310" endOffset="75"/></Target><Target id="@+id/taskEstimatedHoursEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="305" startOffset="24" endLine="309" endOffset="63"/></Target><Target id="@+id/taskReminderInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="313" startOffset="20" endLine="329" endOffset="75"/></Target><Target id="@+id/taskReminderAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="324" startOffset="24" endLine="328" endOffset="54"/></Target><Target id="@+id/taskTagsInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="358" startOffset="20" endLine="375" endOffset="75"/></Target><Target id="@+id/taskTagsEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="370" startOffset="24" endLine="374" endOffset="54"/></Target><Target id="@+id/taskNotesInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="378" startOffset="20" endLine="396" endOffset="75"/></Target><Target id="@+id/taskNotesEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="390" startOffset="24" endLine="395" endOffset="50"/></Target><Target id="@+id/switchRecurringTask" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="412" startOffset="24" endLine="415" endOffset="66"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="431" startOffset="16" endLine="441" endOffset="44"/></Target><Target id="@+id/addTaskButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="443" startOffset="16" endLine="455" endOffset="44"/></Target></Targets></Layout>