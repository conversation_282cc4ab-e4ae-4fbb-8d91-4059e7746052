// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class KpiDetailItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView kpiAchievedPercentageTextView;

  @NonNull
  public final TextView kpiAnnualPercentageTextView;

  @NonNull
  public final TextView kpiMonthlyPercentageTextView;

  @NonNull
  public final TextView kpiNameTextView;

  @NonNull
  public final ProgressBar kpiProgressBar;

  private KpiDetailItemBinding(@NonNull LinearLayout rootView,
      @NonNull TextView kpiAchievedPercentageTextView,
      @NonNull TextView kpiAnnualPercentageTextView, @NonNull TextView kpiMonthlyPercentageTextView,
      @NonNull TextView kpiNameTextView, @NonNull ProgressBar kpiProgressBar) {
    this.rootView = rootView;
    this.kpiAchievedPercentageTextView = kpiAchievedPercentageTextView;
    this.kpiAnnualPercentageTextView = kpiAnnualPercentageTextView;
    this.kpiMonthlyPercentageTextView = kpiMonthlyPercentageTextView;
    this.kpiNameTextView = kpiNameTextView;
    this.kpiProgressBar = kpiProgressBar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static KpiDetailItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static KpiDetailItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.kpi_detail_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static KpiDetailItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.kpiAchievedPercentageTextView;
      TextView kpiAchievedPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiAchievedPercentageTextView == null) {
        break missingId;
      }

      id = R.id.kpiAnnualPercentageTextView;
      TextView kpiAnnualPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiAnnualPercentageTextView == null) {
        break missingId;
      }

      id = R.id.kpiMonthlyPercentageTextView;
      TextView kpiMonthlyPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiMonthlyPercentageTextView == null) {
        break missingId;
      }

      id = R.id.kpiNameTextView;
      TextView kpiNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiNameTextView == null) {
        break missingId;
      }

      id = R.id.kpiProgressBar;
      ProgressBar kpiProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (kpiProgressBar == null) {
        break missingId;
      }

      return new KpiDetailItemBinding((LinearLayout) rootView, kpiAchievedPercentageTextView,
          kpiAnnualPercentageTextView, kpiMonthlyPercentageTextView, kpiNameTextView,
          kpiProgressBar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
