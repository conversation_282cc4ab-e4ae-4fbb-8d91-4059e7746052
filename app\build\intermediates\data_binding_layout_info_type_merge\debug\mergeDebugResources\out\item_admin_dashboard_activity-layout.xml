<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_admin_dashboard_activity" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_admin_dashboard_activity.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/activityCard"><Targets><Target id="@+id/activityCard" tag="layout/item_admin_dashboard_activity_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="58" endOffset="51"/></Target><Target id="@+id/activityUser" view="TextView"><Expressions/><location startLine="27" startOffset="12" endLine="35" endOffset="42"/></Target><Target id="@+id/activityTime" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="43" endOffset="41"/></Target><Target id="@+id/activityAction" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="54" endOffset="37"/></Target></Targets></Layout>