// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogProgressUpdateBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final SeekBar seekBarProgress;

  @NonNull
  public final TextView tvProgressValue;

  private DialogProgressUpdateBinding(@NonNull LinearLayout rootView,
      @NonNull SeekBar seekBarProgress, @NonNull TextView tvProgressValue) {
    this.rootView = rootView;
    this.seekBarProgress = seekBarProgress;
    this.tvProgressValue = tvProgressValue;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogProgressUpdateBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogProgressUpdateBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_progress_update, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogProgressUpdateBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.seekBarProgress;
      SeekBar seekBarProgress = ViewBindings.findChildViewById(rootView, id);
      if (seekBarProgress == null) {
        break missingId;
      }

      id = R.id.tvProgressValue;
      TextView tvProgressValue = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressValue == null) {
        break missingId;
      }

      return new DialogProgressUpdateBinding((LinearLayout) rootView, seekBarProgress,
          tvProgressValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
