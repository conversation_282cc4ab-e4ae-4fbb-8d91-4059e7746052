<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".ui.ReportActivity">

    <!-- Modern App Bar with subtle elevation -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:elevation="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:navigationIcon="@drawable/ic_baseline_arrow_back_24"
            app:title="Interactive Performance Report"
            app:titleTextAppearance="@style/TextAppearance.MaterialComponents.Headline6"
            app:titleTextColor="@color/black" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Thin divider below app bar -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        app:layout_anchor="@id/appBarLayout"
        app:layout_anchorGravity="bottom" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="vertical"
        android:scrollbarThumbVertical="@android:color/darker_gray"
        android:fadeScrollbars="false"
        android:scrollbarSize="5dp"
        android:visibility="visible"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/reportConstraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:descendantFocusability="blocksDescendants">

            <!-- KPI Selector with modern styling -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/kpiSelectorLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:hint="Select KPI"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusTopStart="12dp"
                app:boxStrokeColor="#BDBDBD"
                app:hintTextColor="#757575"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <AutoCompleteTextView
                    android:id="@+id/kpiSelectorAutoCompleteTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:inputType="none"
                    android:text="Global"
                    android:textColor="#212121"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- User Filter Selector -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/userFilterLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Filter Users"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusTopStart="12dp"
                app:boxStrokeColor="#BDBDBD"
                app:hintTextColor="#757575"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/kpiSelectorLayout">

                <AutoCompleteTextView
                    android:id="@+id/userFilterAutoCompleteTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:inputType="none"
                    android:text="All Users"
                    android:textColor="#212121"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Start Date Input with calendar icon -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/startDateInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="8dp"
                android:hint="From"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusTopStart="12dp"
                app:boxStrokeColor="#BDBDBD"
                app:endIconDrawable="@drawable/ic_baseline_calendar_today_24"
                app:endIconMode="custom"
                app:endIconTint="#757575"
                app:hintTextColor="#757575"
                app:layout_constraintEnd_toStartOf="@+id/endDateInputLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/userFilterLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/startDateEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:textColor="#212121"
                    android:textSize="16sp"
                    tools:text="2025-01-01" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- End Date Input with calendar icon -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/endDateInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:hint="To"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusTopStart="12dp"
                app:boxStrokeColor="#BDBDBD"
                app:endIconDrawable="@drawable/ic_baseline_calendar_today_24"
                app:endIconMode="custom"
                app:endIconTint="#757575"
                app:hintTextColor="#757575"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/startDateInputLayout"
                app:layout_constraintTop_toTopOf="@+id/startDateInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/endDateEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:textColor="#212121"
                    android:textSize="16sp"
                    tools:text="2025-05-21" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Modern GO Button with gradient background -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/goButton"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_marginTop="28dp"
                android:background="@drawable/blue_gradient_button_background"
                android:letterSpacing="0.1"
                android:text="GO"
                android:textAllCaps="true"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                app:cornerRadius="30dp"
                app:elevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/startDateInputLayout"
                app:rippleColor="#1565C0" />

            <!-- Content Container for Report Data -->
            <androidx.cardview.widget.CardView
                android:id="@+id/reportContentCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/goButton">

                <!-- This will be populated with report data -->
                <LinearLayout
                    android:id="@+id/reportContentContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/kpiNameLabel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#F5F5F5"
                        android:padding="16dp"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="#212121"
                        android:textStyle="bold"
                        tools:text="Wellness Card" />

                    <include
                        android:id="@+id/compactReportTable"
                        layout="@layout/compact_report_table"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Chart Filter Options -->
            <RadioGroup
                android:id="@+id/summaryChartFilterRadioGroup"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="#F5F5F5"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/reportContentCard">

                <RadioButton
                    android:id="@+id/radioDaily"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:buttonTint="#2196F3"
                    android:text="Daily"
                    android:textColor="#212121" />

                <RadioButton
                    android:id="@+id/radioMonthly"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:buttonTint="#2196F3"
                    android:text="Monthly"
                    android:textColor="#212121" />

                <RadioButton
                    android:id="@+id/radioAnnual"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:buttonTint="#2196F3"
                    android:checked="true"
                    android:text="Annual"
                    android:textColor="#212121" />
            </RadioGroup>

            <!-- Summary Bar Chart Section -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/barChartContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/summaryChartFilterRadioGroup">

                <TextView
                    android:id="@+id/barChartTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Summary Chart"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:textColor="#212121"
                    android:layout_marginStart="4dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btnShareBarChart" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnShareBarChart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Share"
                    android:textSize="12sp"
                    app:icon="@drawable/ic_share_24"
                    app:iconSize="18dp"
                    app:iconGravity="textStart"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.github.mikephil.charting.charts.BarChart
                    android:id="@+id/summaryBarChart"
                    android:layout_width="0dp"
                    android:layout_height="250dp"
                    android:layout_marginTop="8dp"
                    android:paddingBottom="20dp"
                    android:visibility="visible"
                    android:background="#FFFFFF"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/barChartTitle" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Trend Chart Section with Share Button -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/trendChartContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="24dp"
                android:paddingTop="16dp"
                android:background="#F5F5F5"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/barChartContainer"
                app:layout_constraintVertical_bias="0.0">

                <TextView
                    android:id="@+id/trendChartTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Trend Chart"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:textColor="#212121"
                    android:layout_marginStart="4dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btnShareTrendChart" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnShareTrendChart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Share"
                    android:textSize="12sp"
                    app:icon="@drawable/ic_share_24"
                    app:iconSize="18dp"
                    app:iconGravity="textStart"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/trendChart"
                    android:layout_width="0dp"
                    android:layout_height="250dp"
                    android:layout_marginTop="8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/trendChartTitle" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
