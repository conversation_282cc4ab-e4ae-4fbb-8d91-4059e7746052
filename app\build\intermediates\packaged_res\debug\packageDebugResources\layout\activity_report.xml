<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F9FA"
    tools:context=".ui.ReportActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_500"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:navigationIcon="@drawable/ic_baseline_arrow_back_24"
            app:title="Performance Report"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/kpiSelectorLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:hint="Select KPI"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <AutoCompleteTextView
                    android:id="@+id/kpiSelectorAutoCompleteTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:focusable="false"
                    android:clickable="true" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/startDateInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="8dp"
                android:hint="From"
                app:layout_constraintEnd_toStartOf="@+id/endDateInputLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/kpiSelectorLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/startDateEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:clickable="true"
                    tools:text="2025-01-01" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/endDateInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:hint="To"
                app:boxCornerRadiusBottomEnd="8dp"
                app:boxCornerRadiusBottomStart="8dp"
                app:boxCornerRadiusTopEnd="8dp"
                app:boxCornerRadiusTopStart="8dp"
                app:endIconMode="custom"
                app:endIconDrawable="@drawable/ic_baseline_calendar_today_24"
                app:endIconTint="@color/purple_500"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/startDateInputLayout"
                app:layout_constraintTop_toTopOf="@+id/startDateInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/endDateEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:clickable="true"
                    tools:text="2025-04-11" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/goButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="GO"
                android:textAllCaps="true"
                android:textSize="16sp"
                android:textStyle="bold"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:backgroundTint="@color/purple_500"
                app:cornerRadius="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/startDateInputLayout" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/reportTableCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/goButton">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/kpiNameLabel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#F0F0F0"
                        android:padding="12dp"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="?android:attr/textColorPrimary"
                        android:textStyle="bold"
                        tools:text="Wellness Card" />

                    <include
                        android:id="@+id/compactReportTable"
                        layout="@layout/compact_report_table"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/trendChart"
                android:layout_width="0dp"
                android:layout_height="250dp"
                android:layout_marginTop="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/reportTableCard" />

            <!-- Add RadioGroup for filtering the summary chart -->
            <RadioGroup
                android:id="@+id/summaryChartFilterRadioGroup"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/trendChart">

                <RadioButton
                    android:id="@+id/radioDaily"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Daily"
                    android:layout_marginEnd="16dp"/>

                <RadioButton
                    android:id="@+id/radioMonthly"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Monthly"
                    android:layout_marginEnd="16dp"/>

                <RadioButton
                    android:id="@+id/radioAnnual"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Annual"
                    android:checked="true"/> <!-- Default to Annual -->
            </RadioGroup>

            <com.github.mikephil.charting.charts.BarChart
                android:id="@+id/summaryBarChart"
                android:layout_width="0dp"
                android:layout_height="280dp"
                android:layout_marginTop="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/summaryChartFilterRadioGroup" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
