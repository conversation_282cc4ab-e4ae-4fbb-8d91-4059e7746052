<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="compact_report_table_row" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\compact_report_table_row.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.TableLayout"><Targets><Target tag="layout/compact_report_table_row_0" view="TableLayout"><Expressions/><location startLine="1" startOffset="0" endLine="96" endOffset="13"/></Target><Target id="@+id/doctorNameTextView" view="TextView"><Expressions/><location startLine="13" startOffset="8" endLine="19" endOffset="36"/></Target><Target id="@+id/dailyTargetTextView" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="28" endOffset="30"/></Target><Target id="@+id/dailyAchievedTextView" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="36" endOffset="30"/></Target><Target id="@+id/dailyPercentageTextView" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="44" endOffset="30"/></Target><Target id="@+id/monthlyTargetTextView" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="53" endOffset="32"/></Target><Target id="@+id/monthlyAchievedTextView" view="TextView"><Expressions/><location startLine="55" startOffset="8" endLine="61" endOffset="32"/></Target><Target id="@+id/monthlyPercentageTextView" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="69" endOffset="30"/></Target><Target id="@+id/annualTargetTextView" view="TextView"><Expressions/><location startLine="72" startOffset="8" endLine="78" endOffset="33"/></Target><Target id="@+id/annualAchievedTextView" view="TextView"><Expressions/><location startLine="80" startOffset="8" endLine="86" endOffset="33"/></Target><Target id="@+id/annualPercentageTextView" view="TextView"><Expressions/><location startLine="88" startOffset="8" endLine="94" endOffset="30"/></Target></Targets></Layout>