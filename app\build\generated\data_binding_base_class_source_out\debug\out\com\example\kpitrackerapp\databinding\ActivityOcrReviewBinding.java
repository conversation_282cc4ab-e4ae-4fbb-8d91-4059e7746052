// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityOcrReviewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnConfirmOcrImport;

  @NonNull
  public final RecyclerView rvOcrReview;

  private ActivityOcrReviewBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnConfirmOcrImport, @NonNull RecyclerView rvOcrReview) {
    this.rootView = rootView;
    this.btnConfirmOcrImport = btnConfirmOcrImport;
    this.rvOcrReview = rvOcrReview;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityOcrReviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityOcrReviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_ocr_review, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityOcrReviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_confirm_ocr_import;
      Button btnConfirmOcrImport = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirmOcrImport == null) {
        break missingId;
      }

      id = R.id.rv_ocr_review;
      RecyclerView rvOcrReview = ViewBindings.findChildViewById(rootView, id);
      if (rvOcrReview == null) {
        break missingId;
      }

      return new ActivityOcrReviewBinding((ConstraintLayout) rootView, btnConfirmOcrImport,
          rvOcrReview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
