<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/chat_background"
    tools:context=".ui.ChatActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:elevation="4dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/purple_700"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@android:color/white"
            app:subtitleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Messages RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/messagesRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="8dp"
            android:clipToPadding="false"
            tools:listitem="@layout/item_chat_message_sent" />

        <!-- Message Input Area -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/message_input_background"
            android:elevation="8dp"
            android:orientation="horizontal"
            android:padding="8dp">

            <!-- Attach Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/attachButton"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="bottom"
                android:layout_marginEnd="8dp"
                app:icon="@drawable/ic_baseline_attach_file_24"
                app:iconTint="@color/purple_700" />

            <!-- Message Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:boxBackgroundMode="filled"
                app:boxCornerRadiusBottomEnd="20dp"
                app:boxCornerRadiusBottomStart="20dp"
                app:boxCornerRadiusTopEnd="20dp"
                app:boxCornerRadiusTopStart="20dp"
                app:hintEnabled="false">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/messageEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Type a message..."
                    android:inputType="textCapSentences|textMultiLine"
                    android:maxLines="4"
                    android:minHeight="48dp"
                    android:padding="12dp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Send Button -->
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/sendButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                app:fabSize="mini"
                app:srcCompat="@drawable/ic_baseline_send_24"
                app:tint="@android:color/white" />

        </LinearLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
