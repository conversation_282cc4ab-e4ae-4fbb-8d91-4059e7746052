com.example.kpitrackerapp-sqlite-framework-2.4.0-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0123e8542d77a2007d3d056311003214\transformed\sqlite-framework-2.4.0\res
com.example.kpitrackerapp-lifecycle-livedata-2.7.0-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05a340700c22a3597ac7d257261f1b6c\transformed\lifecycle-livedata-2.7.0\res
com.example.kpitrackerapp-jetified-play-services-measurement-api-21.5.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\res
com.example.kpitrackerapp-jetified-lifecycle-runtime-ktx-2.7.0-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09298e2e19478ba8444cfb3a5623ad7e\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.kpitrackerapp-jetified-fragment-ktx-1.6.2-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a2d034fef6e6e0aee6f00e4e6e6339c\transformed\jetified-fragment-ktx-1.6.2\res
com.example.kpitrackerapp-work-runtime-ktx-2.9.0-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0adee79537555c43ac8dbc761db8e6f4\transformed\work-runtime-ktx-2.9.0\res
com.example.kpitrackerapp-jetified-ads-adservices-java-1.0.0-beta05-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b1c8c0dcde34b5677befcb35e576a63\transformed\jetified-ads-adservices-java-1.0.0-beta05\res
com.example.kpitrackerapp-jetified-savedstate-ktx-1.2.1-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d375bcc68308e0027ed42fce2bdab01\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.kpitrackerapp-appcompat-1.6.1-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fb6da15b525de990376081401abcb58\transformed\appcompat-1.6.1\res
com.example.kpitrackerapp-jetified-zxing-android-embedded-4.3.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b029f212d90e38d6ae5a8afe6446dd6\transformed\jetified-zxing-android-embedded-4.3.0\res
com.example.kpitrackerapp-fragment-1.6.2-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f2b19577bbc547083ca6393dbc3f2e6\transformed\fragment-1.6.2\res
com.example.kpitrackerapp-jetified-firebase-common-20.4.2-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\res
com.example.kpitrackerapp-jetified-play-services-base-18.1.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\res
com.example.kpitrackerapp-material-1.11.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b4f88c4f8ab3af336856b4860e45b2\transformed\material-1.11.0\res
com.example.kpitrackerapp-jetified-play-services-basement-18.1.0-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\res
com.example.kpitrackerapp-cardview-1.0.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\466ec11b95e2afc60708dfd373d39894\transformed\cardview-1.0.0\res
com.example.kpitrackerapp-lifecycle-livedata-core-2.7.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ef906d675c9ff59f13bc77efa7110db\transformed\lifecycle-livedata-core-2.7.0\res
com.example.kpitrackerapp-jetified-core-ktx-1.12.0-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55dfb5441f0748e018b19fd6288aa24f\transformed\jetified-core-ktx-1.12.0\res
com.example.kpitrackerapp-jetified-lifecycle-service-2.7.0-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a9bf94ef2e119e15d1dcfa1f800c8d\transformed\jetified-lifecycle-service-2.7.0\res
com.example.kpitrackerapp-jetified-colorpicker-2.3-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57a95fe910a95177c0d9db83b43be3ff\transformed\jetified-colorpicker-2.3\res
com.example.kpitrackerapp-jetified-savedstate-1.2.1-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab0c7f26a78ec11892ac5ccf705aa40\transformed\jetified-savedstate-1.2.1\res
com.example.kpitrackerapp-jetified-lifecycle-viewmodel-ktx-2.7.0-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61fe5a73d23a08e7055621849d178174\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.kpitrackerapp-jetified-ads-adservices-1.0.0-beta05-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\res
com.example.kpitrackerapp-jetified-emoji2-views-helper-1.2.0-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68dfe5edb51352c9eb13bfe99e7f21bb\transformed\jetified-emoji2-views-helper-1.2.0\res
com.example.kpitrackerapp-jetified-glide-4.16.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fb4d29465920dd22bc2e2ed8e81580b\transformed\jetified-glide-4.16.0\res
com.example.kpitrackerapp-constraintlayout-2.1.4-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\743a2ee51e6a45f55619996925fb518f\transformed\constraintlayout-2.1.4\res
com.example.kpitrackerapp-lifecycle-viewmodel-2.7.0-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\749cdf56299307a36fb2af984a0aed10\transformed\lifecycle-viewmodel-2.7.0\res
com.example.kpitrackerapp-work-runtime-2.9.0-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\res
com.example.kpitrackerapp-jetified-firebase-messaging-23.4.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\res
com.example.kpitrackerapp-drawerlayout-1.1.1-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fcc1079b267bd6f4fb71b832ae3a418\transformed\drawerlayout-1.1.1\res
com.example.kpitrackerapp-jetified-annotation-experimental-1.3.0-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87a3b027b34b103960db8de91bf60954\transformed\jetified-annotation-experimental-1.3.0\res
com.example.kpitrackerapp-jetified-lifecycle-process-2.7.0-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\res
com.example.kpitrackerapp-lifecycle-runtime-2.7.0-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\981128d3bc654d4a32368395985627b9\transformed\lifecycle-runtime-2.7.0\res
com.example.kpitrackerapp-jetified-startup-runtime-1.1.1-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98628f64b3e01be0c2601c77caad5415\transformed\jetified-startup-runtime-1.1.1\res
com.example.kpitrackerapp-jetified-lifecycle-viewmodel-savedstate-2.7.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0a9357f9b407134171ade2dd66ad7c6\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.kpitrackerapp-jetified-emoji2-1.2.0-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\res
com.example.kpitrackerapp-coordinatorlayout-1.1.0-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa133285454d7367dd83a78f6994c87f\transformed\coordinatorlayout-1.1.0\res
com.example.kpitrackerapp-jetified-customview-poolingcontainer-1.0.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aac439f03980d2a4711de8aa7c57086e\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.example.kpitrackerapp-room-runtime-2.6.1-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\res
com.example.kpitrackerapp-sqlite-2.4.0-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afe7e761e708f17e5c6b88d1014a8ea5\transformed\sqlite-2.4.0\res
com.example.kpitrackerapp-transition-1.2.0-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3360052e5618112f3d7b8409093cae1\transformed\transition-1.2.0\res
com.example.kpitrackerapp-jetified-activity-1.8.2-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7b6738e783e3b74357a778a678a3116\transformed\jetified-activity-1.8.2\res
com.example.kpitrackerapp-jetified-flexbox-3.0.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c502974973688f02338845ea213ecd\transformed\jetified-flexbox-3.0.0\res
com.example.kpitrackerapp-recyclerview-1.3.2-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cad49663aa80b748f1af80039f7ce980\transformed\recyclerview-1.3.2\res
com.example.kpitrackerapp-core-1.12.0-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\res
com.example.kpitrackerapp-jetified-room-ktx-2.6.1-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6b4beb555ce28f591026f155422c37c\transformed\jetified-room-ktx-2.6.1\res
com.example.kpitrackerapp-jetified-profileinstaller-1.3.0-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\res
com.example.kpitrackerapp-jetified-viewpager2-1.1.0-beta02-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3b983316129c32f93cfbcbda0ab7ca8\transformed\jetified-viewpager2-1.1.0-beta02\res
com.example.kpitrackerapp-jetified-lifecycle-livedata-core-ktx-2.7.0-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb8e259bb5ddd32d7fe62d25131ffea3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.kpitrackerapp-jetified-activity-ktx-1.8.2-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eba8184dc645491236fed5ba601f5f97\transformed\jetified-activity-ktx-1.8.2\res
com.example.kpitrackerapp-jetified-appcompat-resources-1.6.1-50 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ab5f2429abfc02d09195ac341f034b\transformed\jetified-appcompat-resources-1.6.1\res
com.example.kpitrackerapp-core-runtime-2.2.0-51 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe401d04d12c0d2105eba9121a08e394\transformed\core-runtime-2.2.0\res
com.example.kpitrackerapp-pngs-52 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build\generated\res\pngs\debug
com.example.kpitrackerapp-res-53 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build\generated\res\processDebugGoogleServices
com.example.kpitrackerapp-resValues-54 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build\generated\res\resValues\debug
com.example.kpitrackerapp-packageDebugResources-55 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.kpitrackerapp-packageDebugResources-56 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.kpitrackerapp-debug-57 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.kpitrackerapp-debug-58 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\debug\res
com.example.kpitrackerapp-main-59 D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\res
