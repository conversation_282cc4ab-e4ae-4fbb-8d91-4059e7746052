<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.kpitrackerapp"
    android:versionCode="1"
    android:versionName="1.0-debug" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="34" />

    <!-- Required for notifications on Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Required for running foreground services (used by WorkManager when setForeground is called) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- Required for Camera -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- Required for Calendar Integration -->
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <!-- Required for Speech Recognition -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Required for Location Services -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- Required for Setting Alarms -->
    <uses-permission android:name="android.permission.SET_ALARM" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />

    <!-- Declare camera feature, but don't require it if app can function without -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <!-- Queries for specific apps we want to detect (Android 11+ requirement) -->
    <queries>

        <!-- WhatsApp Regular -->
        <package android:name="com.whatsapp" />
        <!-- WhatsApp Business -->
        <package android:name="com.whatsapp.w4b" />
        <!-- Gmail -->
        <package android:name="com.google.android.gm" />
        <!-- Email intent -->
        <intent>
            <action android:name="android.intent.action.SENDTO" />

            <data android:scheme="mailto" />
        </intent>
    </queries>

    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
    <!-- <uses-sdk android:minSdkVersion="14"/> -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />

    <permission
        android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.screen.landscape"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />

    <application
        android:name="com.example.kpitrackerapp.KpiTrackerApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.KPITrackerApp" >

        <!-- Optional: Request OCR module download on install/update -->
        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="ocr,ocr_arabic" />

        <activity
            android:name="com.example.kpitrackerapp.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" > <!-- Apply NoActionBar theme here -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.kpitrackerapp.ui.AddEditKpiActivity"
            android:label="@string/add_kpi_title"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- This is now for Tasks -->
        <activity
            android:name="com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity"
            android:label="@string/add_kpi_title"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name="com.example.kpitrackerapp.ui.KpiDetailActivity"
            android:label="@string/kpi_detail_title"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
        <activity
            android:name="com.example.kpitrackerapp.ui.ModernReportActivity"
            android:label="Interactive Performance Report"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name="com.example.kpitrackerapp.ui.ExpireManagementActivity"
            android:label="@string/action_expiry_management"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
        <activity
            android:name="com.example.kpitrackerapp.ui.SearchEditProgressActivity"
            android:label="@string/search_edit_progress_title"
            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
        <activity
            android:name="com.example.kpitrackerapp.ui.OcrActivity"
            android:label="@string/ocr_activity_title"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
        <activity
            android:name="com.example.kpitrackerapp.ui.OcrReviewActivity"
            android:label="@string/review_ocr_results_title"
            android:parentActivityName="com.example.kpitrackerapp.ui.OcrActivity" /> <!-- Parent is OcrActivity -->
        <!-- Removed SmartListAnalysisActivity declaration -->
        <activity
            android:name="com.example.kpitrackerapp.ui.ExcelImportActivity"
            android:label="Import from Excel"
            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
        <activity
            android:name="com.example.kpitrackerapp.ui.ExcelReviewActivity"
            android:label="Review Excel Import"
            android:parentActivityName="com.example.kpitrackerapp.ui.ExcelImportActivity" />
        <activity
            android:name="com.example.kpitrackerapp.ui.UserKpiListActivity"
            android:label="@string/user_kpi_list_title"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- Apply NoActionBar theme -->
        <activity
            android:name="com.example.kpitrackerapp.ui.TaskManagementActivity"
            android:label="Task Management"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name="com.example.kpitrackerapp.ui.TaskReportActivity"
            android:label="Task Reports"
            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Login and User Management Activities -->
        <activity
            android:name="com.example.kpitrackerapp.ui.LoginActivity"
            android:exported="false"
            android:label="Login"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name="com.example.kpitrackerapp.ui.CreateUserActivity"
            android:exported="false"
            android:label="Create User"
            android:parentActivityName="com.example.kpitrackerapp.ui.LoginActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Admin Dashboard Activity -->
        <activity
            android:name="com.example.kpitrackerapp.AdminDashboardActivity"
            android:exported="false"
            android:label="Admin Dashboard"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Product Management Activities -->
        <activity
            android:name="com.example.kpitrackerapp.ui.AddProductActivity"
            android:exported="false"
            android:label="Add Product"
            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
            android:theme="@style/Theme.KPITrackerApp" />
        <activity
            android:name="com.example.kpitrackerapp.ui.EditProductActivity"
            android:exported="false"
            android:label="Edit Product"
            android:parentActivityName="com.example.kpitrackerapp.ui.ProductListActivity"
            android:theme="@style/Theme.KPITrackerApp" />
        <activity
            android:name="com.example.kpitrackerapp.ui.ProductListActivity"
            android:exported="false"
            android:label="Product List"
            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
            android:theme="@style/Theme.KPITrackerApp" />
        <activity
            android:name="com.example.kpitrackerapp.ui.ExpiryTrackingActivity"
            android:exported="false"
            android:label="Expiry Tracking"
            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
            android:theme="@style/Theme.KPITrackerApp" />

        <!-- Chat Activities -->
        <activity
            android:name="com.example.kpitrackerapp.ui.ChatListActivity"
            android:exported="false"
            android:label="Messages"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name="com.example.kpitrackerapp.ui.ChatActivity"
            android:exported="false"
            android:label="Chat"
            android:parentActivityName="com.example.kpitrackerapp.ui.ChatListActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Date Converter Activity -->
        <activity
            android:name="com.example.kpitrackerapp.ui.DateConverterActivity"
            android:exported="false"
            android:label="محول التاريخ"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Task Reminder Settings Activity -->
        <activity
            android:name="com.example.kpitrackerapp.ui.TaskReminderSettingsActivity"
            android:exported="false"
            android:label="Task Reminder Settings"
            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Auto Send Settings Activity -->
        <activity
            android:name="com.example.kpitrackerapp.ui.AutoSendSettingsActivity"
            android:exported="false"
            android:label="Auto Send Settings"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Advanced Task Activity -->
        <activity
            android:name="com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity"
            android:exported="false"
            android:label="إضافة مهمة متقدمة"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Modern Add Task Activity -->
        <activity
            android:name="com.example.kpitrackerapp.ui.ModernAddTaskActivity"
            android:exported="false"
            android:label="Add New Task"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Pomodoro Timer Activity -->
        <activity
            android:name="com.example.kpitrackerapp.ui.PomodoroTimerActivity"
            android:exported="false"
            android:label="Pomodoro Timer"
            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Add other activities, services, etc. here -->
        <activity
            android:name="com.example.kpitrackerapp.ui.NotificationsActivity"
            android:exported="false"
            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />

        <!-- Firebase Messaging Service -->
        <service
            android:name="com.example.kpitrackerapp.services.KPIFirebaseMessagingService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Firebase Messaging default notification icon -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <!-- Firebase Messaging default notification color -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/purple_500" />

        <!-- FileProvider for sharing camera image URI -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.example.kpitrackerapp.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Disable WorkManager automatic initialization since we use Configuration.Provider -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.kpitrackerapp.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
            android:authorities="com.example.kpitrackerapp.mlkitinitprovider"
            android:exported="false"
            android:initOrder="99" />

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>

            <meta-data
                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
                android:value="true" />
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/ga_ad_services_config" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.example.kpitrackerapp.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />

        <activity
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:clearTaskOnLaunch="true"
            android:screenOrientation="sensorLandscape"
            android:stateNotNeeded="true"
            android:theme="@style/zxing_CaptureTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
    </application>

</manifest>