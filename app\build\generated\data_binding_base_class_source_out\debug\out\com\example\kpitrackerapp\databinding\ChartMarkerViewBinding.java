// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ChartMarkerViewBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView tvComparison;

  @NonNull
  public final TextView tvPercentage;

  @NonNull
  public final TextView tvTarget;

  @NonNull
  public final TextView tvUserName;

  @NonNull
  public final TextView tvValue;

  private ChartMarkerViewBinding(@NonNull CardView rootView, @NonNull TextView tvComparison,
      @NonNull TextView tvPercentage, @NonNull TextView tvTarget, @NonNull TextView tvUserName,
      @NonNull TextView tvValue) {
    this.rootView = rootView;
    this.tvComparison = tvComparison;
    this.tvPercentage = tvPercentage;
    this.tvTarget = tvTarget;
    this.tvUserName = tvUserName;
    this.tvValue = tvValue;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ChartMarkerViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ChartMarkerViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.chart_marker_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ChartMarkerViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tvComparison;
      TextView tvComparison = ViewBindings.findChildViewById(rootView, id);
      if (tvComparison == null) {
        break missingId;
      }

      id = R.id.tvPercentage;
      TextView tvPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvPercentage == null) {
        break missingId;
      }

      id = R.id.tvTarget;
      TextView tvTarget = ViewBindings.findChildViewById(rootView, id);
      if (tvTarget == null) {
        break missingId;
      }

      id = R.id.tvUserName;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      id = R.id.tvValue;
      TextView tvValue = ViewBindings.findChildViewById(rootView, id);
      if (tvValue == null) {
        break missingId;
      }

      return new ChartMarkerViewBinding((CardView) rootView, tvComparison, tvPercentage, tvTarget,
          tvUserName, tvValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
