// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.github.mikephil.charting.charts.LineChart;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityKpiDetailBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button addProgressButton;

  @NonNull
  public final LinearLayout addProgressButtonLayout;

  @NonNull
  public final CircularProgressIndicator annualCircularProgressIndicator;

  @NonNull
  public final FrameLayout annualProgressIndicatorContainer;

  @NonNull
  public final LinearLayout annualProgressLayout;

  @NonNull
  public final TextView annualProgressPercentageText;

  @NonNull
  public final TextView annualProgressPercentageTextView;

  @NonNull
  public final TextView averagePerDayTextView;

  @NonNull
  public final CircularProgressIndicator dailyCircularProgressIndicator;

  @NonNull
  public final FrameLayout dailyProgressIndicatorContainer;

  @NonNull
  public final LinearLayout dailyProgressLayout;

  @NonNull
  public final TextView dailyProgressPercentageText;

  @NonNull
  public final TextView dailyProgressPercentageTextView;

  @NonNull
  public final TextView detailKpiCurrentValueTextView;

  @NonNull
  public final TextView detailKpiDailyTargetValueTextView;

  @NonNull
  public final TextView detailKpiMonthlyTargetValueTextView;

  @NonNull
  public final TextView detailKpiNameTextView;

  @NonNull
  public final TextView detailKpiProgressLabelTextView;

  @NonNull
  public final TextView detailKpiTargetValueTextView;

  @NonNull
  public final LineChart kpiLineChart;

  @NonNull
  public final TextView lastEntryDateTextView;

  @NonNull
  public final TextView lineChartTitle;

  @NonNull
  public final Spinner monthFilterSpinner;

  @NonNull
  public final CircularProgressIndicator monthlyCircularProgressIndicator;

  @NonNull
  public final FrameLayout monthlyProgressIndicatorContainer;

  @NonNull
  public final LinearLayout monthlyProgressLayout;

  @NonNull
  public final TextView monthlyProgressPercentageText;

  @NonNull
  public final TextView monthlyProgressPercentageTextView;

  @NonNull
  public final LinearLayout progressIndicatorsLayout;

  @NonNull
  public final TextView progressSummaryTextView;

  @NonNull
  public final MaterialButton quickAdd10Button;

  @NonNull
  public final MaterialButton quickAdd50Button;

  @NonNull
  public final MaterialCardView quickStatsCard;

  @NonNull
  public final NestedScrollView scrollView;

  @NonNull
  public final MaterialCardView targetsCard;

  @NonNull
  public final TextView targetsTitleTextView;

  @NonNull
  public final TextView thisWeekProgressTextView;

  private ActivityKpiDetailBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button addProgressButton, @NonNull LinearLayout addProgressButtonLayout,
      @NonNull CircularProgressIndicator annualCircularProgressIndicator,
      @NonNull FrameLayout annualProgressIndicatorContainer,
      @NonNull LinearLayout annualProgressLayout, @NonNull TextView annualProgressPercentageText,
      @NonNull TextView annualProgressPercentageTextView, @NonNull TextView averagePerDayTextView,
      @NonNull CircularProgressIndicator dailyCircularProgressIndicator,
      @NonNull FrameLayout dailyProgressIndicatorContainer,
      @NonNull LinearLayout dailyProgressLayout, @NonNull TextView dailyProgressPercentageText,
      @NonNull TextView dailyProgressPercentageTextView,
      @NonNull TextView detailKpiCurrentValueTextView,
      @NonNull TextView detailKpiDailyTargetValueTextView,
      @NonNull TextView detailKpiMonthlyTargetValueTextView,
      @NonNull TextView detailKpiNameTextView, @NonNull TextView detailKpiProgressLabelTextView,
      @NonNull TextView detailKpiTargetValueTextView, @NonNull LineChart kpiLineChart,
      @NonNull TextView lastEntryDateTextView, @NonNull TextView lineChartTitle,
      @NonNull Spinner monthFilterSpinner,
      @NonNull CircularProgressIndicator monthlyCircularProgressIndicator,
      @NonNull FrameLayout monthlyProgressIndicatorContainer,
      @NonNull LinearLayout monthlyProgressLayout, @NonNull TextView monthlyProgressPercentageText,
      @NonNull TextView monthlyProgressPercentageTextView,
      @NonNull LinearLayout progressIndicatorsLayout, @NonNull TextView progressSummaryTextView,
      @NonNull MaterialButton quickAdd10Button, @NonNull MaterialButton quickAdd50Button,
      @NonNull MaterialCardView quickStatsCard, @NonNull NestedScrollView scrollView,
      @NonNull MaterialCardView targetsCard, @NonNull TextView targetsTitleTextView,
      @NonNull TextView thisWeekProgressTextView) {
    this.rootView = rootView;
    this.addProgressButton = addProgressButton;
    this.addProgressButtonLayout = addProgressButtonLayout;
    this.annualCircularProgressIndicator = annualCircularProgressIndicator;
    this.annualProgressIndicatorContainer = annualProgressIndicatorContainer;
    this.annualProgressLayout = annualProgressLayout;
    this.annualProgressPercentageText = annualProgressPercentageText;
    this.annualProgressPercentageTextView = annualProgressPercentageTextView;
    this.averagePerDayTextView = averagePerDayTextView;
    this.dailyCircularProgressIndicator = dailyCircularProgressIndicator;
    this.dailyProgressIndicatorContainer = dailyProgressIndicatorContainer;
    this.dailyProgressLayout = dailyProgressLayout;
    this.dailyProgressPercentageText = dailyProgressPercentageText;
    this.dailyProgressPercentageTextView = dailyProgressPercentageTextView;
    this.detailKpiCurrentValueTextView = detailKpiCurrentValueTextView;
    this.detailKpiDailyTargetValueTextView = detailKpiDailyTargetValueTextView;
    this.detailKpiMonthlyTargetValueTextView = detailKpiMonthlyTargetValueTextView;
    this.detailKpiNameTextView = detailKpiNameTextView;
    this.detailKpiProgressLabelTextView = detailKpiProgressLabelTextView;
    this.detailKpiTargetValueTextView = detailKpiTargetValueTextView;
    this.kpiLineChart = kpiLineChart;
    this.lastEntryDateTextView = lastEntryDateTextView;
    this.lineChartTitle = lineChartTitle;
    this.monthFilterSpinner = monthFilterSpinner;
    this.monthlyCircularProgressIndicator = monthlyCircularProgressIndicator;
    this.monthlyProgressIndicatorContainer = monthlyProgressIndicatorContainer;
    this.monthlyProgressLayout = monthlyProgressLayout;
    this.monthlyProgressPercentageText = monthlyProgressPercentageText;
    this.monthlyProgressPercentageTextView = monthlyProgressPercentageTextView;
    this.progressIndicatorsLayout = progressIndicatorsLayout;
    this.progressSummaryTextView = progressSummaryTextView;
    this.quickAdd10Button = quickAdd10Button;
    this.quickAdd50Button = quickAdd50Button;
    this.quickStatsCard = quickStatsCard;
    this.scrollView = scrollView;
    this.targetsCard = targetsCard;
    this.targetsTitleTextView = targetsTitleTextView;
    this.thisWeekProgressTextView = thisWeekProgressTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityKpiDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityKpiDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_kpi_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityKpiDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addProgressButton;
      Button addProgressButton = ViewBindings.findChildViewById(rootView, id);
      if (addProgressButton == null) {
        break missingId;
      }

      id = R.id.addProgressButtonLayout;
      LinearLayout addProgressButtonLayout = ViewBindings.findChildViewById(rootView, id);
      if (addProgressButtonLayout == null) {
        break missingId;
      }

      id = R.id.annualCircularProgressIndicator;
      CircularProgressIndicator annualCircularProgressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (annualCircularProgressIndicator == null) {
        break missingId;
      }

      id = R.id.annualProgressIndicatorContainer;
      FrameLayout annualProgressIndicatorContainer = ViewBindings.findChildViewById(rootView, id);
      if (annualProgressIndicatorContainer == null) {
        break missingId;
      }

      id = R.id.annualProgressLayout;
      LinearLayout annualProgressLayout = ViewBindings.findChildViewById(rootView, id);
      if (annualProgressLayout == null) {
        break missingId;
      }

      id = R.id.annualProgressPercentageText;
      TextView annualProgressPercentageText = ViewBindings.findChildViewById(rootView, id);
      if (annualProgressPercentageText == null) {
        break missingId;
      }

      id = R.id.annualProgressPercentageTextView;
      TextView annualProgressPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (annualProgressPercentageTextView == null) {
        break missingId;
      }

      id = R.id.averagePerDayTextView;
      TextView averagePerDayTextView = ViewBindings.findChildViewById(rootView, id);
      if (averagePerDayTextView == null) {
        break missingId;
      }

      id = R.id.dailyCircularProgressIndicator;
      CircularProgressIndicator dailyCircularProgressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (dailyCircularProgressIndicator == null) {
        break missingId;
      }

      id = R.id.dailyProgressIndicatorContainer;
      FrameLayout dailyProgressIndicatorContainer = ViewBindings.findChildViewById(rootView, id);
      if (dailyProgressIndicatorContainer == null) {
        break missingId;
      }

      id = R.id.dailyProgressLayout;
      LinearLayout dailyProgressLayout = ViewBindings.findChildViewById(rootView, id);
      if (dailyProgressLayout == null) {
        break missingId;
      }

      id = R.id.dailyProgressPercentageText;
      TextView dailyProgressPercentageText = ViewBindings.findChildViewById(rootView, id);
      if (dailyProgressPercentageText == null) {
        break missingId;
      }

      id = R.id.dailyProgressPercentageTextView;
      TextView dailyProgressPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (dailyProgressPercentageTextView == null) {
        break missingId;
      }

      id = R.id.detailKpiCurrentValueTextView;
      TextView detailKpiCurrentValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (detailKpiCurrentValueTextView == null) {
        break missingId;
      }

      id = R.id.detailKpiDailyTargetValueTextView;
      TextView detailKpiDailyTargetValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (detailKpiDailyTargetValueTextView == null) {
        break missingId;
      }

      id = R.id.detailKpiMonthlyTargetValueTextView;
      TextView detailKpiMonthlyTargetValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (detailKpiMonthlyTargetValueTextView == null) {
        break missingId;
      }

      id = R.id.detailKpiNameTextView;
      TextView detailKpiNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (detailKpiNameTextView == null) {
        break missingId;
      }

      id = R.id.detailKpiProgressLabelTextView;
      TextView detailKpiProgressLabelTextView = ViewBindings.findChildViewById(rootView, id);
      if (detailKpiProgressLabelTextView == null) {
        break missingId;
      }

      id = R.id.detailKpiTargetValueTextView;
      TextView detailKpiTargetValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (detailKpiTargetValueTextView == null) {
        break missingId;
      }

      id = R.id.kpiLineChart;
      LineChart kpiLineChart = ViewBindings.findChildViewById(rootView, id);
      if (kpiLineChart == null) {
        break missingId;
      }

      id = R.id.lastEntryDateTextView;
      TextView lastEntryDateTextView = ViewBindings.findChildViewById(rootView, id);
      if (lastEntryDateTextView == null) {
        break missingId;
      }

      id = R.id.lineChartTitle;
      TextView lineChartTitle = ViewBindings.findChildViewById(rootView, id);
      if (lineChartTitle == null) {
        break missingId;
      }

      id = R.id.monthFilterSpinner;
      Spinner monthFilterSpinner = ViewBindings.findChildViewById(rootView, id);
      if (monthFilterSpinner == null) {
        break missingId;
      }

      id = R.id.monthlyCircularProgressIndicator;
      CircularProgressIndicator monthlyCircularProgressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (monthlyCircularProgressIndicator == null) {
        break missingId;
      }

      id = R.id.monthlyProgressIndicatorContainer;
      FrameLayout monthlyProgressIndicatorContainer = ViewBindings.findChildViewById(rootView, id);
      if (monthlyProgressIndicatorContainer == null) {
        break missingId;
      }

      id = R.id.monthlyProgressLayout;
      LinearLayout monthlyProgressLayout = ViewBindings.findChildViewById(rootView, id);
      if (monthlyProgressLayout == null) {
        break missingId;
      }

      id = R.id.monthlyProgressPercentageText;
      TextView monthlyProgressPercentageText = ViewBindings.findChildViewById(rootView, id);
      if (monthlyProgressPercentageText == null) {
        break missingId;
      }

      id = R.id.monthlyProgressPercentageTextView;
      TextView monthlyProgressPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (monthlyProgressPercentageTextView == null) {
        break missingId;
      }

      id = R.id.progressIndicatorsLayout;
      LinearLayout progressIndicatorsLayout = ViewBindings.findChildViewById(rootView, id);
      if (progressIndicatorsLayout == null) {
        break missingId;
      }

      id = R.id.progressSummaryTextView;
      TextView progressSummaryTextView = ViewBindings.findChildViewById(rootView, id);
      if (progressSummaryTextView == null) {
        break missingId;
      }

      id = R.id.quickAdd10Button;
      MaterialButton quickAdd10Button = ViewBindings.findChildViewById(rootView, id);
      if (quickAdd10Button == null) {
        break missingId;
      }

      id = R.id.quickAdd50Button;
      MaterialButton quickAdd50Button = ViewBindings.findChildViewById(rootView, id);
      if (quickAdd50Button == null) {
        break missingId;
      }

      id = R.id.quickStatsCard;
      MaterialCardView quickStatsCard = ViewBindings.findChildViewById(rootView, id);
      if (quickStatsCard == null) {
        break missingId;
      }

      id = R.id.scrollView;
      NestedScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.targetsCard;
      MaterialCardView targetsCard = ViewBindings.findChildViewById(rootView, id);
      if (targetsCard == null) {
        break missingId;
      }

      id = R.id.targetsTitleTextView;
      TextView targetsTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (targetsTitleTextView == null) {
        break missingId;
      }

      id = R.id.thisWeekProgressTextView;
      TextView thisWeekProgressTextView = ViewBindings.findChildViewById(rootView, id);
      if (thisWeekProgressTextView == null) {
        break missingId;
      }

      return new ActivityKpiDetailBinding((ConstraintLayout) rootView, addProgressButton,
          addProgressButtonLayout, annualCircularProgressIndicator,
          annualProgressIndicatorContainer, annualProgressLayout, annualProgressPercentageText,
          annualProgressPercentageTextView, averagePerDayTextView, dailyCircularProgressIndicator,
          dailyProgressIndicatorContainer, dailyProgressLayout, dailyProgressPercentageText,
          dailyProgressPercentageTextView, detailKpiCurrentValueTextView,
          detailKpiDailyTargetValueTextView, detailKpiMonthlyTargetValueTextView,
          detailKpiNameTextView, detailKpiProgressLabelTextView, detailKpiTargetValueTextView,
          kpiLineChart, lastEntryDateTextView, lineChartTitle, monthFilterSpinner,
          monthlyCircularProgressIndicator, monthlyProgressIndicatorContainer,
          monthlyProgressLayout, monthlyProgressPercentageText, monthlyProgressPercentageTextView,
          progressIndicatorsLayout, progressSummaryTextView, quickAdd10Button, quickAdd50Button,
          quickStatsCard, scrollView, targetsCard, targetsTitleTextView, thisWeekProgressTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
