// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAdminDashboardUserBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView userBadge;

  @NonNull
  public final MaterialCardView userCard;

  @NonNull
  public final TextView userName;

  @NonNull
  public final TextView userPerformance;

  private ItemAdminDashboardUserBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView userBadge, @NonNull MaterialCardView userCard, @NonNull TextView userName,
      @NonNull TextView userPerformance) {
    this.rootView = rootView;
    this.userBadge = userBadge;
    this.userCard = userCard;
    this.userName = userName;
    this.userPerformance = userPerformance;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAdminDashboardUserBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAdminDashboardUserBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_admin_dashboard_user, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAdminDashboardUserBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.userBadge;
      TextView userBadge = ViewBindings.findChildViewById(rootView, id);
      if (userBadge == null) {
        break missingId;
      }

      MaterialCardView userCard = (MaterialCardView) rootView;

      id = R.id.userName;
      TextView userName = ViewBindings.findChildViewById(rootView, id);
      if (userName == null) {
        break missingId;
      }

      id = R.id.userPerformance;
      TextView userPerformance = ViewBindings.findChildViewById(rootView, id);
      if (userPerformance == null) {
        break missingId;
      }

      return new ItemAdminDashboardUserBinding((MaterialCardView) rootView, userBadge, userCard,
          userName, userPerformance);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
