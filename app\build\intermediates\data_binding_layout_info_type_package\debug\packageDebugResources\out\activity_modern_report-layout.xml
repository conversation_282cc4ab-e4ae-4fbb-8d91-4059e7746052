<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_modern_report" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_modern_report.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_modern_report_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="388" endOffset="53"/></Target><Target id="@+id/reportContentContainer" tag="binding_1" view="LinearLayout"><Expressions/><location startLine="217" startOffset="16" endLine="240" endOffset="30"/></Target><Target id="@+id/compactReportTable" tag="binding_1" include="compact_report_table"><Expressions/><location startLine="234" startOffset="20" endLine="238" endOffset="62"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="26" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="17" startOffset="8" endLine="24" endOffset="47"/></Target><Target id="@+id/scrollView" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="36" startOffset="4" endLine="386" endOffset="43"/></Target><Target id="@+id/reportConstraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="48" startOffset="8" endLine="385" endOffset="59"/></Target><Target id="@+id/kpiSelectorLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="56" startOffset="12" endLine="83" endOffset="67"/></Target><Target id="@+id/kpiSelectorAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="72" startOffset="16" endLine="81" endOffset="45"/></Target><Target id="@+id/userFilterLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="86" startOffset="12" endLine="114" endOffset="67"/></Target><Target id="@+id/userFilterAutoCompleteTextView" view="AutoCompleteTextView"><Expressions/><location startLine="103" startOffset="16" endLine="112" endOffset="45"/></Target><Target id="@+id/startDateInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="117" startOffset="12" endLine="148" endOffset="67"/></Target><Target id="@+id/startDateEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="138" startOffset="16" endLine="146" endOffset="45"/></Target><Target id="@+id/endDateInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="151" startOffset="12" endLine="181" endOffset="67"/></Target><Target id="@+id/endDateEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="171" startOffset="16" endLine="179" endOffset="45"/></Target><Target id="@+id/goButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="184" startOffset="12" endLine="201" endOffset="43"/></Target><Target id="@+id/reportContentCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="204" startOffset="12" endLine="241" endOffset="47"/></Target><Target id="@+id/kpiNameLabel" view="TextView"><Expressions/><location startLine="223" startOffset="20" endLine="232" endOffset="52"/></Target><Target id="@+id/summaryChartFilterRadioGroup" view="RadioGroup"><Expressions/><location startLine="244" startOffset="12" endLine="283" endOffset="24"/></Target><Target id="@+id/radioDaily" view="RadioButton"><Expressions/><location startLine="257" startOffset="16" endLine="264" endOffset="49"/></Target><Target id="@+id/radioMonthly" view="RadioButton"><Expressions/><location startLine="266" startOffset="16" endLine="273" endOffset="49"/></Target><Target id="@+id/radioAnnual" view="RadioButton"><Expressions/><location startLine="275" startOffset="16" endLine="282" endOffset="49"/></Target><Target id="@+id/barChartContainer" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="286" startOffset="12" endLine="332" endOffset="63"/></Target><Target id="@+id/barChartTitle" view="TextView"><Expressions/><location startLine="296" startOffset="16" endLine="306" endOffset="79"/></Target><Target id="@+id/btnShareBarChart" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="308" startOffset="16" endLine="319" endOffset="63"/></Target><Target id="@+id/summaryBarChart" view="com.github.mikephil.charting.charts.BarChart"><Expressions/><location startLine="321" startOffset="16" endLine="331" endOffset="77"/></Target><Target id="@+id/trendChartContainer" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="335" startOffset="12" endLine="383" endOffset="63"/></Target><Target id="@+id/trendChartTitle" view="TextView"><Expressions/><location startLine="350" startOffset="16" endLine="360" endOffset="81"/></Target><Target id="@+id/btnShareTrendChart" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="362" startOffset="16" endLine="373" endOffset="63"/></Target><Target id="@+id/trendChart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="375" startOffset="16" endLine="382" endOffset="79"/></Target></Targets></Layout>