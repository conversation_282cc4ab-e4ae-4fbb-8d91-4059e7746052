// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityExpiryTrackingBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final TextView actionMessageText;

  @NonNull
  public final TextView currentDateText;

  @NonNull
  public final MaterialCardView datePickerCard;

  @NonNull
  public final TextView expiredCountText;

  @NonNull
  public final TextView expiringSoonCountText;

  @NonNull
  public final MaterialCardView expiryActionCard;

  @NonNull
  public final MaterialButton viewExpiredProductsButton;

  @NonNull
  public final MaterialButton viewExpiringSoonButton;

  private ActivityExpiryTrackingBinding(@NonNull CoordinatorLayout rootView,
      @NonNull TextView actionMessageText, @NonNull TextView currentDateText,
      @NonNull MaterialCardView datePickerCard, @NonNull TextView expiredCountText,
      @NonNull TextView expiringSoonCountText, @NonNull MaterialCardView expiryActionCard,
      @NonNull MaterialButton viewExpiredProductsButton,
      @NonNull MaterialButton viewExpiringSoonButton) {
    this.rootView = rootView;
    this.actionMessageText = actionMessageText;
    this.currentDateText = currentDateText;
    this.datePickerCard = datePickerCard;
    this.expiredCountText = expiredCountText;
    this.expiringSoonCountText = expiringSoonCountText;
    this.expiryActionCard = expiryActionCard;
    this.viewExpiredProductsButton = viewExpiredProductsButton;
    this.viewExpiringSoonButton = viewExpiringSoonButton;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityExpiryTrackingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityExpiryTrackingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_expiry_tracking, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityExpiryTrackingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.actionMessageText;
      TextView actionMessageText = ViewBindings.findChildViewById(rootView, id);
      if (actionMessageText == null) {
        break missingId;
      }

      id = R.id.currentDateText;
      TextView currentDateText = ViewBindings.findChildViewById(rootView, id);
      if (currentDateText == null) {
        break missingId;
      }

      id = R.id.datePickerCard;
      MaterialCardView datePickerCard = ViewBindings.findChildViewById(rootView, id);
      if (datePickerCard == null) {
        break missingId;
      }

      id = R.id.expiredCountText;
      TextView expiredCountText = ViewBindings.findChildViewById(rootView, id);
      if (expiredCountText == null) {
        break missingId;
      }

      id = R.id.expiringSoonCountText;
      TextView expiringSoonCountText = ViewBindings.findChildViewById(rootView, id);
      if (expiringSoonCountText == null) {
        break missingId;
      }

      id = R.id.expiryActionCard;
      MaterialCardView expiryActionCard = ViewBindings.findChildViewById(rootView, id);
      if (expiryActionCard == null) {
        break missingId;
      }

      id = R.id.viewExpiredProductsButton;
      MaterialButton viewExpiredProductsButton = ViewBindings.findChildViewById(rootView, id);
      if (viewExpiredProductsButton == null) {
        break missingId;
      }

      id = R.id.viewExpiringSoonButton;
      MaterialButton viewExpiringSoonButton = ViewBindings.findChildViewById(rootView, id);
      if (viewExpiringSoonButton == null) {
        break missingId;
      }

      return new ActivityExpiryTrackingBinding((CoordinatorLayout) rootView, actionMessageText,
          currentDateText, datePickerCard, expiredCountText, expiringSoonCountText,
          expiryActionCard, viewExpiredProductsButton, viewExpiringSoonButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
