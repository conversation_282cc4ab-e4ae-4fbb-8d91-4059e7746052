// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView ivTaskActions;

  @NonNull
  public final View statusIndicatorDot;

  @NonNull
  public final TextView tvTaskExpirationDate;

  @NonNull
  public final TextView tvTaskName;

  @NonNull
  public final TextView tvTaskStatusText;

  private ItemTaskBinding(@NonNull MaterialCardView rootView, @NonNull ImageView ivTaskActions,
      @NonNull View statusIndicatorDot, @NonNull TextView tvTaskExpirationDate,
      @NonNull TextView tvTaskName, @NonNull TextView tvTaskStatusText) {
    this.rootView = rootView;
    this.ivTaskActions = ivTaskActions;
    this.statusIndicatorDot = statusIndicatorDot;
    this.tvTaskExpirationDate = tvTaskExpirationDate;
    this.tvTaskName = tvTaskName;
    this.tvTaskStatusText = tvTaskStatusText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivTaskActions;
      ImageView ivTaskActions = ViewBindings.findChildViewById(rootView, id);
      if (ivTaskActions == null) {
        break missingId;
      }

      id = R.id.statusIndicatorDot;
      View statusIndicatorDot = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicatorDot == null) {
        break missingId;
      }

      id = R.id.tvTaskExpirationDate;
      TextView tvTaskExpirationDate = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskExpirationDate == null) {
        break missingId;
      }

      id = R.id.tvTaskName;
      TextView tvTaskName = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskName == null) {
        break missingId;
      }

      id = R.id.tvTaskStatusText;
      TextView tvTaskStatusText = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskStatusText == null) {
        break missingId;
      }

      return new ItemTaskBinding((MaterialCardView) rootView, ivTaskActions, statusIndicatorDot,
          tvTaskExpirationDate, tvTaskName, tvTaskStatusText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
