<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="kpi_list_item" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\kpi_list_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/kpi_list_item_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="109" endOffset="51"/></Target><Target id="@+id/kpiNameTextView" view="TextView"><Expressions/><location startLine="14" startOffset="8" endLine="22" endOffset="40"/></Target><Target id="@+id/kpiOwnerTextView" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="34" endOffset="46"/></Target><Target id="@+id/kpiTargetLabelTextView" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="44" endOffset="72"/></Target><Target id="@+id/kpiTargetValueTextView" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="37"/></Target><Target id="@+id/kpiCurrentLabelTextView" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="65" endOffset="78"/></Target><Target id="@+id/kpiCurrentValueTextView" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="76" endOffset="35"/></Target><Target id="@+id/progress_circular_layout" view="FrameLayout"><Expressions/><location startLine="79" startOffset="8" endLine="105" endOffset="21"/></Target><Target id="@+id/kpiCircularProgressIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="87" startOffset="12" endLine="95" endOffset="57"/></Target><Target id="@+id/kpiProgressPercentageTextView" view="TextView"><Expressions/><location startLine="97" startOffset="12" endLine="104" endOffset="34"/></Target></Targets></Layout>