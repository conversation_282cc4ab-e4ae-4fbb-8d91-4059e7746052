// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSelectCardColorsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View bottomColorPreview;

  @NonNull
  public final Button buttonCancelColorSelection;

  @NonNull
  public final Button buttonPickBottomColor;

  @NonNull
  public final Button buttonPickTopColor;

  @NonNull
  public final Button buttonResetColors;

  @NonNull
  public final Button buttonSaveColors;

  @NonNull
  public final View topColorPreview;

  private DialogSelectCardColorsBinding(@NonNull LinearLayout rootView,
      @NonNull View bottomColorPreview, @NonNull Button buttonCancelColorSelection,
      @NonNull Button buttonPickBottomColor, @NonNull Button buttonPickTopColor,
      @NonNull Button buttonResetColors, @NonNull Button buttonSaveColors,
      @NonNull View topColorPreview) {
    this.rootView = rootView;
    this.bottomColorPreview = bottomColorPreview;
    this.buttonCancelColorSelection = buttonCancelColorSelection;
    this.buttonPickBottomColor = buttonPickBottomColor;
    this.buttonPickTopColor = buttonPickTopColor;
    this.buttonResetColors = buttonResetColors;
    this.buttonSaveColors = buttonSaveColors;
    this.topColorPreview = topColorPreview;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectCardColorsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectCardColorsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_select_card_colors, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectCardColorsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomColorPreview;
      View bottomColorPreview = ViewBindings.findChildViewById(rootView, id);
      if (bottomColorPreview == null) {
        break missingId;
      }

      id = R.id.buttonCancelColorSelection;
      Button buttonCancelColorSelection = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancelColorSelection == null) {
        break missingId;
      }

      id = R.id.buttonPickBottomColor;
      Button buttonPickBottomColor = ViewBindings.findChildViewById(rootView, id);
      if (buttonPickBottomColor == null) {
        break missingId;
      }

      id = R.id.buttonPickTopColor;
      Button buttonPickTopColor = ViewBindings.findChildViewById(rootView, id);
      if (buttonPickTopColor == null) {
        break missingId;
      }

      id = R.id.buttonResetColors;
      Button buttonResetColors = ViewBindings.findChildViewById(rootView, id);
      if (buttonResetColors == null) {
        break missingId;
      }

      id = R.id.buttonSaveColors;
      Button buttonSaveColors = ViewBindings.findChildViewById(rootView, id);
      if (buttonSaveColors == null) {
        break missingId;
      }

      id = R.id.topColorPreview;
      View topColorPreview = ViewBindings.findChildViewById(rootView, id);
      if (topColorPreview == null) {
        break missingId;
      }

      return new DialogSelectCardColorsBinding((LinearLayout) rootView, bottomColorPreview,
          buttonCancelColorSelection, buttonPickBottomColor, buttonPickTopColor, buttonResetColors,
          buttonSaveColors, topColorPreview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
