<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Contact Information Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📞 Contact Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="Email Address"
                        app:startIconDrawable="@drawable/ic_email"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextEmail"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="Phone Number (with country code)"
                        app:startIconDrawable="@drawable/ic_phone"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Reminder Types Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔔 Reminder Types"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_email"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Email Reminders"
                            android:textSize="16sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchEmailReminders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_whatsapp"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="WhatsApp Reminders"
                            android:textSize="16sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchWhatsappReminders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_notifications"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Local Notifications"
                            android:textSize="16sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchLocalNotifications"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Default Reminder Timing Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="⏰ Default Reminder Timing"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="16dp" />

                    <RadioGroup
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <RadioButton
                            android:id="@+id/radioNoReminder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="No reminder"
                            android:layout_marginBottom="8dp" />

                        <RadioButton
                            android:id="@+id/radio1Day"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="1 day before"
                            android:checked="true"
                            android:layout_marginBottom="8dp" />

                        <RadioButton
                            android:id="@+id/radio3Days"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="3 days before"
                            android:layout_marginBottom="8dp" />

                        <RadioButton
                            android:id="@+id/radio1Week"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="1 week before" />

                    </RadioGroup>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Test Buttons Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🧪 Test Reminders"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:layout_marginBottom="12dp">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/buttonTestWhatsapp"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="Test WhatsApp"
                            android:drawableStart="@drawable/ic_whatsapp"
                            android:drawablePadding="8dp"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/buttonTestEmail"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="Test Email"
                            android:drawableStart="@drawable/ic_email"
                            android:drawablePadding="8dp"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                    </LinearLayout>

                    <!-- App Status Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonCheckApps"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📱 Check Available Apps"
                        android:textSize="14sp"
                        style="@style/Widget.MaterialComponents.Button.TextButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Save Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSave"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="💾 Save Settings"
                android:textSize="16sp"
                android:padding="16dp"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
