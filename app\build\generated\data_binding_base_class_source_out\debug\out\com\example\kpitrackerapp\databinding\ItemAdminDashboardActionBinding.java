// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAdminDashboardActionBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCardView actionCard;

  @NonNull
  public final TextView actionDescription;

  @NonNull
  public final TextView actionIcon;

  @NonNull
  public final TextView actionTitle;

  private ItemAdminDashboardActionBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialCardView actionCard, @NonNull TextView actionDescription,
      @NonNull TextView actionIcon, @NonNull TextView actionTitle) {
    this.rootView = rootView;
    this.actionCard = actionCard;
    this.actionDescription = actionDescription;
    this.actionIcon = actionIcon;
    this.actionTitle = actionTitle;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAdminDashboardActionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAdminDashboardActionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_admin_dashboard_action, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAdminDashboardActionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialCardView actionCard = (MaterialCardView) rootView;

      id = R.id.actionDescription;
      TextView actionDescription = ViewBindings.findChildViewById(rootView, id);
      if (actionDescription == null) {
        break missingId;
      }

      id = R.id.actionIcon;
      TextView actionIcon = ViewBindings.findChildViewById(rootView, id);
      if (actionIcon == null) {
        break missingId;
      }

      id = R.id.actionTitle;
      TextView actionTitle = ViewBindings.findChildViewById(rootView, id);
      if (actionTitle == null) {
        break missingId;
      }

      return new ItemAdminDashboardActionBinding((MaterialCardView) rootView, actionCard,
          actionDescription, actionIcon, actionTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
