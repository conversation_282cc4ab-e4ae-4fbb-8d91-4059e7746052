// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentMainDashboardBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView emptyStateTextView;

  @NonNull
  public final FloatingActionButton fabAddKpi;

  @NonNull
  public final RecyclerView kpiRecyclerView;

  private FragmentMainDashboardBinding(@NonNull LinearLayout rootView,
      @NonNull TextView emptyStateTextView, @NonNull FloatingActionButton fabAddKpi,
      @NonNull RecyclerView kpiRecyclerView) {
    this.rootView = rootView;
    this.emptyStateTextView = emptyStateTextView;
    this.fabAddKpi = fabAddKpi;
    this.kpiRecyclerView = kpiRecyclerView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentMainDashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentMainDashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_main_dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentMainDashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emptyStateTextView;
      TextView emptyStateTextView = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateTextView == null) {
        break missingId;
      }

      id = R.id.fabAddKpi;
      FloatingActionButton fabAddKpi = ViewBindings.findChildViewById(rootView, id);
      if (fabAddKpi == null) {
        break missingId;
      }

      id = R.id.kpiRecyclerView;
      RecyclerView kpiRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (kpiRecyclerView == null) {
        break missingId;
      }

      return new FragmentMainDashboardBinding((LinearLayout) rootView, emptyStateTextView,
          fabAddKpi, kpiRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
