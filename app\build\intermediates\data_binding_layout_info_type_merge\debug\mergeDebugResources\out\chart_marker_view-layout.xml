<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="chart_marker_view" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\chart_marker_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/chart_marker_view_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="57" endOffset="35"/></Target><Target id="@+id/tvUserName" view="TextView"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="38"/></Target><Target id="@+id/tvValue" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="30" endOffset="37"/></Target><Target id="@+id/tvTarget" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="38" endOffset="37"/></Target><Target id="@+id/tvPercentage" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="46" endOffset="37"/></Target><Target id="@+id/tvComparison" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="53" endOffset="37"/></Target></Targets></Layout>