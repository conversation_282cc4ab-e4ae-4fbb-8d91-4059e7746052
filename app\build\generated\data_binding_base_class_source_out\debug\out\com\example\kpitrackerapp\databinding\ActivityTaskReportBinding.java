// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTaskReportBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayoutTaskReport;

  @NonNull
  public final Button btnSaveTaskReport;

  @NonNull
  public final RecyclerView rvAllTasksReport;

  @NonNull
  public final RecyclerView rvTasksDueSoon;

  @NonNull
  public final Toolbar toolbarTaskReport;

  @NonNull
  public final TextView tvAllTasksTitle;

  @NonNull
  public final TextView tvTasksDueSoonTitle;

  private ActivityTaskReportBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayoutTaskReport, @NonNull Button btnSaveTaskReport,
      @NonNull RecyclerView rvAllTasksReport, @NonNull RecyclerView rvTasksDueSoon,
      @NonNull Toolbar toolbarTaskReport, @NonNull TextView tvAllTasksTitle,
      @NonNull TextView tvTasksDueSoonTitle) {
    this.rootView = rootView;
    this.appBarLayoutTaskReport = appBarLayoutTaskReport;
    this.btnSaveTaskReport = btnSaveTaskReport;
    this.rvAllTasksReport = rvAllTasksReport;
    this.rvTasksDueSoon = rvTasksDueSoon;
    this.toolbarTaskReport = toolbarTaskReport;
    this.tvAllTasksTitle = tvAllTasksTitle;
    this.tvTasksDueSoonTitle = tvTasksDueSoonTitle;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTaskReportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTaskReportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_task_report, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTaskReportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayoutTaskReport;
      AppBarLayout appBarLayoutTaskReport = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayoutTaskReport == null) {
        break missingId;
      }

      id = R.id.btnSaveTaskReport;
      Button btnSaveTaskReport = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveTaskReport == null) {
        break missingId;
      }

      id = R.id.rvAllTasksReport;
      RecyclerView rvAllTasksReport = ViewBindings.findChildViewById(rootView, id);
      if (rvAllTasksReport == null) {
        break missingId;
      }

      id = R.id.rvTasksDueSoon;
      RecyclerView rvTasksDueSoon = ViewBindings.findChildViewById(rootView, id);
      if (rvTasksDueSoon == null) {
        break missingId;
      }

      id = R.id.toolbarTaskReport;
      Toolbar toolbarTaskReport = ViewBindings.findChildViewById(rootView, id);
      if (toolbarTaskReport == null) {
        break missingId;
      }

      id = R.id.tvAllTasksTitle;
      TextView tvAllTasksTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvAllTasksTitle == null) {
        break missingId;
      }

      id = R.id.tvTasksDueSoonTitle;
      TextView tvTasksDueSoonTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTasksDueSoonTitle == null) {
        break missingId;
      }

      return new ActivityTaskReportBinding((CoordinatorLayout) rootView, appBarLayoutTaskReport,
          btnSaveTaskReport, rvAllTasksReport, rvTasksDueSoon, toolbarTaskReport, tvAllTasksTitle,
          tvTasksDueSoonTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
