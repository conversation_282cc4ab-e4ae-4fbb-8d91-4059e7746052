<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_user_list" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_user_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/userCard"><Targets><Target id="@+id/userCard" tag="layout/item_user_list_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="115" endOffset="51"/></Target><Target id="@+id/userImage" view="ImageView"><Expressions/><location startLine="27" startOffset="12" endLine="33" endOffset="63"/></Target><Target id="@+id/onlineIndicator" view="View"><Expressions/><location startLine="36" startOffset="12" endLine="42" endOffset="46"/></Target><Target id="@+id/userName" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="67" endOffset="46"/></Target><Target id="@+id/roleIcon" view="TextView"><Expressions/><location startLine="69" startOffset="16" endLine="74" endOffset="45"/></Target><Target id="@+id/userEmail" view="TextView"><Expressions/><location startLine="79" startOffset="12" endLine="88" endOffset="41"/></Target><Target id="@+id/userRole" view="TextView"><Expressions/><location startLine="91" startOffset="12" endLine="99" endOffset="42"/></Target><Target id="@+id/chatButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="104" startOffset="8" endLine="111" endOffset="46"/></Target></Targets></Layout>