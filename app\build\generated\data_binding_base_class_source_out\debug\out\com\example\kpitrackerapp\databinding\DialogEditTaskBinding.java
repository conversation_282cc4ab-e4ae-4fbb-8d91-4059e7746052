// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditTaskBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox cbTaskCompleted;

  @NonNull
  public final TextInputEditText etEditTaskExpirationDate;

  @NonNull
  public final TextInputEditText etEditTaskExpirationTime;

  @NonNull
  public final TextInputEditText etEditTaskName;

  @NonNull
  public final TextInputEditText etEditTaskReminderDays;

  @NonNull
  public final TextInputLayout tilEditTaskExpirationDate;

  @NonNull
  public final TextInputLayout tilEditTaskExpirationTime;

  @NonNull
  public final TextInputLayout tilEditTaskName;

  @NonNull
  public final TextInputLayout tilEditTaskReminderDays;

  private DialogEditTaskBinding(@NonNull LinearLayout rootView, @NonNull CheckBox cbTaskCompleted,
      @NonNull TextInputEditText etEditTaskExpirationDate,
      @NonNull TextInputEditText etEditTaskExpirationTime,
      @NonNull TextInputEditText etEditTaskName, @NonNull TextInputEditText etEditTaskReminderDays,
      @NonNull TextInputLayout tilEditTaskExpirationDate,
      @NonNull TextInputLayout tilEditTaskExpirationTime, @NonNull TextInputLayout tilEditTaskName,
      @NonNull TextInputLayout tilEditTaskReminderDays) {
    this.rootView = rootView;
    this.cbTaskCompleted = cbTaskCompleted;
    this.etEditTaskExpirationDate = etEditTaskExpirationDate;
    this.etEditTaskExpirationTime = etEditTaskExpirationTime;
    this.etEditTaskName = etEditTaskName;
    this.etEditTaskReminderDays = etEditTaskReminderDays;
    this.tilEditTaskExpirationDate = tilEditTaskExpirationDate;
    this.tilEditTaskExpirationTime = tilEditTaskExpirationTime;
    this.tilEditTaskName = tilEditTaskName;
    this.tilEditTaskReminderDays = tilEditTaskReminderDays;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cbTaskCompleted;
      CheckBox cbTaskCompleted = ViewBindings.findChildViewById(rootView, id);
      if (cbTaskCompleted == null) {
        break missingId;
      }

      id = R.id.etEditTaskExpirationDate;
      TextInputEditText etEditTaskExpirationDate = ViewBindings.findChildViewById(rootView, id);
      if (etEditTaskExpirationDate == null) {
        break missingId;
      }

      id = R.id.etEditTaskExpirationTime;
      TextInputEditText etEditTaskExpirationTime = ViewBindings.findChildViewById(rootView, id);
      if (etEditTaskExpirationTime == null) {
        break missingId;
      }

      id = R.id.etEditTaskName;
      TextInputEditText etEditTaskName = ViewBindings.findChildViewById(rootView, id);
      if (etEditTaskName == null) {
        break missingId;
      }

      id = R.id.etEditTaskReminderDays;
      TextInputEditText etEditTaskReminderDays = ViewBindings.findChildViewById(rootView, id);
      if (etEditTaskReminderDays == null) {
        break missingId;
      }

      id = R.id.tilEditTaskExpirationDate;
      TextInputLayout tilEditTaskExpirationDate = ViewBindings.findChildViewById(rootView, id);
      if (tilEditTaskExpirationDate == null) {
        break missingId;
      }

      id = R.id.tilEditTaskExpirationTime;
      TextInputLayout tilEditTaskExpirationTime = ViewBindings.findChildViewById(rootView, id);
      if (tilEditTaskExpirationTime == null) {
        break missingId;
      }

      id = R.id.tilEditTaskName;
      TextInputLayout tilEditTaskName = ViewBindings.findChildViewById(rootView, id);
      if (tilEditTaskName == null) {
        break missingId;
      }

      id = R.id.tilEditTaskReminderDays;
      TextInputLayout tilEditTaskReminderDays = ViewBindings.findChildViewById(rootView, id);
      if (tilEditTaskReminderDays == null) {
        break missingId;
      }

      return new DialogEditTaskBinding((LinearLayout) rootView, cbTaskCompleted,
          etEditTaskExpirationDate, etEditTaskExpirationTime, etEditTaskName,
          etEditTaskReminderDays, tilEditTaskExpirationDate, tilEditTaskExpirationTime,
          tilEditTaskName, tilEditTaskReminderDays);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
