// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityExcelImportBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnSelectExcelFile;

  @NonNull
  public final ProgressBar progressBarExcelImport;

  @NonNull
  public final TextView tvExcelImportStatus;

  private ActivityExcelImportBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnSelectExcelFile, @NonNull ProgressBar progressBarExcelImport,
      @NonNull TextView tvExcelImportStatus) {
    this.rootView = rootView;
    this.btnSelectExcelFile = btnSelectExcelFile;
    this.progressBarExcelImport = progressBarExcelImport;
    this.tvExcelImportStatus = tvExcelImportStatus;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityExcelImportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityExcelImportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_excel_import, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityExcelImportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnSelectExcelFile;
      Button btnSelectExcelFile = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectExcelFile == null) {
        break missingId;
      }

      id = R.id.progressBarExcelImport;
      ProgressBar progressBarExcelImport = ViewBindings.findChildViewById(rootView, id);
      if (progressBarExcelImport == null) {
        break missingId;
      }

      id = R.id.tvExcelImportStatus;
      TextView tvExcelImportStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvExcelImportStatus == null) {
        break missingId;
      }

      return new ActivityExcelImportBinding((ConstraintLayout) rootView, btnSelectExcelFile,
          progressBarExcelImport, tvExcelImportStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
