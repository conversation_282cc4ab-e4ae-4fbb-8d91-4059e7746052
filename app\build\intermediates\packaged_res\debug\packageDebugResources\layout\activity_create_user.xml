<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp"
        tools:context=".ui.CreateUserActivity">

        <!-- Header -->
        <TextView
            android:id="@+id/headerTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="Create New User"
            android:textColor="@color/purple_700"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Profile Picture Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/profilePictureCard"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="32dp"
            app:cardCornerRadius="60dp"
            app:cardElevation="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/headerTextView">

            <ImageView
                android:id="@+id/profilePictureImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/gray_200"
                android:contentDescription="Profile Picture"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_baseline_person_24" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Add Photo Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/addPhotoButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Add Photo"
            android:textSize="14sp"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            app:icon="@drawable/ic_baseline_camera_alt_24"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/profilePictureCard" />

        <!-- Form Fields -->
        <LinearLayout
            android:id="@+id/formLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/addPhotoButton">

            <!-- Full Name -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/fullNameInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Full Name *"
                app:startIconDrawable="@drawable/ic_baseline_person_24"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/fullNameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Username -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/usernameInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Username"
                app:helperText="Optional - for easy login"
                app:startIconDrawable="@drawable/ic_baseline_alternate_email_24"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/usernameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Email -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/emailInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Email"
                app:helperText="Optional - for notifications"
                app:startIconDrawable="@drawable/ic_baseline_email_24"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/emailEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Department -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/departmentInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="Department"
                app:helperText="Optional - e.g., Cardiology, Pediatrics"
                app:startIconDrawable="@drawable/ic_baseline_business_24"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/departmentEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/buttonLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal"
            android:weightSum="2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/formLayout">

            <!-- Cancel Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/cancelButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Cancel"
                android:textSize="16sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                app:cornerRadius="12dp" />

            <!-- Create Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/createButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="Create User"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="12dp" />

        </LinearLayout>

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/loadingProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
