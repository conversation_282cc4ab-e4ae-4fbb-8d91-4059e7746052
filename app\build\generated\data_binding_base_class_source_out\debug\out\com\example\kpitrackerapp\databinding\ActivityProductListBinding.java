// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProductListBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton addProductFab;

  @NonNull
  public final TextView emptyStateText;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final EditText searchEditText;

  @NonNull
  public final Toolbar toolbar;

  private ActivityProductListBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton addProductFab, @NonNull TextView emptyStateText,
      @NonNull RecyclerView recyclerView, @NonNull EditText searchEditText,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.addProductFab = addProductFab;
    this.emptyStateText = emptyStateText;
    this.recyclerView = recyclerView;
    this.searchEditText = searchEditText;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProductListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProductListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_product_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProductListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addProductFab;
      FloatingActionButton addProductFab = ViewBindings.findChildViewById(rootView, id);
      if (addProductFab == null) {
        break missingId;
      }

      id = R.id.emptyStateText;
      TextView emptyStateText = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateText == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.searchEditText;
      EditText searchEditText = ViewBindings.findChildViewById(rootView, id);
      if (searchEditText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityProductListBinding((CoordinatorLayout) rootView, addProductFab,
          emptyStateText, recyclerView, searchEditText, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
