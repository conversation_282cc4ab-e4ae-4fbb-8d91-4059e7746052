<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:contentPadding="12dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvUserName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:textColor="#212121"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            android:textColor="#212121"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvTarget"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            android:textColor="#212121"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvPercentage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            android:textColor="#212121"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvComparison"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#212121"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
