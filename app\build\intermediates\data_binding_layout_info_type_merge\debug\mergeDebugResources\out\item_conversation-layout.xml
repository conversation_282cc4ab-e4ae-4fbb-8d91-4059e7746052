<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_conversation" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_conversation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/conversationCard"><Targets><Target id="@+id/conversationCard" tag="layout/item_conversation_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="128" endOffset="51"/></Target><Target id="@+id/userImage" view="ImageView"><Expressions/><location startLine="27" startOffset="12" endLine="33" endOffset="63"/></Target><Target id="@+id/onlineIndicator" view="View"><Expressions/><location startLine="36" startOffset="12" endLine="42" endOffset="43"/></Target><Target id="@+id/userName" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="67" endOffset="46"/></Target><Target id="@+id/timeText" view="TextView"><Expressions/><location startLine="69" startOffset="16" endLine="75" endOffset="45"/></Target><Target id="@+id/lastMessage" view="TextView"><Expressions/><location startLine="86" startOffset="16" endLine="95" endOffset="45"/></Target><Target id="@+id/muteIcon" view="ImageView"><Expressions/><location startLine="98" startOffset="16" endLine="104" endOffset="47"/></Target><Target id="@+id/unreadBadge" view="TextView"><Expressions/><location startLine="107" startOffset="16" endLine="120" endOffset="47"/></Target></Targets></Layout>