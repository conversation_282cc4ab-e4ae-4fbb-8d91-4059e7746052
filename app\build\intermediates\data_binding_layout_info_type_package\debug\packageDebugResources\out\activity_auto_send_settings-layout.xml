<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_auto_send_settings" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_auto_send_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_auto_send_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="472" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="62"/></Target><Target id="@+id/editTextEmail" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="24" endLine="61" endOffset="66"/></Target><Target id="@+id/editTextPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="71" startOffset="24" endLine="75" endOffset="55"/></Target><Target id="@+id/textWhatsappStatus" view="TextView"><Expressions/><location startLine="119" startOffset="24" endLine="125" endOffset="54"/></Target><Target id="@+id/textEmailStatus" view="TextView"><Expressions/><location startLine="142" startOffset="24" endLine="148" endOffset="54"/></Target><Target id="@+id/buttonCheckApps" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="152" startOffset="20" endLine="157" endOffset="84"/></Target><Target id="@+id/switchWeeklyReports" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="199" startOffset="24" endLine="202" endOffset="66"/></Target><Target id="@+id/switchMonthlyReports" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="219" startOffset="24" endLine="222" endOffset="66"/></Target><Target id="@+id/buttonTestWeeklyReport" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="231" startOffset="24" endLine="238" endOffset="92"/></Target><Target id="@+id/buttonTestMonthlyReport" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="240" startOffset="24" endLine="247" endOffset="92"/></Target><Target id="@+id/switchWhatsappEnabled" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="291" startOffset="24" endLine="294" endOffset="66"/></Target><Target id="@+id/switchEmailEnabled" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="310" startOffset="24" endLine="313" endOffset="66"/></Target><Target id="@+id/switchAutoReminders" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="357" startOffset="24" endLine="360" endOffset="66"/></Target><Target id="@+id/switchReminderWhatsapp" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="377" startOffset="24" endLine="380" endOffset="66"/></Target><Target id="@+id/switchReminderEmail" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="396" startOffset="24" endLine="399" endOffset="66"/></Target><Target id="@+id/buttonSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="430" startOffset="20" endLine="436" endOffset="49"/></Target><Target id="@+id/buttonScheduleNow" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="444" startOffset="24" endLine="451" endOffset="92"/></Target><Target id="@+id/buttonCancelSchedules" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="453" startOffset="24" endLine="460" endOffset="92"/></Target></Targets></Layout>