<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.github.dhaval2404:colorpicker:2.3" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57a95fe910a95177c0d9db83b43be3ff\transformed\jetified-colorpicker-2.3\assets"><file name="material-colors.json" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57a95fe910a95177c0d9db83b43be3ff\transformed\jetified-colorpicker-2.3\assets\material-colors.json"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\assets"><file name="data/smart_list.json" path="D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\assets\data\smart_list.json"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>