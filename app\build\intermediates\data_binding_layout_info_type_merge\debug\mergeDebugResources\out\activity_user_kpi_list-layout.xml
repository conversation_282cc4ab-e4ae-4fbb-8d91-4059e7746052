<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_kpi_list" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_user_kpi_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_user_kpi_list_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="63" endOffset="51"/></Target><Target id="@+id/appBarLayoutUserKpi" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="53"/></Target><Target id="@+id/toolbarUserKpi" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="17" startOffset="8" endLine="22" endOffset="66"/></Target><Target id="@+id/userKpiRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="26" startOffset="4" endLine="38" endOffset="48"/></Target><Target id="@+id/emptyStateTextViewUserKpi" view="TextView"><Expressions/><location startLine="40" startOffset="4" endLine="51" endOffset="35"/></Target><Target id="@+id/fabAddKpiUserList" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="53" startOffset="4" endLine="61" endOffset="51"/></Target></Targets></Layout>