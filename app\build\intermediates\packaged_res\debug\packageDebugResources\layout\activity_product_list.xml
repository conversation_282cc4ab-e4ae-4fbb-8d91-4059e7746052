<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.ProductListActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Search Bar -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_search"
                        app:tint="@color/text_secondary" />

                    <EditText
                        android:id="@+id/searchEditText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="البحث في المنتجات..."
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_secondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Products RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_product" />

            <!-- Empty State -->
            <TextView
                android:id="@+id/emptyStateText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                android:gravity="center"
                android:text="لا توجد منتجات"
                android:textColor="@color/text_secondary"
                android:textSize="18sp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addProductFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_check"
        app:tint="@color/white"
        app:backgroundTint="@color/primary_color" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
