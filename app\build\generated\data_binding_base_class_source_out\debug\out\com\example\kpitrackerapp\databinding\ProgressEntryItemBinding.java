// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ProgressEntryItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView entryDateTextView;

  @NonNull
  public final TextView entryValueTextView;

  private ProgressEntryItemBinding(@NonNull LinearLayout rootView,
      @NonNull TextView entryDateTextView, @NonNull TextView entryValueTextView) {
    this.rootView = rootView;
    this.entryDateTextView = entryDateTextView;
    this.entryValueTextView = entryValueTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ProgressEntryItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ProgressEntryItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.progress_entry_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ProgressEntryItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.entryDateTextView;
      TextView entryDateTextView = ViewBindings.findChildViewById(rootView, id);
      if (entryDateTextView == null) {
        break missingId;
      }

      id = R.id.entryValueTextView;
      TextView entryValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (entryValueTextView == null) {
        break missingId;
      }

      return new ProgressEntryItemBinding((LinearLayout) rootView, entryDateTextView,
          entryValueTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
