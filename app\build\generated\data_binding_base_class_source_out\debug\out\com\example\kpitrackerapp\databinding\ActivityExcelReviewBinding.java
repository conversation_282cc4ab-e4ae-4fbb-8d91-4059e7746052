// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityExcelReviewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnConfirmExcelImport;

  @NonNull
  public final RadioButton rbCalculateAverage;

  @NonNull
  public final RadioButton rbImportIndividual;

  @NonNull
  public final RadioGroup rgAggregationOptions;

  @NonNull
  public final RecyclerView rvExcelReviewItems;

  @NonNull
  public final Spinner spinnerMonthFilter;

  @NonNull
  public final Spinner spinnerUserFilter;

  @NonNull
  public final TextView tvExcelReviewTitle;

  private ActivityExcelReviewBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnConfirmExcelImport, @NonNull RadioButton rbCalculateAverage,
      @NonNull RadioButton rbImportIndividual, @NonNull RadioGroup rgAggregationOptions,
      @NonNull RecyclerView rvExcelReviewItems, @NonNull Spinner spinnerMonthFilter,
      @NonNull Spinner spinnerUserFilter, @NonNull TextView tvExcelReviewTitle) {
    this.rootView = rootView;
    this.btnConfirmExcelImport = btnConfirmExcelImport;
    this.rbCalculateAverage = rbCalculateAverage;
    this.rbImportIndividual = rbImportIndividual;
    this.rgAggregationOptions = rgAggregationOptions;
    this.rvExcelReviewItems = rvExcelReviewItems;
    this.spinnerMonthFilter = spinnerMonthFilter;
    this.spinnerUserFilter = spinnerUserFilter;
    this.tvExcelReviewTitle = tvExcelReviewTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityExcelReviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityExcelReviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_excel_review, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityExcelReviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnConfirmExcelImport;
      Button btnConfirmExcelImport = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirmExcelImport == null) {
        break missingId;
      }

      id = R.id.rbCalculateAverage;
      RadioButton rbCalculateAverage = ViewBindings.findChildViewById(rootView, id);
      if (rbCalculateAverage == null) {
        break missingId;
      }

      id = R.id.rbImportIndividual;
      RadioButton rbImportIndividual = ViewBindings.findChildViewById(rootView, id);
      if (rbImportIndividual == null) {
        break missingId;
      }

      id = R.id.rgAggregationOptions;
      RadioGroup rgAggregationOptions = ViewBindings.findChildViewById(rootView, id);
      if (rgAggregationOptions == null) {
        break missingId;
      }

      id = R.id.rvExcelReviewItems;
      RecyclerView rvExcelReviewItems = ViewBindings.findChildViewById(rootView, id);
      if (rvExcelReviewItems == null) {
        break missingId;
      }

      id = R.id.spinnerMonthFilter;
      Spinner spinnerMonthFilter = ViewBindings.findChildViewById(rootView, id);
      if (spinnerMonthFilter == null) {
        break missingId;
      }

      id = R.id.spinnerUserFilter;
      Spinner spinnerUserFilter = ViewBindings.findChildViewById(rootView, id);
      if (spinnerUserFilter == null) {
        break missingId;
      }

      id = R.id.tvExcelReviewTitle;
      TextView tvExcelReviewTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvExcelReviewTitle == null) {
        break missingId;
      }

      return new ActivityExcelReviewBinding((ConstraintLayout) rootView, btnConfirmExcelImport,
          rbCalculateAverage, rbImportIndividual, rgAggregationOptions, rvExcelReviewItems,
          spinnerMonthFilter, spinnerUserFilter, tvExcelReviewTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
