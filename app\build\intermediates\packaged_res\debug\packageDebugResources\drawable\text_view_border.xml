<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <stroke
        android:width="1dp"
        android:color="?android:attr/textColorSecondary" /> <!-- Use a standard secondary text color for the border -->
    <corners android:radius="4dp" /> <!-- Optional: Add rounded corners -->
    <solid android:color="@android:color/transparent" /> <!-- Make the background transparent -->
</shape>
