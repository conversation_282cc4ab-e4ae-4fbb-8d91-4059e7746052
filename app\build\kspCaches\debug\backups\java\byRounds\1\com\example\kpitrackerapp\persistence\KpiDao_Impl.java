package com.example.kpitrackerapp.persistence;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.kpitrackerapp.models.Kpi;
import com.example.kpitrackerapp.models.KpiUnit;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class KpiDao_Impl implements KpiDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Kpi> __insertionAdapterOfKpi;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<Kpi> __deletionAdapterOfKpi;

  private final EntityDeletionOrUpdateAdapter<Kpi> __updateAdapterOfKpi;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllKpis;

  private final SharedSQLiteStatement __preparedStmtOfUpdateKpiCardColor;

  public KpiDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfKpi = new EntityInsertionAdapter<Kpi>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `kpis` (`id`,`name`,`description`,`annual_target`,`quarterly_target`,`monthly_target`,`daily_target`,`unit`,`creation_date`,`owner_type`,`owner_name`,`owner_image_uri`,`card_color_hex`,`bottom_card_color_hex`,`master_kpi_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Kpi entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindDouble(4, entity.getAnnualTarget());
        if (entity.getQuarterlyTarget() == null) {
          statement.bindNull(5);
        } else {
          statement.bindDouble(5, entity.getQuarterlyTarget());
        }
        if (entity.getMonthlyTarget() == null) {
          statement.bindNull(6);
        } else {
          statement.bindDouble(6, entity.getMonthlyTarget());
        }
        if (entity.getDailyTarget() == null) {
          statement.bindNull(7);
        } else {
          statement.bindDouble(7, entity.getDailyTarget());
        }
        final String _tmp = __converters.kpiUnitToName(entity.getUnit());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getCreationDate());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp_1);
        }
        if (entity.getOwnerType() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getOwnerType());
        }
        if (entity.getOwnerName() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getOwnerName());
        }
        if (entity.getOwnerImageUri() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getOwnerImageUri());
        }
        if (entity.getCardColorHex() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getCardColorHex());
        }
        if (entity.getBottomCardColorHex() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getBottomCardColorHex());
        }
        if (entity.getMasterKpiId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getMasterKpiId());
        }
      }
    };
    this.__deletionAdapterOfKpi = new EntityDeletionOrUpdateAdapter<Kpi>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `kpis` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Kpi entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfKpi = new EntityDeletionOrUpdateAdapter<Kpi>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `kpis` SET `id` = ?,`name` = ?,`description` = ?,`annual_target` = ?,`quarterly_target` = ?,`monthly_target` = ?,`daily_target` = ?,`unit` = ?,`creation_date` = ?,`owner_type` = ?,`owner_name` = ?,`owner_image_uri` = ?,`card_color_hex` = ?,`bottom_card_color_hex` = ?,`master_kpi_id` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Kpi entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindDouble(4, entity.getAnnualTarget());
        if (entity.getQuarterlyTarget() == null) {
          statement.bindNull(5);
        } else {
          statement.bindDouble(5, entity.getQuarterlyTarget());
        }
        if (entity.getMonthlyTarget() == null) {
          statement.bindNull(6);
        } else {
          statement.bindDouble(6, entity.getMonthlyTarget());
        }
        if (entity.getDailyTarget() == null) {
          statement.bindNull(7);
        } else {
          statement.bindDouble(7, entity.getDailyTarget());
        }
        final String _tmp = __converters.kpiUnitToName(entity.getUnit());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getCreationDate());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp_1);
        }
        if (entity.getOwnerType() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getOwnerType());
        }
        if (entity.getOwnerName() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getOwnerName());
        }
        if (entity.getOwnerImageUri() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getOwnerImageUri());
        }
        if (entity.getCardColorHex() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getCardColorHex());
        }
        if (entity.getBottomCardColorHex() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getBottomCardColorHex());
        }
        if (entity.getMasterKpiId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getMasterKpiId());
        }
        statement.bindString(16, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAllKpis = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM kpis";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateKpiCardColor = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE kpis SET card_color_hex = ?, bottom_card_color_hex = NULL WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertKpi(final Kpi kpi, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfKpi.insert(kpi);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteKpi(final Kpi kpi, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfKpi.handle(kpi);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateKpi(final Kpi kpi, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfKpi.handle(kpi);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllKpis(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllKpis.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllKpis.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateKpiCardColor(final String kpiId, final String colorHex,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateKpiCardColor.acquire();
        int _argIndex = 1;
        if (colorHex == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, colorHex);
        }
        _argIndex = 2;
        _stmt.bindString(_argIndex, kpiId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateKpiCardColor.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Kpi>> getAllKpis() {
    final String _sql = "SELECT * FROM kpis ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpis"}, new Callable<List<Kpi>>() {
      @Override
      @NonNull
      public List<Kpi> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAnnualTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "annual_target");
          final int _cursorIndexOfQuarterlyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "quarterly_target");
          final int _cursorIndexOfMonthlyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "monthly_target");
          final int _cursorIndexOfDailyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "daily_target");
          final int _cursorIndexOfUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "unit");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creation_date");
          final int _cursorIndexOfOwnerType = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_type");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfOwnerImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_image_uri");
          final int _cursorIndexOfCardColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "card_color_hex");
          final int _cursorIndexOfBottomCardColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "bottom_card_color_hex");
          final int _cursorIndexOfMasterKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "master_kpi_id");
          final List<Kpi> _result = new ArrayList<Kpi>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Kpi _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final double _tmpAnnualTarget;
            _tmpAnnualTarget = _cursor.getDouble(_cursorIndexOfAnnualTarget);
            final Double _tmpQuarterlyTarget;
            if (_cursor.isNull(_cursorIndexOfQuarterlyTarget)) {
              _tmpQuarterlyTarget = null;
            } else {
              _tmpQuarterlyTarget = _cursor.getDouble(_cursorIndexOfQuarterlyTarget);
            }
            final Double _tmpMonthlyTarget;
            if (_cursor.isNull(_cursorIndexOfMonthlyTarget)) {
              _tmpMonthlyTarget = null;
            } else {
              _tmpMonthlyTarget = _cursor.getDouble(_cursorIndexOfMonthlyTarget);
            }
            final Double _tmpDailyTarget;
            if (_cursor.isNull(_cursorIndexOfDailyTarget)) {
              _tmpDailyTarget = null;
            } else {
              _tmpDailyTarget = _cursor.getDouble(_cursorIndexOfDailyTarget);
            }
            final KpiUnit _tmpUnit;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnit)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnit);
            }
            final KpiUnit _tmp_1 = __converters.fromKpiUnitName(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'com.example.kpitrackerapp.models.KpiUnit', but it was NULL.");
            } else {
              _tmpUnit = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final String _tmpOwnerType;
            if (_cursor.isNull(_cursorIndexOfOwnerType)) {
              _tmpOwnerType = null;
            } else {
              _tmpOwnerType = _cursor.getString(_cursorIndexOfOwnerType);
            }
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            final String _tmpOwnerImageUri;
            if (_cursor.isNull(_cursorIndexOfOwnerImageUri)) {
              _tmpOwnerImageUri = null;
            } else {
              _tmpOwnerImageUri = _cursor.getString(_cursorIndexOfOwnerImageUri);
            }
            final String _tmpCardColorHex;
            if (_cursor.isNull(_cursorIndexOfCardColorHex)) {
              _tmpCardColorHex = null;
            } else {
              _tmpCardColorHex = _cursor.getString(_cursorIndexOfCardColorHex);
            }
            final String _tmpBottomCardColorHex;
            if (_cursor.isNull(_cursorIndexOfBottomCardColorHex)) {
              _tmpBottomCardColorHex = null;
            } else {
              _tmpBottomCardColorHex = _cursor.getString(_cursorIndexOfBottomCardColorHex);
            }
            final String _tmpMasterKpiId;
            if (_cursor.isNull(_cursorIndexOfMasterKpiId)) {
              _tmpMasterKpiId = null;
            } else {
              _tmpMasterKpiId = _cursor.getString(_cursorIndexOfMasterKpiId);
            }
            _item = new Kpi(_tmpId,_tmpName,_tmpDescription,_tmpAnnualTarget,_tmpQuarterlyTarget,_tmpMonthlyTarget,_tmpDailyTarget,_tmpUnit,_tmpCreationDate,_tmpOwnerType,_tmpOwnerName,_tmpOwnerImageUri,_tmpCardColorHex,_tmpBottomCardColorHex,_tmpMasterKpiId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Kpi> getKpiById(final String kpiId) {
    final String _sql = "SELECT * FROM kpis WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpis"}, new Callable<Kpi>() {
      @Override
      @Nullable
      public Kpi call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAnnualTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "annual_target");
          final int _cursorIndexOfQuarterlyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "quarterly_target");
          final int _cursorIndexOfMonthlyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "monthly_target");
          final int _cursorIndexOfDailyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "daily_target");
          final int _cursorIndexOfUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "unit");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creation_date");
          final int _cursorIndexOfOwnerType = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_type");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfOwnerImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_image_uri");
          final int _cursorIndexOfCardColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "card_color_hex");
          final int _cursorIndexOfBottomCardColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "bottom_card_color_hex");
          final int _cursorIndexOfMasterKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "master_kpi_id");
          final Kpi _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final double _tmpAnnualTarget;
            _tmpAnnualTarget = _cursor.getDouble(_cursorIndexOfAnnualTarget);
            final Double _tmpQuarterlyTarget;
            if (_cursor.isNull(_cursorIndexOfQuarterlyTarget)) {
              _tmpQuarterlyTarget = null;
            } else {
              _tmpQuarterlyTarget = _cursor.getDouble(_cursorIndexOfQuarterlyTarget);
            }
            final Double _tmpMonthlyTarget;
            if (_cursor.isNull(_cursorIndexOfMonthlyTarget)) {
              _tmpMonthlyTarget = null;
            } else {
              _tmpMonthlyTarget = _cursor.getDouble(_cursorIndexOfMonthlyTarget);
            }
            final Double _tmpDailyTarget;
            if (_cursor.isNull(_cursorIndexOfDailyTarget)) {
              _tmpDailyTarget = null;
            } else {
              _tmpDailyTarget = _cursor.getDouble(_cursorIndexOfDailyTarget);
            }
            final KpiUnit _tmpUnit;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnit)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnit);
            }
            final KpiUnit _tmp_1 = __converters.fromKpiUnitName(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'com.example.kpitrackerapp.models.KpiUnit', but it was NULL.");
            } else {
              _tmpUnit = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final String _tmpOwnerType;
            if (_cursor.isNull(_cursorIndexOfOwnerType)) {
              _tmpOwnerType = null;
            } else {
              _tmpOwnerType = _cursor.getString(_cursorIndexOfOwnerType);
            }
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            final String _tmpOwnerImageUri;
            if (_cursor.isNull(_cursorIndexOfOwnerImageUri)) {
              _tmpOwnerImageUri = null;
            } else {
              _tmpOwnerImageUri = _cursor.getString(_cursorIndexOfOwnerImageUri);
            }
            final String _tmpCardColorHex;
            if (_cursor.isNull(_cursorIndexOfCardColorHex)) {
              _tmpCardColorHex = null;
            } else {
              _tmpCardColorHex = _cursor.getString(_cursorIndexOfCardColorHex);
            }
            final String _tmpBottomCardColorHex;
            if (_cursor.isNull(_cursorIndexOfBottomCardColorHex)) {
              _tmpBottomCardColorHex = null;
            } else {
              _tmpBottomCardColorHex = _cursor.getString(_cursorIndexOfBottomCardColorHex);
            }
            final String _tmpMasterKpiId;
            if (_cursor.isNull(_cursorIndexOfMasterKpiId)) {
              _tmpMasterKpiId = null;
            } else {
              _tmpMasterKpiId = _cursor.getString(_cursorIndexOfMasterKpiId);
            }
            _result = new Kpi(_tmpId,_tmpName,_tmpDescription,_tmpAnnualTarget,_tmpQuarterlyTarget,_tmpMonthlyTarget,_tmpDailyTarget,_tmpUnit,_tmpCreationDate,_tmpOwnerType,_tmpOwnerName,_tmpOwnerImageUri,_tmpCardColorHex,_tmpBottomCardColorHex,_tmpMasterKpiId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Kpi>> getKpisByName(final String kpiName) {
    final String _sql = "SELECT * FROM kpis WHERE name = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, kpiName);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"kpis"}, new Callable<List<Kpi>>() {
      @Override
      @NonNull
      public List<Kpi> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAnnualTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "annual_target");
          final int _cursorIndexOfQuarterlyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "quarterly_target");
          final int _cursorIndexOfMonthlyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "monthly_target");
          final int _cursorIndexOfDailyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "daily_target");
          final int _cursorIndexOfUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "unit");
          final int _cursorIndexOfCreationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "creation_date");
          final int _cursorIndexOfOwnerType = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_type");
          final int _cursorIndexOfOwnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_name");
          final int _cursorIndexOfOwnerImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "owner_image_uri");
          final int _cursorIndexOfCardColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "card_color_hex");
          final int _cursorIndexOfBottomCardColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "bottom_card_color_hex");
          final int _cursorIndexOfMasterKpiId = CursorUtil.getColumnIndexOrThrow(_cursor, "master_kpi_id");
          final List<Kpi> _result = new ArrayList<Kpi>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Kpi _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final double _tmpAnnualTarget;
            _tmpAnnualTarget = _cursor.getDouble(_cursorIndexOfAnnualTarget);
            final Double _tmpQuarterlyTarget;
            if (_cursor.isNull(_cursorIndexOfQuarterlyTarget)) {
              _tmpQuarterlyTarget = null;
            } else {
              _tmpQuarterlyTarget = _cursor.getDouble(_cursorIndexOfQuarterlyTarget);
            }
            final Double _tmpMonthlyTarget;
            if (_cursor.isNull(_cursorIndexOfMonthlyTarget)) {
              _tmpMonthlyTarget = null;
            } else {
              _tmpMonthlyTarget = _cursor.getDouble(_cursorIndexOfMonthlyTarget);
            }
            final Double _tmpDailyTarget;
            if (_cursor.isNull(_cursorIndexOfDailyTarget)) {
              _tmpDailyTarget = null;
            } else {
              _tmpDailyTarget = _cursor.getDouble(_cursorIndexOfDailyTarget);
            }
            final KpiUnit _tmpUnit;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnit)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnit);
            }
            final KpiUnit _tmp_1 = __converters.fromKpiUnitName(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'com.example.kpitrackerapp.models.KpiUnit', but it was NULL.");
            } else {
              _tmpUnit = _tmp_1;
            }
            final Date _tmpCreationDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreationDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreationDate);
            }
            final Date _tmp_3 = __converters.fromTimestamp(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.Date', but it was NULL.");
            } else {
              _tmpCreationDate = _tmp_3;
            }
            final String _tmpOwnerType;
            if (_cursor.isNull(_cursorIndexOfOwnerType)) {
              _tmpOwnerType = null;
            } else {
              _tmpOwnerType = _cursor.getString(_cursorIndexOfOwnerType);
            }
            final String _tmpOwnerName;
            if (_cursor.isNull(_cursorIndexOfOwnerName)) {
              _tmpOwnerName = null;
            } else {
              _tmpOwnerName = _cursor.getString(_cursorIndexOfOwnerName);
            }
            final String _tmpOwnerImageUri;
            if (_cursor.isNull(_cursorIndexOfOwnerImageUri)) {
              _tmpOwnerImageUri = null;
            } else {
              _tmpOwnerImageUri = _cursor.getString(_cursorIndexOfOwnerImageUri);
            }
            final String _tmpCardColorHex;
            if (_cursor.isNull(_cursorIndexOfCardColorHex)) {
              _tmpCardColorHex = null;
            } else {
              _tmpCardColorHex = _cursor.getString(_cursorIndexOfCardColorHex);
            }
            final String _tmpBottomCardColorHex;
            if (_cursor.isNull(_cursorIndexOfBottomCardColorHex)) {
              _tmpBottomCardColorHex = null;
            } else {
              _tmpBottomCardColorHex = _cursor.getString(_cursorIndexOfBottomCardColorHex);
            }
            final String _tmpMasterKpiId;
            if (_cursor.isNull(_cursorIndexOfMasterKpiId)) {
              _tmpMasterKpiId = null;
            } else {
              _tmpMasterKpiId = _cursor.getString(_cursorIndexOfMasterKpiId);
            }
            _item = new Kpi(_tmpId,_tmpName,_tmpDescription,_tmpAnnualTarget,_tmpQuarterlyTarget,_tmpMonthlyTarget,_tmpDailyTarget,_tmpUnit,_tmpCreationDate,_tmpOwnerType,_tmpOwnerName,_tmpOwnerImageUri,_tmpCardColorHex,_tmpBottomCardColorHex,_tmpMasterKpiId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
