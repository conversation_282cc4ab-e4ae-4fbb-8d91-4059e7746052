// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskEnhancedBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton btnScheduleTask;

  @NonNull
  public final MaterialButton btnStartTask;

  @NonNull
  public final MaterialButton btnTaskSuggestions;

  @NonNull
  public final CheckBox cbTaskComplete;

  @NonNull
  public final Chip chipCategory;

  @NonNull
  public final ChipGroup chipGroupTags;

  @NonNull
  public final Chip chipPriority;

  @NonNull
  public final ImageView ivAttachment;

  @NonNull
  public final ImageView ivCompleteTask;

  @NonNull
  public final ImageView ivTaskActions;

  @NonNull
  public final LinearLayout layoutActionButtons;

  @NonNull
  public final LinearLayout layoutAssignedUser;

  @NonNull
  public final LinearLayout layoutDetails;

  @NonNull
  public final LinearLayout layoutEnergyFocus;

  @NonNull
  public final LinearLayout layoutFocusTime;

  @NonNull
  public final LinearLayout layoutQuickActions;

  @NonNull
  public final View priorityIndicator;

  @NonNull
  public final ProgressBar progressTask;

  @NonNull
  public final RecyclerView rvSubtasks;

  @NonNull
  public final View statusIndicatorDot;

  @NonNull
  public final TextView tvAssignedUser;

  @NonNull
  public final TextView tvDueDate;

  @NonNull
  public final TextView tvEnergyIcon;

  @NonNull
  public final TextView tvEnergyLevel;

  @NonNull
  public final TextView tvFocusTime;

  @NonNull
  public final TextView tvProgressPercent;

  @NonNull
  public final TextView tvTaskDescription;

  @NonNull
  public final TextView tvTaskDetails;

  @NonNull
  public final TextView tvTaskIcon;

  @NonNull
  public final TextView tvTaskName;

  @NonNull
  public final TextView tvTimeRemaining;

  private ItemTaskEnhancedBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton btnScheduleTask, @NonNull MaterialButton btnStartTask,
      @NonNull MaterialButton btnTaskSuggestions, @NonNull CheckBox cbTaskComplete,
      @NonNull Chip chipCategory, @NonNull ChipGroup chipGroupTags, @NonNull Chip chipPriority,
      @NonNull ImageView ivAttachment, @NonNull ImageView ivCompleteTask,
      @NonNull ImageView ivTaskActions, @NonNull LinearLayout layoutActionButtons,
      @NonNull LinearLayout layoutAssignedUser, @NonNull LinearLayout layoutDetails,
      @NonNull LinearLayout layoutEnergyFocus, @NonNull LinearLayout layoutFocusTime,
      @NonNull LinearLayout layoutQuickActions, @NonNull View priorityIndicator,
      @NonNull ProgressBar progressTask, @NonNull RecyclerView rvSubtasks,
      @NonNull View statusIndicatorDot, @NonNull TextView tvAssignedUser,
      @NonNull TextView tvDueDate, @NonNull TextView tvEnergyIcon, @NonNull TextView tvEnergyLevel,
      @NonNull TextView tvFocusTime, @NonNull TextView tvProgressPercent,
      @NonNull TextView tvTaskDescription, @NonNull TextView tvTaskDetails,
      @NonNull TextView tvTaskIcon, @NonNull TextView tvTaskName,
      @NonNull TextView tvTimeRemaining) {
    this.rootView = rootView;
    this.btnScheduleTask = btnScheduleTask;
    this.btnStartTask = btnStartTask;
    this.btnTaskSuggestions = btnTaskSuggestions;
    this.cbTaskComplete = cbTaskComplete;
    this.chipCategory = chipCategory;
    this.chipGroupTags = chipGroupTags;
    this.chipPriority = chipPriority;
    this.ivAttachment = ivAttachment;
    this.ivCompleteTask = ivCompleteTask;
    this.ivTaskActions = ivTaskActions;
    this.layoutActionButtons = layoutActionButtons;
    this.layoutAssignedUser = layoutAssignedUser;
    this.layoutDetails = layoutDetails;
    this.layoutEnergyFocus = layoutEnergyFocus;
    this.layoutFocusTime = layoutFocusTime;
    this.layoutQuickActions = layoutQuickActions;
    this.priorityIndicator = priorityIndicator;
    this.progressTask = progressTask;
    this.rvSubtasks = rvSubtasks;
    this.statusIndicatorDot = statusIndicatorDot;
    this.tvAssignedUser = tvAssignedUser;
    this.tvDueDate = tvDueDate;
    this.tvEnergyIcon = tvEnergyIcon;
    this.tvEnergyLevel = tvEnergyLevel;
    this.tvFocusTime = tvFocusTime;
    this.tvProgressPercent = tvProgressPercent;
    this.tvTaskDescription = tvTaskDescription;
    this.tvTaskDetails = tvTaskDetails;
    this.tvTaskIcon = tvTaskIcon;
    this.tvTaskName = tvTaskName;
    this.tvTimeRemaining = tvTimeRemaining;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskEnhancedBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskEnhancedBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task_enhanced, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskEnhancedBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnScheduleTask;
      MaterialButton btnScheduleTask = ViewBindings.findChildViewById(rootView, id);
      if (btnScheduleTask == null) {
        break missingId;
      }

      id = R.id.btnStartTask;
      MaterialButton btnStartTask = ViewBindings.findChildViewById(rootView, id);
      if (btnStartTask == null) {
        break missingId;
      }

      id = R.id.btnTaskSuggestions;
      MaterialButton btnTaskSuggestions = ViewBindings.findChildViewById(rootView, id);
      if (btnTaskSuggestions == null) {
        break missingId;
      }

      id = R.id.cbTaskComplete;
      CheckBox cbTaskComplete = ViewBindings.findChildViewById(rootView, id);
      if (cbTaskComplete == null) {
        break missingId;
      }

      id = R.id.chipCategory;
      Chip chipCategory = ViewBindings.findChildViewById(rootView, id);
      if (chipCategory == null) {
        break missingId;
      }

      id = R.id.chipGroupTags;
      ChipGroup chipGroupTags = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupTags == null) {
        break missingId;
      }

      id = R.id.chipPriority;
      Chip chipPriority = ViewBindings.findChildViewById(rootView, id);
      if (chipPriority == null) {
        break missingId;
      }

      id = R.id.ivAttachment;
      ImageView ivAttachment = ViewBindings.findChildViewById(rootView, id);
      if (ivAttachment == null) {
        break missingId;
      }

      id = R.id.ivCompleteTask;
      ImageView ivCompleteTask = ViewBindings.findChildViewById(rootView, id);
      if (ivCompleteTask == null) {
        break missingId;
      }

      id = R.id.ivTaskActions;
      ImageView ivTaskActions = ViewBindings.findChildViewById(rootView, id);
      if (ivTaskActions == null) {
        break missingId;
      }

      id = R.id.layoutActionButtons;
      LinearLayout layoutActionButtons = ViewBindings.findChildViewById(rootView, id);
      if (layoutActionButtons == null) {
        break missingId;
      }

      id = R.id.layoutAssignedUser;
      LinearLayout layoutAssignedUser = ViewBindings.findChildViewById(rootView, id);
      if (layoutAssignedUser == null) {
        break missingId;
      }

      id = R.id.layoutDetails;
      LinearLayout layoutDetails = ViewBindings.findChildViewById(rootView, id);
      if (layoutDetails == null) {
        break missingId;
      }

      id = R.id.layoutEnergyFocus;
      LinearLayout layoutEnergyFocus = ViewBindings.findChildViewById(rootView, id);
      if (layoutEnergyFocus == null) {
        break missingId;
      }

      id = R.id.layoutFocusTime;
      LinearLayout layoutFocusTime = ViewBindings.findChildViewById(rootView, id);
      if (layoutFocusTime == null) {
        break missingId;
      }

      id = R.id.layoutQuickActions;
      LinearLayout layoutQuickActions = ViewBindings.findChildViewById(rootView, id);
      if (layoutQuickActions == null) {
        break missingId;
      }

      id = R.id.priorityIndicator;
      View priorityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (priorityIndicator == null) {
        break missingId;
      }

      id = R.id.progressTask;
      ProgressBar progressTask = ViewBindings.findChildViewById(rootView, id);
      if (progressTask == null) {
        break missingId;
      }

      id = R.id.rvSubtasks;
      RecyclerView rvSubtasks = ViewBindings.findChildViewById(rootView, id);
      if (rvSubtasks == null) {
        break missingId;
      }

      id = R.id.statusIndicatorDot;
      View statusIndicatorDot = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicatorDot == null) {
        break missingId;
      }

      id = R.id.tvAssignedUser;
      TextView tvAssignedUser = ViewBindings.findChildViewById(rootView, id);
      if (tvAssignedUser == null) {
        break missingId;
      }

      id = R.id.tvDueDate;
      TextView tvDueDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDueDate == null) {
        break missingId;
      }

      id = R.id.tvEnergyIcon;
      TextView tvEnergyIcon = ViewBindings.findChildViewById(rootView, id);
      if (tvEnergyIcon == null) {
        break missingId;
      }

      id = R.id.tvEnergyLevel;
      TextView tvEnergyLevel = ViewBindings.findChildViewById(rootView, id);
      if (tvEnergyLevel == null) {
        break missingId;
      }

      id = R.id.tvFocusTime;
      TextView tvFocusTime = ViewBindings.findChildViewById(rootView, id);
      if (tvFocusTime == null) {
        break missingId;
      }

      id = R.id.tvProgressPercent;
      TextView tvProgressPercent = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressPercent == null) {
        break missingId;
      }

      id = R.id.tvTaskDescription;
      TextView tvTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDescription == null) {
        break missingId;
      }

      id = R.id.tvTaskDetails;
      TextView tvTaskDetails = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDetails == null) {
        break missingId;
      }

      id = R.id.tvTaskIcon;
      TextView tvTaskIcon = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskIcon == null) {
        break missingId;
      }

      id = R.id.tvTaskName;
      TextView tvTaskName = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskName == null) {
        break missingId;
      }

      id = R.id.tvTimeRemaining;
      TextView tvTimeRemaining = ViewBindings.findChildViewById(rootView, id);
      if (tvTimeRemaining == null) {
        break missingId;
      }

      return new ItemTaskEnhancedBinding((MaterialCardView) rootView, btnScheduleTask, btnStartTask,
          btnTaskSuggestions, cbTaskComplete, chipCategory, chipGroupTags, chipPriority,
          ivAttachment, ivCompleteTask, ivTaskActions, layoutActionButtons, layoutAssignedUser,
          layoutDetails, layoutEnergyFocus, layoutFocusTime, layoutQuickActions, priorityIndicator,
          progressTask, rvSubtasks, statusIndicatorDot, tvAssignedUser, tvDueDate, tvEnergyIcon,
          tvEnergyLevel, tvFocusTime, tvProgressPercent, tvTaskDescription, tvTaskDetails,
          tvTaskIcon, tvTaskName, tvTimeRemaining);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
