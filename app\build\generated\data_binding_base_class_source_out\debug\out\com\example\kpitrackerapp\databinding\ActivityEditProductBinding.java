// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEditProductBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextInputEditText barcodeInput;

  @NonNull
  public final TextInputLayout barcodeInputLayout;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final AutoCompleteTextView categorySpinner;

  @NonNull
  public final MaterialButton deleteButton;

  @NonNull
  public final TextInputEditText expiryDateInput;

  @NonNull
  public final TextInputEditText locationInput;

  @NonNull
  public final TextInputEditText notesInput;

  @NonNull
  public final TextInputEditText productNameInput;

  @NonNull
  public final TextInputLayout productNameInputLayout;

  @NonNull
  public final TextInputEditText quantityInput;

  @NonNull
  public final MaterialButton updateProductButton;

  private ActivityEditProductBinding(@NonNull ScrollView rootView,
      @NonNull TextInputEditText barcodeInput, @NonNull TextInputLayout barcodeInputLayout,
      @NonNull MaterialButton cancelButton, @NonNull AutoCompleteTextView categorySpinner,
      @NonNull MaterialButton deleteButton, @NonNull TextInputEditText expiryDateInput,
      @NonNull TextInputEditText locationInput, @NonNull TextInputEditText notesInput,
      @NonNull TextInputEditText productNameInput, @NonNull TextInputLayout productNameInputLayout,
      @NonNull TextInputEditText quantityInput, @NonNull MaterialButton updateProductButton) {
    this.rootView = rootView;
    this.barcodeInput = barcodeInput;
    this.barcodeInputLayout = barcodeInputLayout;
    this.cancelButton = cancelButton;
    this.categorySpinner = categorySpinner;
    this.deleteButton = deleteButton;
    this.expiryDateInput = expiryDateInput;
    this.locationInput = locationInput;
    this.notesInput = notesInput;
    this.productNameInput = productNameInput;
    this.productNameInputLayout = productNameInputLayout;
    this.quantityInput = quantityInput;
    this.updateProductButton = updateProductButton;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEditProductBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEditProductBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_edit_product, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEditProductBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.barcodeInput;
      TextInputEditText barcodeInput = ViewBindings.findChildViewById(rootView, id);
      if (barcodeInput == null) {
        break missingId;
      }

      id = R.id.barcodeInputLayout;
      TextInputLayout barcodeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (barcodeInputLayout == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.categorySpinner;
      AutoCompleteTextView categorySpinner = ViewBindings.findChildViewById(rootView, id);
      if (categorySpinner == null) {
        break missingId;
      }

      id = R.id.deleteButton;
      MaterialButton deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.expiryDateInput;
      TextInputEditText expiryDateInput = ViewBindings.findChildViewById(rootView, id);
      if (expiryDateInput == null) {
        break missingId;
      }

      id = R.id.locationInput;
      TextInputEditText locationInput = ViewBindings.findChildViewById(rootView, id);
      if (locationInput == null) {
        break missingId;
      }

      id = R.id.notesInput;
      TextInputEditText notesInput = ViewBindings.findChildViewById(rootView, id);
      if (notesInput == null) {
        break missingId;
      }

      id = R.id.productNameInput;
      TextInputEditText productNameInput = ViewBindings.findChildViewById(rootView, id);
      if (productNameInput == null) {
        break missingId;
      }

      id = R.id.productNameInputLayout;
      TextInputLayout productNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (productNameInputLayout == null) {
        break missingId;
      }

      id = R.id.quantityInput;
      TextInputEditText quantityInput = ViewBindings.findChildViewById(rootView, id);
      if (quantityInput == null) {
        break missingId;
      }

      id = R.id.updateProductButton;
      MaterialButton updateProductButton = ViewBindings.findChildViewById(rootView, id);
      if (updateProductButton == null) {
        break missingId;
      }

      return new ActivityEditProductBinding((ScrollView) rootView, barcodeInput, barcodeInputLayout,
          cancelButton, categorySpinner, deleteButton, expiryDateInput, locationInput, notesInput,
          productNameInput, productNameInputLayout, quantityInput, updateProductButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
