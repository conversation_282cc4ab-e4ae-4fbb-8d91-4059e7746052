<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.TaskReportActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayoutTaskReport"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbarTaskReport"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="Task Reports" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvTasksDueSoonTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tasks Due Soon (Next 7 Days)"
                android:textAppearance="?attr/textAppearanceHeadline6" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvTasksDueSoon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_task_report"
                tools:itemCount="2"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="?android:attr/listDivider"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"/>

            <TextView
                android:id="@+id/tvAllTasksTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="All Tasks"
                android:textAppearance="?attr/textAppearanceHeadline6"
                android:layout_marginTop="16dp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvAllTasksReport"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_task_report"
                tools:itemCount="5"/>

            <Button
                android:id="@+id/btnSaveTaskReport"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="Save Report as Text" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
