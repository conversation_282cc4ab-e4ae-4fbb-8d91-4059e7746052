// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemChatMessageReceivedBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView editedText;

  @NonNull
  public final MaterialCardView messageCard;

  @NonNull
  public final TextView messageText;

  @NonNull
  public final LinearLayout replyContainer;

  @NonNull
  public final TextView replyMessageText;

  @NonNull
  public final TextView replyToText;

  @NonNull
  public final TextView senderText;

  @NonNull
  public final TextView timeText;

  @NonNull
  public final ImageView userAvatar;

  private ItemChatMessageReceivedBinding(@NonNull LinearLayout rootView,
      @NonNull TextView editedText, @NonNull MaterialCardView messageCard,
      @NonNull TextView messageText, @NonNull LinearLayout replyContainer,
      @NonNull TextView replyMessageText, @NonNull TextView replyToText,
      @NonNull TextView senderText, @NonNull TextView timeText, @NonNull ImageView userAvatar) {
    this.rootView = rootView;
    this.editedText = editedText;
    this.messageCard = messageCard;
    this.messageText = messageText;
    this.replyContainer = replyContainer;
    this.replyMessageText = replyMessageText;
    this.replyToText = replyToText;
    this.senderText = senderText;
    this.timeText = timeText;
    this.userAvatar = userAvatar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemChatMessageReceivedBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemChatMessageReceivedBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_chat_message_received, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemChatMessageReceivedBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.editedText;
      TextView editedText = ViewBindings.findChildViewById(rootView, id);
      if (editedText == null) {
        break missingId;
      }

      id = R.id.messageCard;
      MaterialCardView messageCard = ViewBindings.findChildViewById(rootView, id);
      if (messageCard == null) {
        break missingId;
      }

      id = R.id.messageText;
      TextView messageText = ViewBindings.findChildViewById(rootView, id);
      if (messageText == null) {
        break missingId;
      }

      id = R.id.replyContainer;
      LinearLayout replyContainer = ViewBindings.findChildViewById(rootView, id);
      if (replyContainer == null) {
        break missingId;
      }

      id = R.id.replyMessageText;
      TextView replyMessageText = ViewBindings.findChildViewById(rootView, id);
      if (replyMessageText == null) {
        break missingId;
      }

      id = R.id.replyToText;
      TextView replyToText = ViewBindings.findChildViewById(rootView, id);
      if (replyToText == null) {
        break missingId;
      }

      id = R.id.senderText;
      TextView senderText = ViewBindings.findChildViewById(rootView, id);
      if (senderText == null) {
        break missingId;
      }

      id = R.id.timeText;
      TextView timeText = ViewBindings.findChildViewById(rootView, id);
      if (timeText == null) {
        break missingId;
      }

      id = R.id.userAvatar;
      ImageView userAvatar = ViewBindings.findChildViewById(rootView, id);
      if (userAvatar == null) {
        break missingId;
      }

      return new ItemChatMessageReceivedBinding((LinearLayout) rootView, editedText, messageCard,
          messageText, replyContainer, replyMessageText, replyToText, senderText, timeText,
          userAvatar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
