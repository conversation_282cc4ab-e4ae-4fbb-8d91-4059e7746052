// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskReportBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView tvReportTaskExpirationDate;

  @NonNull
  public final TextView tvReportTaskName;

  private ItemTaskReportBinding(@NonNull LinearLayout rootView,
      @NonNull TextView tvReportTaskExpirationDate, @NonNull TextView tvReportTaskName) {
    this.rootView = rootView;
    this.tvReportTaskExpirationDate = tvReportTaskExpirationDate;
    this.tvReportTaskName = tvReportTaskName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskReportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskReportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task_report, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskReportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tvReportTaskExpirationDate;
      TextView tvReportTaskExpirationDate = ViewBindings.findChildViewById(rootView, id);
      if (tvReportTaskExpirationDate == null) {
        break missingId;
      }

      id = R.id.tvReportTaskName;
      TextView tvReportTaskName = ViewBindings.findChildViewById(rootView, id);
      if (tvReportTaskName == null) {
        break missingId;
      }

      return new ItemTaskReportBinding((LinearLayout) rootView, tvReportTaskExpirationDate,
          tvReportTaskName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
