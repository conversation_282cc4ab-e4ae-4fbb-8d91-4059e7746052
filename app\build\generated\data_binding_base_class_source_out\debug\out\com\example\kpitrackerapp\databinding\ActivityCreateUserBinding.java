// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCreateUserBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton addPhotoButton;

  @NonNull
  public final LinearLayout buttonLayout;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final MaterialButton createButton;

  @NonNull
  public final TextInputEditText departmentEditText;

  @NonNull
  public final TextInputLayout departmentInputLayout;

  @NonNull
  public final TextInputEditText emailEditText;

  @NonNull
  public final TextInputLayout emailInputLayout;

  @NonNull
  public final LinearLayout formLayout;

  @NonNull
  public final TextInputEditText fullNameEditText;

  @NonNull
  public final TextInputLayout fullNameInputLayout;

  @NonNull
  public final TextView headerTextView;

  @NonNull
  public final ProgressBar loadingProgressBar;

  @NonNull
  public final MaterialCardView profilePictureCard;

  @NonNull
  public final ImageView profilePictureImageView;

  @NonNull
  public final TextInputEditText usernameEditText;

  @NonNull
  public final TextInputLayout usernameInputLayout;

  private ActivityCreateUserBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton addPhotoButton, @NonNull LinearLayout buttonLayout,
      @NonNull MaterialButton cancelButton, @NonNull MaterialButton createButton,
      @NonNull TextInputEditText departmentEditText, @NonNull TextInputLayout departmentInputLayout,
      @NonNull TextInputEditText emailEditText, @NonNull TextInputLayout emailInputLayout,
      @NonNull LinearLayout formLayout, @NonNull TextInputEditText fullNameEditText,
      @NonNull TextInputLayout fullNameInputLayout, @NonNull TextView headerTextView,
      @NonNull ProgressBar loadingProgressBar, @NonNull MaterialCardView profilePictureCard,
      @NonNull ImageView profilePictureImageView, @NonNull TextInputEditText usernameEditText,
      @NonNull TextInputLayout usernameInputLayout) {
    this.rootView = rootView;
    this.addPhotoButton = addPhotoButton;
    this.buttonLayout = buttonLayout;
    this.cancelButton = cancelButton;
    this.createButton = createButton;
    this.departmentEditText = departmentEditText;
    this.departmentInputLayout = departmentInputLayout;
    this.emailEditText = emailEditText;
    this.emailInputLayout = emailInputLayout;
    this.formLayout = formLayout;
    this.fullNameEditText = fullNameEditText;
    this.fullNameInputLayout = fullNameInputLayout;
    this.headerTextView = headerTextView;
    this.loadingProgressBar = loadingProgressBar;
    this.profilePictureCard = profilePictureCard;
    this.profilePictureImageView = profilePictureImageView;
    this.usernameEditText = usernameEditText;
    this.usernameInputLayout = usernameInputLayout;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCreateUserBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCreateUserBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_create_user, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCreateUserBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addPhotoButton;
      MaterialButton addPhotoButton = ViewBindings.findChildViewById(rootView, id);
      if (addPhotoButton == null) {
        break missingId;
      }

      id = R.id.buttonLayout;
      LinearLayout buttonLayout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLayout == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.createButton;
      MaterialButton createButton = ViewBindings.findChildViewById(rootView, id);
      if (createButton == null) {
        break missingId;
      }

      id = R.id.departmentEditText;
      TextInputEditText departmentEditText = ViewBindings.findChildViewById(rootView, id);
      if (departmentEditText == null) {
        break missingId;
      }

      id = R.id.departmentInputLayout;
      TextInputLayout departmentInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (departmentInputLayout == null) {
        break missingId;
      }

      id = R.id.emailEditText;
      TextInputEditText emailEditText = ViewBindings.findChildViewById(rootView, id);
      if (emailEditText == null) {
        break missingId;
      }

      id = R.id.emailInputLayout;
      TextInputLayout emailInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (emailInputLayout == null) {
        break missingId;
      }

      id = R.id.formLayout;
      LinearLayout formLayout = ViewBindings.findChildViewById(rootView, id);
      if (formLayout == null) {
        break missingId;
      }

      id = R.id.fullNameEditText;
      TextInputEditText fullNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (fullNameEditText == null) {
        break missingId;
      }

      id = R.id.fullNameInputLayout;
      TextInputLayout fullNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (fullNameInputLayout == null) {
        break missingId;
      }

      id = R.id.headerTextView;
      TextView headerTextView = ViewBindings.findChildViewById(rootView, id);
      if (headerTextView == null) {
        break missingId;
      }

      id = R.id.loadingProgressBar;
      ProgressBar loadingProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgressBar == null) {
        break missingId;
      }

      id = R.id.profilePictureCard;
      MaterialCardView profilePictureCard = ViewBindings.findChildViewById(rootView, id);
      if (profilePictureCard == null) {
        break missingId;
      }

      id = R.id.profilePictureImageView;
      ImageView profilePictureImageView = ViewBindings.findChildViewById(rootView, id);
      if (profilePictureImageView == null) {
        break missingId;
      }

      id = R.id.usernameEditText;
      TextInputEditText usernameEditText = ViewBindings.findChildViewById(rootView, id);
      if (usernameEditText == null) {
        break missingId;
      }

      id = R.id.usernameInputLayout;
      TextInputLayout usernameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (usernameInputLayout == null) {
        break missingId;
      }

      return new ActivityCreateUserBinding((ScrollView) rootView, addPhotoButton, buttonLayout,
          cancelButton, createButton, departmentEditText, departmentInputLayout, emailEditText,
          emailInputLayout, formLayout, fullNameEditText, fullNameInputLayout, headerTextView,
          loadingProgressBar, profilePictureCard, profilePictureImageView, usernameEditText,
          usernameInputLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
