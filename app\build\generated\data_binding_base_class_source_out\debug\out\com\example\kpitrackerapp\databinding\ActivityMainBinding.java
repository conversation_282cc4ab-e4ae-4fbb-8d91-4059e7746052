// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.Barrier;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final MaterialButton btnCreateMasterCard;

  @NonNull
  public final FrameLayout fragmentContainer;

  @NonNull
  public final Barrier masterCardBarrier;

  @NonNull
  public final OverallSummaryCardItemBinding overallSummaryCardInclude;

  @NonNull
  public final Toolbar toolbar;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull BottomNavigationView bottomNavigation,
      @NonNull MaterialButton btnCreateMasterCard, @NonNull FrameLayout fragmentContainer,
      @NonNull Barrier masterCardBarrier,
      @NonNull OverallSummaryCardItemBinding overallSummaryCardInclude, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.bottomNavigation = bottomNavigation;
    this.btnCreateMasterCard = btnCreateMasterCard;
    this.fragmentContainer = fragmentContainer;
    this.masterCardBarrier = masterCardBarrier;
    this.overallSummaryCardInclude = overallSummaryCardInclude;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btnCreateMasterCard;
      MaterialButton btnCreateMasterCard = ViewBindings.findChildViewById(rootView, id);
      if (btnCreateMasterCard == null) {
        break missingId;
      }

      id = R.id.fragmentContainer;
      FrameLayout fragmentContainer = ViewBindings.findChildViewById(rootView, id);
      if (fragmentContainer == null) {
        break missingId;
      }

      id = R.id.masterCardBarrier;
      Barrier masterCardBarrier = ViewBindings.findChildViewById(rootView, id);
      if (masterCardBarrier == null) {
        break missingId;
      }

      id = R.id.overallSummaryCardInclude;
      View overallSummaryCardInclude = ViewBindings.findChildViewById(rootView, id);
      if (overallSummaryCardInclude == null) {
        break missingId;
      }
      OverallSummaryCardItemBinding binding_overallSummaryCardInclude = OverallSummaryCardItemBinding.bind(overallSummaryCardInclude);

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, appBarLayout, bottomNavigation,
          btnCreateMasterCard, fragmentContainer, masterCardBarrier,
          binding_overallSummaryCardInclude, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
