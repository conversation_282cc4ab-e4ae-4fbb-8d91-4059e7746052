// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSubtaskMiniBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox cbSubtaskComplete;

  @NonNull
  public final TextView tvSubtaskName;

  private ItemSubtaskMiniBinding(@NonNull LinearLayout rootView,
      @NonNull CheckBox cbSubtaskComplete, @NonNull TextView tvSubtaskName) {
    this.rootView = rootView;
    this.cbSubtaskComplete = cbSubtaskComplete;
    this.tvSubtaskName = tvSubtaskName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSubtaskMiniBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSubtaskMiniBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_subtask_mini, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSubtaskMiniBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cbSubtaskComplete;
      CheckBox cbSubtaskComplete = ViewBindings.findChildViewById(rootView, id);
      if (cbSubtaskComplete == null) {
        break missingId;
      }

      id = R.id.tvSubtaskName;
      TextView tvSubtaskName = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtaskName == null) {
        break missingId;
      }

      return new ItemSubtaskMiniBinding((LinearLayout) rootView, cbSubtaskComplete, tvSubtaskName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
