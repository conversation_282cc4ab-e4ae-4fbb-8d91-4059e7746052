<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_edit_kpi" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_add_edit_kpi.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_add_edit_kpi_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="1085" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="23" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="47"/></Target><Target id="@+id/bottomAppBar" view="com.google.android.material.bottomappbar.BottomAppBar"><Expressions/><location startLine="26" startOffset="4" endLine="91" endOffset="59"/></Target><Target id="@+id/btnVoiceNote" view="ImageButton"><Expressions/><location startLine="41" startOffset="12" endLine="49" endOffset="49"/></Target><Target id="@+id/btnCamera" view="ImageButton"><Expressions/><location startLine="51" startOffset="12" endLine="59" endOffset="49"/></Target><Target id="@+id/btnLocation" view="ImageButton"><Expressions/><location startLine="61" startOffset="12" endLine="69" endOffset="49"/></Target><Target id="@+id/btnTimer" view="ImageButton"><Expressions/><location startLine="71" startOffset="12" endLine="79" endOffset="49"/></Target><Target id="@+id/btnTemplate" view="ImageButton"><Expressions/><location startLine="81" startOffset="12" endLine="88" endOffset="49"/></Target><Target id="@+id/cardAiSuggestions" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="107" startOffset="12" endLine="155" endOffset="63"/></Target><Target id="@+id/templateMeeting" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="177" startOffset="20" endLine="210" endOffset="71"/></Target><Target id="@+id/templateStudy" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="212" startOffset="20" endLine="245" endOffset="71"/></Target><Target id="@+id/templateCall" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="247" startOffset="20" endLine="280" endOffset="71"/></Target><Target id="@+id/templateExercise" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="282" startOffset="20" endLine="314" endOffset="71"/></Target><Target id="@+id/taskNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="345" startOffset="20" endLine="364" endOffset="75"/></Target><Target id="@+id/taskNameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="359" startOffset="24" endLine="363" endOffset="66"/></Target><Target id="@+id/taskDescriptionInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="367" startOffset="20" endLine="388" endOffset="75"/></Target><Target id="@+id/taskDescriptionEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="381" startOffset="24" endLine="387" endOffset="50"/></Target><Target id="@+id/taskExpirationDateInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="391" startOffset="20" endLine="415" endOffset="75"/></Target><Target id="@+id/taskExpirationDateEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="408" startOffset="24" endLine="414" endOffset="54"/></Target><Target id="@+id/taskExpirationTimeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="418" startOffset="20" endLine="442" endOffset="75"/></Target><Target id="@+id/taskExpirationTimeEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="435" startOffset="24" endLine="441" endOffset="54"/></Target><Target id="@+id/taskPrioritySelector" view="LinearLayout"><Expressions/><location startLine="464" startOffset="24" endLine="491" endOffset="38"/></Target><Target id="@+id/taskPriorityText" view="TextView"><Expressions/><location startLine="476" startOffset="28" endLine="483" endOffset="73"/></Target><Target id="@+id/taskCategorySelector" view="LinearLayout"><Expressions/><location startLine="515" startOffset="24" endLine="542" endOffset="38"/></Target><Target id="@+id/taskCategoryText" view="TextView"><Expressions/><location startLine="527" startOffset="28" endLine="534" endOffset="73"/></Target><Target id="@+id/taskEstimatedTimeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="547" startOffset="20" endLine="567" endOffset="75"/></Target><Target id="@+id/taskEstimatedTimeEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="561" startOffset="24" endLine="566" endOffset="48"/></Target><Target id="@+id/tvAdvancedDetailsHeader" view="TextView"><Expressions/><location startLine="586" startOffset="20" endLine="599" endOffset="69"/></Target><Target id="@+id/layoutAdvancedDetails" view="LinearLayout"><Expressions/><location startLine="601" startOffset="20" endLine="764" endOffset="34"/></Target><Target id="@+id/chipGroupEnergyLevel" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="618" startOffset="24" endLine="650" endOffset="68"/></Target><Target id="@+id/chipEnergyLow" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="625" startOffset="28" endLine="631" endOffset="73"/></Target><Target id="@+id/chipEnergyMedium" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="633" startOffset="28" endLine="640" endOffset="73"/></Target><Target id="@+id/chipEnergyHigh" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="642" startOffset="28" endLine="648" endOffset="73"/></Target><Target id="@+id/tvProgressValue" view="TextView"><Expressions/><location startLine="666" startOffset="28" endLine="672" endOffset="58"/></Target><Target id="@+id/sliderProgress" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="676" startOffset="24" endLine="686" endOffset="73"/></Target><Target id="@+id/taskLocationInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="689" startOffset="24" endLine="710" endOffset="79"/></Target><Target id="@+id/taskLocationEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="705" startOffset="28" endLine="709" endOffset="58"/></Target><Target id="@+id/chipGroupContext" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="722" startOffset="24" endLine="762" endOffset="68"/></Target><Target id="@+id/chipContextHome" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="729" startOffset="28" endLine="736" endOffset="73"/></Target><Target id="@+id/chipContextOffice" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="738" startOffset="28" endLine="744" endOffset="73"/></Target><Target id="@+id/chipContextOnline" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="746" startOffset="28" endLine="752" endOffset="73"/></Target><Target id="@+id/chipContextTravel" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="754" startOffset="28" endLine="760" endOffset="73"/></Target><Target id="@+id/tvSettingsHeader" view="TextView"><Expressions/><location startLine="782" startOffset="20" endLine="795" endOffset="69"/></Target><Target id="@+id/layoutSettings" view="LinearLayout"><Expressions/><location startLine="797" startOffset="20" endLine="1024" endOffset="34"/></Target><Target id="@+id/taskReminderInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="805" startOffset="24" endLine="826" endOffset="79"/></Target><Target id="@+id/taskReminderAutoCompleteTextView" view="com.google.android.material.textfield.MaterialAutoCompleteTextView"><Expressions/><location startLine="819" startOffset="28" endLine="825" endOffset="65"/></Target><Target id="@+id/switchRecurring" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="836" startOffset="28" endLine="840" endOffset="65"/></Target><Target id="@+id/switchNotifications" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="860" startOffset="28" endLine="865" endOffset="56"/></Target><Target id="@+id/switchBreakDown" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="885" startOffset="28" endLine="889" endOffset="65"/></Target><Target id="@+id/colorRed" view="View"><Expressions/><location startLine="918" startOffset="28" endLine="925" endOffset="58"/></Target><Target id="@+id/colorBlue" view="View"><Expressions/><location startLine="927" startOffset="28" endLine="934" endOffset="58"/></Target><Target id="@+id/colorGreen" view="View"><Expressions/><location startLine="936" startOffset="28" endLine="943" endOffset="58"/></Target><Target id="@+id/colorOrange" view="View"><Expressions/><location startLine="945" startOffset="28" endLine="952" endOffset="58"/></Target><Target id="@+id/colorPurple" view="View"><Expressions/><location startLine="954" startOffset="28" endLine="960" endOffset="58"/></Target><Target id="@+id/chipGroupTaskIcon" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="974" startOffset="24" endLine="1022" endOffset="68"/></Target><Target id="@+id/chipIconWork" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="981" startOffset="28" endLine="988" endOffset="73"/></Target><Target id="@+id/chipIconStudy" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="990" startOffset="28" endLine="996" endOffset="73"/></Target><Target id="@+id/chipIconSport" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="998" startOffset="28" endLine="1004" endOffset="73"/></Target><Target id="@+id/chipIconHealth" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="1006" startOffset="28" endLine="1012" endOffset="73"/></Target><Target id="@+id/chipIconIdea" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="1014" startOffset="28" endLine="1020" endOffset="73"/></Target><Target id="@+id/btnSaveDraft" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="1037" startOffset="16" endLine="1047" endOffset="44"/></Target><Target id="@+id/btnAddAnother" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="1050" startOffset="16" endLine="1060" endOffset="44"/></Target><Target id="@+id/addTaskButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="1065" startOffset="12" endLine="1081" endOffset="45"/></Target></Targets></Layout>