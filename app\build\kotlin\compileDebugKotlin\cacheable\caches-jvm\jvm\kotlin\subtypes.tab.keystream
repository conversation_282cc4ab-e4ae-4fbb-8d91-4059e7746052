9com.example.kpitrackerapp.ui.OnUserSummaryActionsListenerandroidx.fragment.app.Fragment3com.example.kpitrackerapp.models.AdminDashboardItem*com.example.kpitrackerapp.models.ChatEvent1androidx.recyclerview.widget.RecyclerView.Adapter(androidx.appcompat.app.AppCompatActivity(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackkotlin.Enumandroidx.lifecycle.ViewModel#androidx.lifecycle.AndroidViewModel,androidx.lifecycle.ViewModelProvider.Factoryandroidx.work.CoroutineWorker androidx.viewbinding.ViewBindingandroid.app.Application$androidx.work.Configuration.Providerandroid.os.Parcelableandroidx.room.RoomDatabase6com.google.firebase.messaging.FirebaseMessagingService$androidx.fragment.app.DialogFragment2com.github.mikephil.charting.components.MarkerView5com.github.mikephil.charting.formatter.ValueFormatter8com.example.kpitrackerapp.ui.EnhancedTaskActionsListenerandroid.app.Dialog1com.example.kpitrackerapp.ui.OnKpiActionsListener!android.view.View.OnTouchListener;androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    