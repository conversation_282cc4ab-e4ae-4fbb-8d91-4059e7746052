// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAccountBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button buttonLogout;

  @NonNull
  public final LinearLayout cardProfile;

  @NonNull
  public final TextView textUserEmail;

  @NonNull
  public final TextView textUserName;

  @NonNull
  public final TextView textUserRole;

  private FragmentAccountBinding(@NonNull ScrollView rootView, @NonNull Button buttonLogout,
      @NonNull LinearLayout cardProfile, @NonNull TextView textUserEmail,
      @NonNull TextView textUserName, @NonNull TextView textUserRole) {
    this.rootView = rootView;
    this.buttonLogout = buttonLogout;
    this.cardProfile = cardProfile;
    this.textUserEmail = textUserEmail;
    this.textUserName = textUserName;
    this.textUserRole = textUserRole;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAccountBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAccountBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_account, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAccountBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonLogout;
      Button buttonLogout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLogout == null) {
        break missingId;
      }

      id = R.id.cardProfile;
      LinearLayout cardProfile = ViewBindings.findChildViewById(rootView, id);
      if (cardProfile == null) {
        break missingId;
      }

      id = R.id.textUserEmail;
      TextView textUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (textUserEmail == null) {
        break missingId;
      }

      id = R.id.textUserName;
      TextView textUserName = ViewBindings.findChildViewById(rootView, id);
      if (textUserName == null) {
        break missingId;
      }

      id = R.id.textUserRole;
      TextView textUserRole = ViewBindings.findChildViewById(rootView, id);
      if (textUserRole == null) {
        break missingId;
      }

      return new FragmentAccountBinding((ScrollView) rootView, buttonLogout, cardProfile,
          textUserEmail, textUserName, textUserRole);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
