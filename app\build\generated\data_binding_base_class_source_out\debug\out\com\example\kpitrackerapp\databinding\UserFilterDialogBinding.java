// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UserFilterDialogBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton applyButton;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final RadioButton radioAllUsers;

  @NonNull
  public final RadioButton radioSelectUsers;

  @NonNull
  public final RecyclerView userCheckboxRecyclerView;

  @NonNull
  public final RadioGroup userFilterRadioGroup;

  private UserFilterDialogBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton applyButton, @NonNull MaterialButton cancelButton,
      @NonNull TextView dialogTitle, @NonNull RadioButton radioAllUsers,
      @NonNull RadioButton radioSelectUsers, @NonNull RecyclerView userCheckboxRecyclerView,
      @NonNull RadioGroup userFilterRadioGroup) {
    this.rootView = rootView;
    this.applyButton = applyButton;
    this.cancelButton = cancelButton;
    this.dialogTitle = dialogTitle;
    this.radioAllUsers = radioAllUsers;
    this.radioSelectUsers = radioSelectUsers;
    this.userCheckboxRecyclerView = userCheckboxRecyclerView;
    this.userFilterRadioGroup = userFilterRadioGroup;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static UserFilterDialogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UserFilterDialogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.user_filter_dialog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UserFilterDialogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.applyButton;
      MaterialButton applyButton = ViewBindings.findChildViewById(rootView, id);
      if (applyButton == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.radioAllUsers;
      RadioButton radioAllUsers = ViewBindings.findChildViewById(rootView, id);
      if (radioAllUsers == null) {
        break missingId;
      }

      id = R.id.radioSelectUsers;
      RadioButton radioSelectUsers = ViewBindings.findChildViewById(rootView, id);
      if (radioSelectUsers == null) {
        break missingId;
      }

      id = R.id.userCheckboxRecyclerView;
      RecyclerView userCheckboxRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (userCheckboxRecyclerView == null) {
        break missingId;
      }

      id = R.id.userFilterRadioGroup;
      RadioGroup userFilterRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (userFilterRadioGroup == null) {
        break missingId;
      }

      return new UserFilterDialogBinding((ConstraintLayout) rootView, applyButton, cancelButton,
          dialogTitle, radioAllUsers, radioSelectUsers, userCheckboxRecyclerView,
          userFilterRadioGroup);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
