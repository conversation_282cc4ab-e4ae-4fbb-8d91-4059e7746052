// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class KpiListItemBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final CircularProgressIndicator kpiCircularProgressIndicator;

  @NonNull
  public final TextView kpiCurrentLabelTextView;

  @NonNull
  public final TextView kpiCurrentValueTextView;

  @NonNull
  public final TextView kpiNameTextView;

  @NonNull
  public final TextView kpiOwnerTextView;

  @NonNull
  public final TextView kpiProgressPercentageTextView;

  @NonNull
  public final TextView kpiTargetLabelTextView;

  @NonNull
  public final TextView kpiTargetValueTextView;

  @NonNull
  public final FrameLayout progressCircularLayout;

  private KpiListItemBinding(@NonNull MaterialCardView rootView,
      @NonNull CircularProgressIndicator kpiCircularProgressIndicator,
      @NonNull TextView kpiCurrentLabelTextView, @NonNull TextView kpiCurrentValueTextView,
      @NonNull TextView kpiNameTextView, @NonNull TextView kpiOwnerTextView,
      @NonNull TextView kpiProgressPercentageTextView, @NonNull TextView kpiTargetLabelTextView,
      @NonNull TextView kpiTargetValueTextView, @NonNull FrameLayout progressCircularLayout) {
    this.rootView = rootView;
    this.kpiCircularProgressIndicator = kpiCircularProgressIndicator;
    this.kpiCurrentLabelTextView = kpiCurrentLabelTextView;
    this.kpiCurrentValueTextView = kpiCurrentValueTextView;
    this.kpiNameTextView = kpiNameTextView;
    this.kpiOwnerTextView = kpiOwnerTextView;
    this.kpiProgressPercentageTextView = kpiProgressPercentageTextView;
    this.kpiTargetLabelTextView = kpiTargetLabelTextView;
    this.kpiTargetValueTextView = kpiTargetValueTextView;
    this.progressCircularLayout = progressCircularLayout;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static KpiListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static KpiListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.kpi_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static KpiListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.kpiCircularProgressIndicator;
      CircularProgressIndicator kpiCircularProgressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (kpiCircularProgressIndicator == null) {
        break missingId;
      }

      id = R.id.kpiCurrentLabelTextView;
      TextView kpiCurrentLabelTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiCurrentLabelTextView == null) {
        break missingId;
      }

      id = R.id.kpiCurrentValueTextView;
      TextView kpiCurrentValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiCurrentValueTextView == null) {
        break missingId;
      }

      id = R.id.kpiNameTextView;
      TextView kpiNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiNameTextView == null) {
        break missingId;
      }

      id = R.id.kpiOwnerTextView;
      TextView kpiOwnerTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiOwnerTextView == null) {
        break missingId;
      }

      id = R.id.kpiProgressPercentageTextView;
      TextView kpiProgressPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiProgressPercentageTextView == null) {
        break missingId;
      }

      id = R.id.kpiTargetLabelTextView;
      TextView kpiTargetLabelTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiTargetLabelTextView == null) {
        break missingId;
      }

      id = R.id.kpiTargetValueTextView;
      TextView kpiTargetValueTextView = ViewBindings.findChildViewById(rootView, id);
      if (kpiTargetValueTextView == null) {
        break missingId;
      }

      id = R.id.progress_circular_layout;
      FrameLayout progressCircularLayout = ViewBindings.findChildViewById(rootView, id);
      if (progressCircularLayout == null) {
        break missingId;
      }

      return new KpiListItemBinding((MaterialCardView) rootView, kpiCircularProgressIndicator,
          kpiCurrentLabelTextView, kpiCurrentValueTextView, kpiNameTextView, kpiOwnerTextView,
          kpiProgressPercentageTextView, kpiTargetLabelTextView, kpiTargetValueTextView,
          progressCircularLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
