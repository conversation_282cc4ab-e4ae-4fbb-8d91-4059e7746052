// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CheckBox adminModeCheckBox;

  @NonNull
  public final AutoCompleteTextView adminRoleAutoComplete;

  @NonNull
  public final TextInputLayout adminRoleInputLayout;

  @NonNull
  public final ImageView appLogoImageView;

  @NonNull
  public final TextView appTitleTextView;

  @NonNull
  public final MaterialButton createUserButton;

  @NonNull
  public final ProgressBar loadingProgressBar;

  @NonNull
  public final MaterialButton loginButton;

  @NonNull
  public final MaterialCardView loginFormCard;

  @NonNull
  public final TextView orTextView;

  @NonNull
  public final TextView quickAccessTextView;

  @NonNull
  public final RecyclerView recentUsersRecyclerView;

  @NonNull
  public final CheckBox rememberMeCheckBox;

  @NonNull
  public final Spinner userSelectionSpinner;

  @NonNull
  public final TextInputEditText usernameEditText;

  @NonNull
  public final TextInputLayout usernameInputLayout;

  @NonNull
  public final TextView welcomeTextView;

  private ActivityLoginBinding(@NonNull ConstraintLayout rootView,
      @NonNull CheckBox adminModeCheckBox, @NonNull AutoCompleteTextView adminRoleAutoComplete,
      @NonNull TextInputLayout adminRoleInputLayout, @NonNull ImageView appLogoImageView,
      @NonNull TextView appTitleTextView, @NonNull MaterialButton createUserButton,
      @NonNull ProgressBar loadingProgressBar, @NonNull MaterialButton loginButton,
      @NonNull MaterialCardView loginFormCard, @NonNull TextView orTextView,
      @NonNull TextView quickAccessTextView, @NonNull RecyclerView recentUsersRecyclerView,
      @NonNull CheckBox rememberMeCheckBox, @NonNull Spinner userSelectionSpinner,
      @NonNull TextInputEditText usernameEditText, @NonNull TextInputLayout usernameInputLayout,
      @NonNull TextView welcomeTextView) {
    this.rootView = rootView;
    this.adminModeCheckBox = adminModeCheckBox;
    this.adminRoleAutoComplete = adminRoleAutoComplete;
    this.adminRoleInputLayout = adminRoleInputLayout;
    this.appLogoImageView = appLogoImageView;
    this.appTitleTextView = appTitleTextView;
    this.createUserButton = createUserButton;
    this.loadingProgressBar = loadingProgressBar;
    this.loginButton = loginButton;
    this.loginFormCard = loginFormCard;
    this.orTextView = orTextView;
    this.quickAccessTextView = quickAccessTextView;
    this.recentUsersRecyclerView = recentUsersRecyclerView;
    this.rememberMeCheckBox = rememberMeCheckBox;
    this.userSelectionSpinner = userSelectionSpinner;
    this.usernameEditText = usernameEditText;
    this.usernameInputLayout = usernameInputLayout;
    this.welcomeTextView = welcomeTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adminModeCheckBox;
      CheckBox adminModeCheckBox = ViewBindings.findChildViewById(rootView, id);
      if (adminModeCheckBox == null) {
        break missingId;
      }

      id = R.id.adminRoleAutoComplete;
      AutoCompleteTextView adminRoleAutoComplete = ViewBindings.findChildViewById(rootView, id);
      if (adminRoleAutoComplete == null) {
        break missingId;
      }

      id = R.id.adminRoleInputLayout;
      TextInputLayout adminRoleInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (adminRoleInputLayout == null) {
        break missingId;
      }

      id = R.id.appLogoImageView;
      ImageView appLogoImageView = ViewBindings.findChildViewById(rootView, id);
      if (appLogoImageView == null) {
        break missingId;
      }

      id = R.id.appTitleTextView;
      TextView appTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (appTitleTextView == null) {
        break missingId;
      }

      id = R.id.createUserButton;
      MaterialButton createUserButton = ViewBindings.findChildViewById(rootView, id);
      if (createUserButton == null) {
        break missingId;
      }

      id = R.id.loadingProgressBar;
      ProgressBar loadingProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgressBar == null) {
        break missingId;
      }

      id = R.id.loginButton;
      MaterialButton loginButton = ViewBindings.findChildViewById(rootView, id);
      if (loginButton == null) {
        break missingId;
      }

      id = R.id.loginFormCard;
      MaterialCardView loginFormCard = ViewBindings.findChildViewById(rootView, id);
      if (loginFormCard == null) {
        break missingId;
      }

      id = R.id.orTextView;
      TextView orTextView = ViewBindings.findChildViewById(rootView, id);
      if (orTextView == null) {
        break missingId;
      }

      id = R.id.quickAccessTextView;
      TextView quickAccessTextView = ViewBindings.findChildViewById(rootView, id);
      if (quickAccessTextView == null) {
        break missingId;
      }

      id = R.id.recentUsersRecyclerView;
      RecyclerView recentUsersRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recentUsersRecyclerView == null) {
        break missingId;
      }

      id = R.id.rememberMeCheckBox;
      CheckBox rememberMeCheckBox = ViewBindings.findChildViewById(rootView, id);
      if (rememberMeCheckBox == null) {
        break missingId;
      }

      id = R.id.userSelectionSpinner;
      Spinner userSelectionSpinner = ViewBindings.findChildViewById(rootView, id);
      if (userSelectionSpinner == null) {
        break missingId;
      }

      id = R.id.usernameEditText;
      TextInputEditText usernameEditText = ViewBindings.findChildViewById(rootView, id);
      if (usernameEditText == null) {
        break missingId;
      }

      id = R.id.usernameInputLayout;
      TextInputLayout usernameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (usernameInputLayout == null) {
        break missingId;
      }

      id = R.id.welcomeTextView;
      TextView welcomeTextView = ViewBindings.findChildViewById(rootView, id);
      if (welcomeTextView == null) {
        break missingId;
      }

      return new ActivityLoginBinding((ConstraintLayout) rootView, adminModeCheckBox,
          adminRoleAutoComplete, adminRoleInputLayout, appLogoImageView, appTitleTextView,
          createUserButton, loadingProgressBar, loginButton, loginFormCard, orTextView,
          quickAccessTextView, recentUsersRecyclerView, rememberMeCheckBox, userSelectionSpinner,
          usernameEditText, usernameInputLayout, welcomeTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
