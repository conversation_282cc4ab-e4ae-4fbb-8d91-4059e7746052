// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TableRow;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UnifiedReportTableRowBinding implements ViewBinding {
  @NonNull
  private final TableRow rootView;

  @NonNull
  public final TextView achievedTextView;

  @NonNull
  public final TextView doctorNameTextView;

  @NonNull
  public final TextView percentageTextView;

  @NonNull
  public final TextView periodTextView;

  @NonNull
  public final TextView targetTextView;

  private UnifiedReportTableRowBinding(@NonNull TableRow rootView,
      @NonNull TextView achievedTextView, @NonNull TextView doctorNameTextView,
      @NonNull TextView percentageTextView, @NonNull TextView periodTextView,
      @NonNull TextView targetTextView) {
    this.rootView = rootView;
    this.achievedTextView = achievedTextView;
    this.doctorNameTextView = doctorNameTextView;
    this.percentageTextView = percentageTextView;
    this.periodTextView = periodTextView;
    this.targetTextView = targetTextView;
  }

  @Override
  @NonNull
  public TableRow getRoot() {
    return rootView;
  }

  @NonNull
  public static UnifiedReportTableRowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UnifiedReportTableRowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.unified_report_table_row, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UnifiedReportTableRowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.achievedTextView;
      TextView achievedTextView = ViewBindings.findChildViewById(rootView, id);
      if (achievedTextView == null) {
        break missingId;
      }

      id = R.id.doctorNameTextView;
      TextView doctorNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (doctorNameTextView == null) {
        break missingId;
      }

      id = R.id.percentageTextView;
      TextView percentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (percentageTextView == null) {
        break missingId;
      }

      id = R.id.periodTextView;
      TextView periodTextView = ViewBindings.findChildViewById(rootView, id);
      if (periodTextView == null) {
        break missingId;
      }

      id = R.id.targetTextView;
      TextView targetTextView = ViewBindings.findChildViewById(rootView, id);
      if (targetTextView == null) {
        break missingId;
      }

      return new UnifiedReportTableRowBinding((TableRow) rootView, achievedTextView,
          doctorNameTextView, percentageTextView, periodTextView, targetTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
