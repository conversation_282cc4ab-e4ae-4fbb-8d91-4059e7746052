<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_task_management" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_task_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_task_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="634" endOffset="53"/></Target><Target id="@+id/appBarLayoutTaskManagement" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="135" endOffset="53"/></Target><Target id="@+id/toolbarTaskManagement" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="16" startOffset="8" endLine="60" endOffset="60"/></Target><Target id="@+id/btnSearch" view="ImageButton"><Expressions/><location startLine="31" startOffset="16" endLine="38" endOffset="45"/></Target><Target id="@+id/btnFilter" view="ImageButton"><Expressions/><location startLine="40" startOffset="16" endLine="47" endOffset="45"/></Target><Target id="@+id/btnViewMode" view="ImageButton"><Expressions/><location startLine="49" startOffset="16" endLine="56" endOffset="45"/></Target><Target id="@+id/searchView" view="androidx.appcompat.widget.SearchView"><Expressions/><location startLine="63" startOffset="8" endLine="71" endOffset="44"/></Target><Target id="@+id/scrollViewFilters" view="HorizontalScrollView"><Expressions/><location startLine="74" startOffset="8" endLine="133" endOffset="30"/></Target><Target id="@+id/chipGroupFilters" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="82" startOffset="12" endLine="132" endOffset="56"/></Target><Target id="@+id/chipAll" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="89" startOffset="16" endLine="95" endOffset="44"/></Target><Target id="@+id/chipToday" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="97" startOffset="16" endLine="102" endOffset="42"/></Target><Target id="@+id/chipThisWeek" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="104" startOffset="16" endLine="109" endOffset="48"/></Target><Target id="@+id/chipUrgent" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="111" startOffset="16" endLine="116" endOffset="41"/></Target><Target id="@+id/chipCompleted" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="118" startOffset="16" endLine="123" endOffset="43"/></Target><Target id="@+id/chipOverdue" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="125" startOffset="16" endLine="130" endOffset="43"/></Target><Target id="@+id/cardQuickStats" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="150" startOffset="12" endLine="306" endOffset="63"/></Target><Target id="@+id/tvTotalTasks" view="TextView"><Expressions/><location startLine="188" startOffset="28" endLine="195" endOffset="74"/></Target><Target id="@+id/tvTodayTasks" view="TextView"><Expressions/><location startLine="216" startOffset="28" endLine="223" endOffset="72"/></Target><Target id="@+id/tvUrgentTasks" view="TextView"><Expressions/><location startLine="244" startOffset="28" endLine="251" endOffset="73"/></Target><Target id="@+id/tvCompletionRate" view="TextView"><Expressions/><location startLine="272" startOffset="28" endLine="279" endOffset="74"/></Target><Target id="@+id/progressOverall" view="ProgressBar"><Expressions/><location startLine="294" startOffset="20" endLine="302" endOffset="85"/></Target><Target id="@+id/cardMatrixToggle" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="309" startOffset="12" endLine="341" endOffset="63"/></Target><Target id="@+id/switchMatrixView" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="333" startOffset="20" endLine="337" endOffset="49"/></Target><Target id="@+id/layoutEisenhowerMatrix" view="LinearLayout"><Expressions/><location startLine="344" startOffset="12" endLine="546" endOffset="26"/></Target><Target id="@+id/tvUrgentImportantCount" view="TextView"><Expressions/><location startLine="385" startOffset="28" endLine="393" endOffset="64"/></Target><Target id="@+id/rvUrgentImportant" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="395" startOffset="28" endLine="400" endOffset="64"/></Target><Target id="@+id/tvNotUrgentImportantCount" view="TextView"><Expressions/><location startLine="431" startOffset="28" endLine="439" endOffset="64"/></Target><Target id="@+id/rvNotUrgentImportant" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="441" startOffset="28" endLine="446" endOffset="64"/></Target><Target id="@+id/tvUrgentNotImportantCount" view="TextView"><Expressions/><location startLine="477" startOffset="28" endLine="485" endOffset="64"/></Target><Target id="@+id/rvUrgentNotImportant" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="487" startOffset="28" endLine="492" endOffset="64"/></Target><Target id="@+id/tvNotUrgentNotImportantCount" view="TextView"><Expressions/><location startLine="523" startOffset="28" endLine="531" endOffset="64"/></Target><Target id="@+id/rvNotUrgentNotImportant" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="533" startOffset="28" endLine="538" endOffset="64"/></Target><Target id="@+id/btnSortTasks" view="ImageButton"><Expressions/><location startLine="564" startOffset="16" endLine="570" endOffset="61"/></Target><Target id="@+id/rvTasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="575" startOffset="12" endLine="580" endOffset="61"/></Target><Target id="@+id/layoutEmptyState" view="LinearLayout"><Expressions/><location startLine="583" startOffset="12" endLine="613" endOffset="26"/></Target><Target id="@+id/fabAddTask" view="com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton"><Expressions/><location startLine="620" startOffset="4" endLine="632" endOffset="75"/></Target></Targets></Layout>