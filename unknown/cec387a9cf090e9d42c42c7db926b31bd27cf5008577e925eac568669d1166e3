<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5"
    tools:context=".fragments.DrugIndexFragment">

    <!-- Removed AppBarLayout and Toolbar as per design feedback -->

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <EditText
                android:id="@+id/searchEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Search for drug or active ingredient..."
                android:drawableStart="@android:drawable/ic_menu_search"
                android:drawablePadding="8dp"
                android:layout_marginBottom="8dp" />

            <AutoCompleteTextView
                android:id="@+id/drugAutoComplete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:hint="اختر الدواء من القائمة..."
                android:padding="12dp"
                android:background="@drawable/rounded_background"
                android:textSize="16sp"
                android:dropDownHeight="300dp"
                android:dropDownVerticalOffset="4dp"
                android:completionThreshold="1"
                android:visibility="gone" />

            <TextView
                android:id="@+id/drugNameTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Drug Name/Active Ingredient"
                android:textSize="20sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                android:layout_marginTop="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💊 الاستخدامات"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/usesTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="-" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                android:layout_marginTop="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⚠️ التفاعلات الدوائية"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/interactionsTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="-" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                android:layout_marginTop="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="❗ الأعراض الجانبية"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/sideEffectsTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="-" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                android:layout_marginTop="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🍽️ طريقة الاستعمال"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/usageInstructionsTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="-" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- خانة السعر -->
            <androidx.cardview.widget.CardView
                android:id="@+id/priceCardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="6dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="#E8F5E8"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💰 السعر"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="#2E7D32" />

                    <TextView
                        android:id="@+id/selectedDrugNameTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="اسم الدواء المختار"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:textColor="#1B5E20" />

                    <TextView
                        android:id="@+id/selectedDrugDetailsTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="التفاصيل"
                        android:textSize="14sp"
                        android:gravity="center"
                        android:textColor="#388E3C" />

                    <TextView
                        android:id="@+id/priceTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="0.00 ريال"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:textColor="#1B5E20"
                        android:background="@drawable/rounded_background"
                        android:padding="12dp" />

                    <TextView
                        android:id="@+id/manufacturerTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="الشركة المصنعة"
                        android:textSize="12sp"
                        android:gravity="center"
                        android:textColor="#4CAF50" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>
</LinearLayout>
