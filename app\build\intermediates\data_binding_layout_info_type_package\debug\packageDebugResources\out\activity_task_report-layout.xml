<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_task_report" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_task_report.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_task_report_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="84" endOffset="53"/></Target><Target id="@+id/appBarLayoutTaskReport" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="21" endOffset="53"/></Target><Target id="@+id/toolbarTaskReport" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="20" endOffset="38"/></Target><Target id="@+id/tvTasksDueSoonTitle" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="39" endOffset="72"/></Target><Target id="@+id/rvTasksDueSoon" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="41" startOffset="12" endLine="48" endOffset="36"/></Target><Target id="@+id/tvAllTasksTitle" view="TextView"><Expressions/><location startLine="57" startOffset="12" endLine="63" endOffset="48"/></Target><Target id="@+id/rvAllTasksReport" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="65" startOffset="12" endLine="72" endOffset="36"/></Target><Target id="@+id/btnSaveTaskReport" view="Button"><Expressions/><location startLine="74" startOffset="12" endLine="79" endOffset="52"/></Target></Targets></Layout>