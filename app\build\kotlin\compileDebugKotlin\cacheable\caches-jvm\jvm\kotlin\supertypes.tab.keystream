0com.example.kpitrackerapp.AdminDashboardActivity/com.example.kpitrackerapp.KpiTrackerApplication&com.example.kpitrackerapp.MainActivity8com.example.kpitrackerapp.adapters.AdminDashboardAdapterIcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.HeaderViewHolderKcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.StatCardViewHolderMcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActionCardViewHolderOcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActivityCardViewHolderKcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.UserCardViewHolder5com.example.kpitrackerapp.adapters.ChatMessageAdapterJcom.example.kpitrackerapp.adapters.ChatMessageAdapter.DateHeaderViewHolderKcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SentMessageViewHolderOcom.example.kpitrackerapp.adapters.ChatMessageAdapter.ReceivedMessageViewHolderMcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SystemMessageViewHolder:com.example.kpitrackerapp.adapters.ChatMessageDiffCallback6com.example.kpitrackerapp.adapters.ConversationAdapterMcom.example.kpitrackerapp.adapters.ConversationAdapter.ConversationViewHolder;com.example.kpitrackerapp.adapters.ConversationDiffCallback6com.example.kpitrackerapp.adapters.NotificationAdapterMcom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationViewHolderOcom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationDiffCallback5com.example.kpitrackerapp.adapters.ProductListAdapterGcom.example.kpitrackerapp.adapters.ProductListAdapter.ProductViewHolderIcom.example.kpitrackerapp.adapters.ProductListAdapter.ProductDiffCallback7com.example.kpitrackerapp.adapters.SearchResultsAdapterNcom.example.kpitrackerapp.adapters.SearchResultsAdapter.SearchResultViewHolderPcom.example.kpitrackerapp.adapters.SearchResultsAdapter.SearchResultDiffCallback4com.example.kpitrackerapp.adapters.UserFilterAdapterCcom.example.kpitrackerapp.adapters.UserFilterAdapter.UserViewHolder2com.example.kpitrackerapp.adapters.UserListAdapterAcom.example.kpitrackerapp.adapters.UserListAdapter.UserViewHolder3com.example.kpitrackerapp.adapters.UserDiffCallback3com.example.kpitrackerapp.fragments.AccountFragment5com.example.kpitrackerapp.fragments.DashboardFragment5com.example.kpitrackerapp.fragments.DrugIndexFragment9com.example.kpitrackerapp.fragments.MainDashboardFragment4com.example.kpitrackerapp.fragments.MessagesFragment7com.example.kpitrackerapp.fragments.PerformanceFragment-com.example.kpitrackerapp.models.ActivityType:com.example.kpitrackerapp.models.AdminDashboardItem.Header<com.example.kpitrackerapp.models.AdminDashboardItem.StatCard><EMAIL><com.example.kpitrackerapp.models.AdminDashboardItem.UserCard,com.example.kpitrackerapp.models.MessageType/com.example.kpitrackerapp.models.AttachmentType5com.example.kpitrackerapp.models.ChatEvent.NewMessage6com.example.kpitrackerapp.models.ChatEvent.MessageRead;com.example.kpitrackerapp.models.ChatEvent.MessageDelivered5com.example.kpitrackerapp.models.ChatEvent.UserTyping<com.example.kpitrackerapp.models.ChatEvent.UserStoppedTyping5com.example.kpitrackerapp.models.ChatEvent.UserOnline6com.example.kpitrackerapp.models.ChatEvent.UserOffline(com.example.kpitrackerapp.models.KpiUnit.com.example.kpitrackerapp.models.OcrResultItem0com.example.kpitrackerapp.models.ProductCategory-com.example.kpitrackerapp.models.TaskPriority/com.example.kpitrackerapp.models.TaskImportance,com.example.kpitrackerapp.models.EnergyLevel-com.example.kpitrackerapp.models.TaskQuadrant)com.example.kpitrackerapp.models.UserRole1com.example.kpitrackerapp.persistence.AppDatabase>com.example.kpitrackerapp.services.KPIFirebaseMessagingService8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivityCcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.SpeechMode/com.example.kpitrackerapp.ui.AddEditKpiActivity7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity:com.example.kpitrackerapp.ui.AddEditProgressDialogFragment/com.example.kpitrackerapp.ui.AddProductActivity5com.example.kpitrackerapp.ui.AutoSendSettingsActivity,com.example.kpitrackerapp.ui.ChartMarkerView)com.example.kpitrackerapp.ui.ChatActivity-com.example.kpitrackerapp.ui.ChatListActivity1com.example.kpitrackerapp.ui.ColoredReportAdapterBcom.example.kpitrackerapp.ui.ColoredReportAdapter.ReportViewHolderDcom.example.kpitrackerapp.ui.ColoredReportAdapter.ReportDiffCallback1com.example.kpitrackerapp.ui.CompactReportAdapterIcom.example.kpitrackerapp.ui.CompactReportAdapter.CompactReportViewHolderDcom.example.kpitrackerapp.ui.CompactReportAdapter.ReportDiffCallback/com.example.kpitrackerapp.ui.CreateUserActivity2com.example.kpitrackerapp.ui.DateConverterActivity0com.example.kpitrackerapp.ui.EnhancedTaskAdapter?com.example.kpitrackerapp.ui.EnhancedTaskAdapter.TaskViewHolderAcom.example.kpitrackerapp.ui.EnhancedTaskAdapter.TaskDiffCallback0com.example.kpitrackerapp.ui.ExcelImportActivity0com.example.kpitrackerapp.ui.ExcelReviewActivity/com.example.kpitrackerapp.ui.ExcelReviewAdapterEcom.example.kpitrackerapp.ui.ExcelReviewAdapter.ExcelReviewViewHolderAcom.example.kpitrackerapp.ui.ExcelReviewAdapter.ExcelDiffCallback5com.example.kpitrackerapp.ui.ExpireManagementActivity3com.example.kpitrackerapp.ui.ExpiryTrackingActivity.com.example.kpitrackerapp.ui.KpiDetailActivityEcom.example.kpitrackerapp.ui.KpiDetailActivity.DateAxisValueFormatter+com.example.kpitrackerapp.ui.KpiListAdapter9com.example.kpitrackerapp.ui.KpiListAdapter.KpiViewHolderGcom.example.kpitrackerapp.ui.KpiListAdapter.KpiWithProgressDiffCallback4com.example.kpitrackerapp.ui.KpiProgressEntryAdapterLcom.example.kpitrackerapp.ui.KpiProgressEntryAdapter.ProgressEntryViewHolderNcom.example.kpitrackerapp.ui.KpiProgressEntryAdapter.ProgressEntryDiffCallback*com.example.kpitrackerapp.ui.LoginActivity2com.example.kpitrackerapp.ui.ModernAddTaskActivity1com.example.kpitrackerapp.ui.ModernReportActivity=com.example.kpitrackerapp.ui.ModernReportActivity.ChartPeriod2com.example.kpitrackerapp.ui.NotificationsActivity(com.example.kpitrackerapp.ui.OcrActivity.com.example.kpitrackerapp.ui.OcrReviewActivity-com.example.kpitrackerapp.ui.OcrReviewAdapter8com.example.kpitrackerapp.ui.OcrReviewAdapter.ViewHolder2com.example.kpitrackerapp.ui.PomodoroTimerActivity0com.example.kpitrackerapp.ui.ProductListActivity/com.example.kpitrackerapp.ui.RecentUsersAdapterDcom.example.kpitrackerapp.ui.RecentUsersAdapter.RecentUserViewHolderFcom.example.kpitrackerapp.ui.RecentUsersAdapter.RecentUserDiffCallback+com.example.kpitrackerapp.ui.ReportActivity7com.example.kpitrackerapp.ui.ReportActivity.ChartPeriod*com.example.kpitrackerapp.ui.ReportAdapter;com.example.kpitrackerapp.ui.ReportAdapter.ReportViewHolder=com.example.kpitrackerapp.ui.ReportAdapter.ReportDiffCallback7com.example.kpitrackerapp.ui.SearchEditProgressActivity(com.example.kpitrackerapp.ui.TaskAdapter7com.example.kpitrackerapp.ui.TaskAdapter.TaskViewHolder-com.example.kpitrackerapp.ui.TaskDiffCallback3com.example.kpitrackerapp.ui.TaskManagementActivity9com.example.kpitrackerapp.ui.TaskReminderSettingsActivity/com.example.kpitrackerapp.ui.TaskReportActivity.com.example.kpitrackerapp.ui.TaskReportAdapterCcom.example.kpitrackerapp.ui.TaskReportAdapter.TaskReportViewHolder1com.example.kpitrackerapp.ui.UnifiedReportAdapterEcom.example.kpitrackerapp.ui.UnifiedReportAdapter.ReportRowViewHolderGcom.example.kpitrackerapp.ui.UnifiedReportAdapter.ReportRowDiffCallback-com.example.kpitrackerapp.ui.UserFilterDialog0com.example.kpitrackerapp.ui.UserKpiListActivity/com.example.kpitrackerapp.ui.UserSummaryAdapterEcom.example.kpitrackerapp.ui.UserSummaryAdapter.UserSummaryViewHolderGcom.example.kpitrackerapp.ui.UserSummaryAdapter.UserSummaryDiffCallbackBcom.example.kpitrackerapp.utils.CardAnimationHelper.SwipeDirection1com.example.kpitrackerapp.utils.CardGestureHelper.com.example.kpitrackerapp.utils.DragDropHelper2com.example.kpitrackerapp.utils.EisenhowerQuadrant2com.example.kpitrackerapp.viewmodels.ChatViewModel9com.example.kpitrackerapp.viewmodels.ChatViewModelFactory1com.example.kpitrackerapp.viewmodels.KpiViewModel8com.example.kpitrackerapp.viewmodels.KpiViewModelFactory5com.example.kpitrackerapp.viewmodels.ProductViewModel<com.example.kpitrackerapp.viewmodels.ProductViewModelFactory2com.example.kpitrackerapp.viewmodels.TaskViewModel9com.example.kpitrackerapp.viewmodels.TaskViewModelFactory8com.example.kpitrackerapp.workers.AutoReportSenderWorker:com.example.kpitrackerapp.workers.ExpiryNotificationWorker5com.example.kpitrackerapp.workers.RecurringTaskWorker4com.example.kpitrackerapp.workers.TaskReminderWorkerCcom.example.kpitrackerapp.databinding.DialogSelectCardColorsBinding=com.example.kpitrackerapp.databinding.ActivityChatListBinding>com.example.kpitrackerapp.databinding.FragmentDashboardBinding=com.example.kpitrackerapp.databinding.FragmentMessagesBindingBcom.example.kpitrackerapp.databinding.CompactReportTableRowBindingBcom.example.kpitrackerapp.databinding.ReportTableRowColoredBinding:com.example.kpitrackerapp.databinding.OcrReviewItemBinding;com.example.kpitrackerapp.databinding.ActivityReportBinding?com.example.kpitrackerapp.databinding.ActivityTaskReportBindingEcom.example.kpitrackerapp.databinding.ActivityExpireManagementBindingCcom.example.kpitrackerapp.databinding.OverallSummaryCardItemBinding>com.example.kpitrackerapp.databinding.ActivityOcrReviewBinding?com.example.kpitrackerapp.databinding.ActivityAddEditKpiBinding:com.example.kpitrackerapp.databinding.ActivityLoginBindingAcom.example.kpitrackerapp.databinding.ActivityModernReportBindingBcom.example.kpitrackerapp.databinding.FragmentMainDashboardBindingGcom.example.kpitrackerapp.databinding.ActivitySearchEditProgressBindingCcom.example.kpitrackerapp.databinding.ActivityTaskManagementBinding<com.example.kpitrackerapp.databinding.ExcelReviewItemBinding=com.example.kpitrackerapp.databinding.ItemTaskEnhancedBinding;com.example.kpitrackerapp.databinding.ItemRecentUserBinding=com.example.kpitrackerapp.databinding.ItemSearchResultBinding<<EMAIL>@com.example.kpitrackerapp.databinding.ActivityUserKpiListBinding><EMAIL>@com.example.kpitrackerapp.databinding.UserSummaryCardItemBinding?com.example.kpitrackerapp.databinding.ActivityAddProductBinding=com.example.kpitrackerapp.databinding.UserFilterDialogBindingAcom.example.kpitrackerapp.databinding.KpiSummaryDetailItemBindingBcom.example.kpitrackerapp.databinding.ActivityAddTaskModernBinding?com.example.kpitrackerapp.databinding.ActivityCreateUserBinding8com.example.kpitrackerapp.databinding.ItemProductBindingCcom.example.kpitrackerapp.databinding.ActivityExpiryTrackingBindingCcom.example.kpitrackerapp.databinding.ActivityAdminDashboardBindingIcom.example.kpitrackerapp.databinding.ActivityTaskReminderSettingsBindingBcom.example.kpitrackerapp.databinding.ActivityPomodoroTimerBindingGcom.example.kpitrackerapp.databinding.ActivityAddEditKpiOriginalBindingIcom.example.kpitrackerapp.databinding.UnifiedReportTableRowBindingBinding><EMAIL>@<EMAIL>                                                                                                                                                                                                                                   