<?xml version="1.0" encoding="utf-8"?>
<TableLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:stretchColumns="*">

    <TableRow
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <!-- Doctor Name -->
        <TextView
            android:id="@+id/doctorNameTextView"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="Dr<PERSON>" />

        <!-- Daily Values -->
        <TextView
            android:id="@+id/dailyTargetTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="145" />

        <TextView
            android:id="@+id/dailyAchievedTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="120" />

        <TextView
            android:id="@+id/dailyPercentageTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="83%" />

        <!-- Monthly Values -->
        <TextView
            android:id="@+id/monthlyTargetTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="3,000" />

        <TextView
            android:id="@+id/monthlyAchievedTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="2,500" />

        <TextView
            android:id="@+id/monthlyPercentageTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="83%" />

        <!-- Annual Values -->
        <TextView
            android:id="@+id/annualTargetTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="36,000" />

        <TextView
            android:id="@+id/annualAchievedTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="12,000" />

        <TextView
            android:id="@+id/annualPercentageTextView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="14sp"
            tools:text="33%" />
    </TableRow>
</TableLayout>
