// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ExcelReviewItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView tvExcelReviewDate;

  @NonNull
  public final TextView tvExcelReviewUser;

  @NonNull
  public final TextView tvExcelReviewValue;

  private ExcelReviewItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView tvExcelReviewDate, @NonNull TextView tvExcelReviewUser,
      @NonNull TextView tvExcelReviewValue) {
    this.rootView = rootView;
    this.tvExcelReviewDate = tvExcelReviewDate;
    this.tvExcelReviewUser = tvExcelReviewUser;
    this.tvExcelReviewValue = tvExcelReviewValue;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ExcelReviewItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExcelReviewItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.excel_review_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExcelReviewItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tvExcelReviewDate;
      TextView tvExcelReviewDate = ViewBindings.findChildViewById(rootView, id);
      if (tvExcelReviewDate == null) {
        break missingId;
      }

      id = R.id.tvExcelReviewUser;
      TextView tvExcelReviewUser = ViewBindings.findChildViewById(rootView, id);
      if (tvExcelReviewUser == null) {
        break missingId;
      }

      id = R.id.tvExcelReviewValue;
      TextView tvExcelReviewValue = ViewBindings.findChildViewById(rootView, id);
      if (tvExcelReviewValue == null) {
        break missingId;
      }

      return new ExcelReviewItemBinding((ConstraintLayout) rootView, tvExcelReviewDate,
          tvExcelReviewUser, tvExcelReviewValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
