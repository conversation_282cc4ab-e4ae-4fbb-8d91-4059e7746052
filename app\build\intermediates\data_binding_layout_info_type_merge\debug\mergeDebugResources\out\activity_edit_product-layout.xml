<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_edit_product" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_edit_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_edit_product_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="216" endOffset="12"/></Target><Target id="@+id/productNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="14" startOffset="8" endLine="32" endOffset="63"/></Target><Target id="@+id/productNameInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="25" startOffset="12" endLine="30" endOffset="38"/></Target><Target id="@+id/barcodeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="35" startOffset="8" endLine="52" endOffset="63"/></Target><Target id="@+id/barcodeInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="46" startOffset="12" endLine="50" endOffset="42"/></Target><Target id="@+id/categorySpinner" view="AutoCompleteTextView"><Expressions/><location startLine="63" startOffset="12" endLine="68" endOffset="43"/></Target><Target id="@+id/expiryDateInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="81" startOffset="12" endLine="87" endOffset="42"/></Target><Target id="@+id/quantityInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="108" startOffset="16" endLine="112" endOffset="48"/></Target><Target id="@+id/locationInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="126" startOffset="16" endLine="130" endOffset="46"/></Target><Target id="@+id/notesInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="145" startOffset="12" endLine="151" endOffset="38"/></Target><Target id="@+id/deleteButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="164" startOffset="12" endLine="179" endOffset="51"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="182" startOffset="12" endLine="195" endOffset="80"/></Target><Target id="@+id/updateProductButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="198" startOffset="12" endLine="210" endOffset="63"/></Target></Targets></Layout>