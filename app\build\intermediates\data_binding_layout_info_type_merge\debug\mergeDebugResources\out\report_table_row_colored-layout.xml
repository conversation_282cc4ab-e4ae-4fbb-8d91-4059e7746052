<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="report_table_row_colored" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\report_table_row_colored.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/report_table_row_colored_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="227" endOffset="51"/></Target><Target id="@+id/reportUserNameTextView" view="TextView"><Expressions/><location startLine="19" startOffset="8" endLine="28" endOffset="36"/></Target><Target id="@+id/reportDailyRow" view="TableRow"><Expressions/><location startLine="80" startOffset="12" endLine="113" endOffset="22"/></Target><Target id="@+id/reportDailyTarget" view="TextView"><Expressions/><location startLine="89" startOffset="16" endLine="96" endOffset="37"/></Target><Target id="@+id/reportDailyAchieved" view="TextView"><Expressions/><location startLine="97" startOffset="16" endLine="104" endOffset="35"/></Target><Target id="@+id/reportDailyPercentage" view="TextView"><Expressions/><location startLine="105" startOffset="16" endLine="112" endOffset="36"/></Target><Target id="@+id/reportMonthlyRow" view="TableRow"><Expressions/><location startLine="116" startOffset="12" endLine="150" endOffset="22"/></Target><Target id="@+id/reportMonthlyTarget" view="TextView"><Expressions/><location startLine="126" startOffset="16" endLine="133" endOffset="39"/></Target><Target id="@+id/reportMonthlyAchieved" view="TextView"><Expressions/><location startLine="134" startOffset="16" endLine="141" endOffset="39"/></Target><Target id="@+id/reportMonthlyPercentage" view="TextView"><Expressions/><location startLine="142" startOffset="16" endLine="149" endOffset="37"/></Target><Target id="@+id/reportAnnualRow" view="TableRow"><Expressions/><location startLine="153" startOffset="12" endLine="186" endOffset="22"/></Target><Target id="@+id/reportAnnualTarget" view="TextView"><Expressions/><location startLine="162" startOffset="16" endLine="169" endOffset="40"/></Target><Target id="@+id/reportAnnualAchieved" view="TextView"><Expressions/><location startLine="170" startOffset="16" endLine="177" endOffset="39"/></Target><Target id="@+id/reportAnnualPercentage" view="TextView"><Expressions/><location startLine="178" startOffset="16" endLine="185" endOffset="36"/></Target><Target id="@+id/reportQuarterlyRow" view="TableRow"><Expressions/><location startLine="189" startOffset="12" endLine="224" endOffset="22"/></Target><Target id="@+id/reportQuarterlyTarget" view="TextView"><Expressions/><location startLine="200" startOffset="16" endLine="207" endOffset="39"/></Target><Target id="@+id/reportQuarterlyAchieved" view="TextView"><Expressions/><location startLine="208" startOffset="16" endLine="215" endOffset="39"/></Target><Target id="@+id/reportQuarterlyPercentage" view="TextView"><Expressions/><location startLine="216" startOffset="16" endLine="223" endOffset="37"/></Target></Targets></Layout>