<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.OcrActivity">

    <Button
        android:id="@+id/btnSelectImage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Select Image for OCR"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/ivSelectedImage"
        android:layout_width="0dp"
        android:layout_height="200dp"
        android:layout_marginTop="16dp"
        android:scaleType="centerInside"
        android:contentDescription="Selected image preview"
        app:layout_constraintTop_toBottomOf="@id/btnSelectImage"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:srcCompat="@tools:sample/backgrounds/scenic" /> <!-- Placeholder image -->

    <TextView
        android:id="@+id/tvOcrResultLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Extracted Text:"
        android:textAppearance="?attr/textAppearanceHeadline6"
        app:layout_constraintTop_toBottomOf="@id/ivSelectedImage"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btnCopyText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Copy Text"
        android:layout_marginStart="8dp"
        style="?attr/materialButtonOutlinedStyle"
        app:layout_constraintTop_toTopOf="@id/tvOcrResultLabel"
        app:layout_constraintBottom_toBottomOf="@id/tvOcrResultLabel"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone" /> <!-- Initially hidden -->

    <ScrollView
        android:id="@+id/scrollViewOcrResult"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/tvOcrResultLabel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tvOcrResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody1"
            android:textIsSelectable="true"
            tools:text="This is where the extracted text will appear after processing the image." />
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
