<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task_enhanced" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_task_enhanced.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_task_enhanced_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="311" endOffset="51"/></Target><Target id="@+id/tvTaskIcon" view="TextView"><Expressions/><location startLine="21" startOffset="8" endLine="29" endOffset="29"/></Target><Target id="@+id/tvTaskName" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="46" endOffset="43"/></Target><Target id="@+id/tvTaskDetails" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="63" endOffset="43"/></Target><Target id="@+id/progressTask" view="ProgressBar"><Expressions/><location startLine="66" startOffset="8" endLine="80" endOffset="33"/></Target><Target id="@+id/tvProgressPercent" view="TextView"><Expressions/><location startLine="83" startOffset="8" endLine="94" endOffset="36"/></Target><Target id="@+id/tvTimeRemaining" view="TextView"><Expressions/><location startLine="97" startOffset="8" endLine="108" endOffset="40"/></Target><Target id="@+id/layoutActionButtons" view="LinearLayout"><Expressions/><location startLine="111" startOffset="8" endLine="135" endOffset="22"/></Target><Target id="@+id/ivCompleteTask" view="ImageView"><Expressions/><location startLine="124" startOffset="12" endLine="133" endOffset="59"/></Target><Target id="@+id/ivTaskActions" view="ImageView"><Expressions/><location startLine="138" startOffset="8" endLine="148" endOffset="32"/></Target><Target id="@+id/tvTaskDescription" view="TextView"><Expressions/><location startLine="151" startOffset="8" endLine="158" endOffset="75"/></Target><Target id="@+id/rvSubtasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="160" startOffset="8" endLine="167" endOffset="73"/></Target><Target id="@+id/layoutDetails" view="LinearLayout"><Expressions/><location startLine="169" startOffset="8" endLine="205" endOffset="22"/></Target><Target id="@+id/tvDueDate" view="TextView"><Expressions/><location startLine="178" startOffset="12" endLine="181" endOffset="54"/></Target><Target id="@+id/chipCategory" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="183" startOffset="12" endLine="186" endOffset="54"/></Target><Target id="@+id/layoutAssignedUser" view="LinearLayout"><Expressions/><location startLine="188" startOffset="12" endLine="198" endOffset="26"/></Target><Target id="@+id/tvAssignedUser" view="TextView"><Expressions/><location startLine="193" startOffset="16" endLine="196" endOffset="58"/></Target><Target id="@+id/ivAttachment" view="ImageView"><Expressions/><location startLine="200" startOffset="12" endLine="203" endOffset="54"/></Target><Target id="@+id/chipGroupTags" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="207" startOffset="8" endLine="214" endOffset="69"/></Target><Target id="@+id/layoutEnergyFocus" view="LinearLayout"><Expressions/><location startLine="216" startOffset="8" endLine="247" endOffset="22"/></Target><Target id="@+id/tvEnergyIcon" view="TextView"><Expressions/><location startLine="225" startOffset="12" endLine="228" endOffset="54"/></Target><Target id="@+id/tvEnergyLevel" view="TextView"><Expressions/><location startLine="230" startOffset="12" endLine="233" endOffset="54"/></Target><Target id="@+id/layoutFocusTime" view="LinearLayout"><Expressions/><location startLine="235" startOffset="12" endLine="245" endOffset="26"/></Target><Target id="@+id/tvFocusTime" view="TextView"><Expressions/><location startLine="240" startOffset="16" endLine="243" endOffset="58"/></Target><Target id="@+id/layoutQuickActions" view="LinearLayout"><Expressions/><location startLine="249" startOffset="8" endLine="273" endOffset="22"/></Target><Target id="@+id/btnStartTask" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="258" startOffset="12" endLine="261" endOffset="54"/></Target><Target id="@+id/btnScheduleTask" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="263" startOffset="12" endLine="266" endOffset="54"/></Target><Target id="@+id/btnTaskSuggestions" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="268" startOffset="12" endLine="271" endOffset="54"/></Target><Target id="@+id/statusIndicatorDot" view="View"><Expressions/><location startLine="275" startOffset="8" endLine="281" endOffset="55"/></Target><Target id="@+id/priorityIndicator" view="View"><Expressions/><location startLine="283" startOffset="8" endLine="290" endOffset="55"/></Target><Target id="@+id/cbTaskComplete" view="CheckBox"><Expressions/><location startLine="292" startOffset="8" endLine="299" endOffset="66"/></Target><Target id="@+id/chipPriority" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="301" startOffset="8" endLine="307" endOffset="69"/></Target></Targets></Layout>