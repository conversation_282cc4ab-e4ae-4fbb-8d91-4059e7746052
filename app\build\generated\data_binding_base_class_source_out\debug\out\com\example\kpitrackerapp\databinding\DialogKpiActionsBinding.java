// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogKpiActionsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView actionChangeColor;

  @NonNull
  public final TextView actionDeleteKpi;

  @NonNull
  public final TextView actionDuplicateCopy;

  @NonNull
  public final TextView actionEditKpi;

  @NonNull
  public final TextView actionResetColor;

  @NonNull
  public final TextView actionShareCard;

  @NonNull
  public final TextView bottomSheetTitle;

  private DialogKpiActionsBinding(@NonNull LinearLayout rootView,
      @NonNull TextView actionChangeColor, @NonNull TextView actionDeleteKpi,
      @NonNull TextView actionDuplicateCopy, @NonNull TextView actionEditKpi,
      @NonNull TextView actionResetColor, @NonNull TextView actionShareCard,
      @NonNull TextView bottomSheetTitle) {
    this.rootView = rootView;
    this.actionChangeColor = actionChangeColor;
    this.actionDeleteKpi = actionDeleteKpi;
    this.actionDuplicateCopy = actionDuplicateCopy;
    this.actionEditKpi = actionEditKpi;
    this.actionResetColor = actionResetColor;
    this.actionShareCard = actionShareCard;
    this.bottomSheetTitle = bottomSheetTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogKpiActionsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogKpiActionsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_kpi_actions, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogKpiActionsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.action_change_color;
      TextView actionChangeColor = ViewBindings.findChildViewById(rootView, id);
      if (actionChangeColor == null) {
        break missingId;
      }

      id = R.id.action_delete_kpi;
      TextView actionDeleteKpi = ViewBindings.findChildViewById(rootView, id);
      if (actionDeleteKpi == null) {
        break missingId;
      }

      id = R.id.action_duplicate_copy;
      TextView actionDuplicateCopy = ViewBindings.findChildViewById(rootView, id);
      if (actionDuplicateCopy == null) {
        break missingId;
      }

      id = R.id.action_edit_kpi;
      TextView actionEditKpi = ViewBindings.findChildViewById(rootView, id);
      if (actionEditKpi == null) {
        break missingId;
      }

      id = R.id.action_reset_color;
      TextView actionResetColor = ViewBindings.findChildViewById(rootView, id);
      if (actionResetColor == null) {
        break missingId;
      }

      id = R.id.action_share_card;
      TextView actionShareCard = ViewBindings.findChildViewById(rootView, id);
      if (actionShareCard == null) {
        break missingId;
      }

      id = R.id.bottom_sheet_title;
      TextView bottomSheetTitle = ViewBindings.findChildViewById(rootView, id);
      if (bottomSheetTitle == null) {
        break missingId;
      }

      return new DialogKpiActionsBinding((LinearLayout) rootView, actionChangeColor,
          actionDeleteKpi, actionDuplicateCopy, actionEditKpi, actionResetColor, actionShareCard,
          bottomSheetTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
