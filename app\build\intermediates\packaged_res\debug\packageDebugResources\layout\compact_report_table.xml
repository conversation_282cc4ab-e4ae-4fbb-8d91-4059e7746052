<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Table Header Row -->
        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <TableLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:stretchColumns="*">

                <TableRow
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/purple_700"
                    android:padding="8dp">

                    <!-- Doctor Column -->
                    <TextView
                        android:layout_width="120dp"
                        android:layout_height="wrap_content"
                        android:text="Doctor"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <!-- Daily Columns -->
                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Daily Target"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Daily Achieved"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Daily %"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <!-- Monthly Columns -->
                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Monthly Target"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Monthly Achieved"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Monthly %"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <!-- Annual Columns -->
                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Annual Target"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Annual Achieved"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Annual %"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </TableRow>
            </TableLayout>
        </HorizontalScrollView>

        <!-- Table Content -->
        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/compactReportRecyclerView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
        </HorizontalScrollView>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
