<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_chat_message_received" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\item_chat_message_received.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_chat_message_received_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="137" endOffset="14"/></Target><Target id="@+id/userAvatar" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="44"/></Target><Target id="@+id/messageCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="27" startOffset="8" endLine="127" endOffset="59"/></Target><Target id="@+id/senderText" view="TextView"><Expressions/><location startLine="45" startOffset="16" endLine="52" endOffset="46"/></Target><Target id="@+id/replyContainer" view="LinearLayout"><Expressions/><location startLine="55" startOffset="16" endLine="82" endOffset="30"/></Target><Target id="@+id/replyToText" view="TextView"><Expressions/><location startLine="65" startOffset="20" endLine="71" endOffset="49"/></Target><Target id="@+id/replyMessageText" view="TextView"><Expressions/><location startLine="73" startOffset="20" endLine="80" endOffset="49"/></Target><Target id="@+id/messageText" view="TextView"><Expressions/><location startLine="85" startOffset="16" endLine="93" endOffset="53"/></Target><Target id="@+id/editedText" view="TextView"><Expressions/><location startLine="104" startOffset="20" endLine="112" endOffset="51"/></Target><Target id="@+id/timeText" view="TextView"><Expressions/><location startLine="115" startOffset="20" endLine="121" endOffset="49"/></Target></Targets></Layout>