// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TableLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CompactReportTableRowBinding implements ViewBinding {
  @NonNull
  private final TableLayout rootView;

  @NonNull
  public final TextView annualAchievedTextView;

  @NonNull
  public final TextView annualPercentageTextView;

  @NonNull
  public final TextView annualTargetTextView;

  @NonNull
  public final TextView dailyAchievedTextView;

  @NonNull
  public final TextView dailyPercentageTextView;

  @NonNull
  public final TextView dailyTargetTextView;

  @NonNull
  public final TextView doctorNameTextView;

  @NonNull
  public final TextView monthlyAchievedTextView;

  @NonNull
  public final TextView monthlyPercentageTextView;

  @NonNull
  public final TextView monthlyTargetTextView;

  private CompactReportTableRowBinding(@NonNull TableLayout rootView,
      @NonNull TextView annualAchievedTextView, @NonNull TextView annualPercentageTextView,
      @NonNull TextView annualTargetTextView, @NonNull TextView dailyAchievedTextView,
      @NonNull TextView dailyPercentageTextView, @NonNull TextView dailyTargetTextView,
      @NonNull TextView doctorNameTextView, @NonNull TextView monthlyAchievedTextView,
      @NonNull TextView monthlyPercentageTextView, @NonNull TextView monthlyTargetTextView) {
    this.rootView = rootView;
    this.annualAchievedTextView = annualAchievedTextView;
    this.annualPercentageTextView = annualPercentageTextView;
    this.annualTargetTextView = annualTargetTextView;
    this.dailyAchievedTextView = dailyAchievedTextView;
    this.dailyPercentageTextView = dailyPercentageTextView;
    this.dailyTargetTextView = dailyTargetTextView;
    this.doctorNameTextView = doctorNameTextView;
    this.monthlyAchievedTextView = monthlyAchievedTextView;
    this.monthlyPercentageTextView = monthlyPercentageTextView;
    this.monthlyTargetTextView = monthlyTargetTextView;
  }

  @Override
  @NonNull
  public TableLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static CompactReportTableRowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CompactReportTableRowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.compact_report_table_row, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CompactReportTableRowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.annualAchievedTextView;
      TextView annualAchievedTextView = ViewBindings.findChildViewById(rootView, id);
      if (annualAchievedTextView == null) {
        break missingId;
      }

      id = R.id.annualPercentageTextView;
      TextView annualPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (annualPercentageTextView == null) {
        break missingId;
      }

      id = R.id.annualTargetTextView;
      TextView annualTargetTextView = ViewBindings.findChildViewById(rootView, id);
      if (annualTargetTextView == null) {
        break missingId;
      }

      id = R.id.dailyAchievedTextView;
      TextView dailyAchievedTextView = ViewBindings.findChildViewById(rootView, id);
      if (dailyAchievedTextView == null) {
        break missingId;
      }

      id = R.id.dailyPercentageTextView;
      TextView dailyPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (dailyPercentageTextView == null) {
        break missingId;
      }

      id = R.id.dailyTargetTextView;
      TextView dailyTargetTextView = ViewBindings.findChildViewById(rootView, id);
      if (dailyTargetTextView == null) {
        break missingId;
      }

      id = R.id.doctorNameTextView;
      TextView doctorNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (doctorNameTextView == null) {
        break missingId;
      }

      id = R.id.monthlyAchievedTextView;
      TextView monthlyAchievedTextView = ViewBindings.findChildViewById(rootView, id);
      if (monthlyAchievedTextView == null) {
        break missingId;
      }

      id = R.id.monthlyPercentageTextView;
      TextView monthlyPercentageTextView = ViewBindings.findChildViewById(rootView, id);
      if (monthlyPercentageTextView == null) {
        break missingId;
      }

      id = R.id.monthlyTargetTextView;
      TextView monthlyTargetTextView = ViewBindings.findChildViewById(rootView, id);
      if (monthlyTargetTextView == null) {
        break missingId;
      }

      return new CompactReportTableRowBinding((TableLayout) rootView, annualAchievedTextView,
          annualPercentageTextView, annualTargetTextView, dailyAchievedTextView,
          dailyPercentageTextView, dailyTargetTextView, doctorNameTextView, monthlyAchievedTextView,
          monthlyPercentageTextView, monthlyTargetTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
