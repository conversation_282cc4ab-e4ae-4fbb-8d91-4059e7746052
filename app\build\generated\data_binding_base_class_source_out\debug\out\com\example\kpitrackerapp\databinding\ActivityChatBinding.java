// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityChatBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final MaterialButton attachButton;

  @NonNull
  public final TextInputEditText messageEditText;

  @NonNull
  public final RecyclerView messagesRecyclerView;

  @NonNull
  public final FloatingActionButton sendButton;

  @NonNull
  public final Toolbar toolbar;

  private ActivityChatBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull MaterialButton attachButton,
      @NonNull TextInputEditText messageEditText, @NonNull RecyclerView messagesRecyclerView,
      @NonNull FloatingActionButton sendButton, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.attachButton = attachButton;
    this.messageEditText = messageEditText;
    this.messagesRecyclerView = messagesRecyclerView;
    this.sendButton = sendButton;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityChatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityChatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_chat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityChatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.attachButton;
      MaterialButton attachButton = ViewBindings.findChildViewById(rootView, id);
      if (attachButton == null) {
        break missingId;
      }

      id = R.id.messageEditText;
      TextInputEditText messageEditText = ViewBindings.findChildViewById(rootView, id);
      if (messageEditText == null) {
        break missingId;
      }

      id = R.id.messagesRecyclerView;
      RecyclerView messagesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (messagesRecyclerView == null) {
        break missingId;
      }

      id = R.id.sendButton;
      FloatingActionButton sendButton = ViewBindings.findChildViewById(rootView, id);
      if (sendButton == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityChatBinding((CoordinatorLayout) rootView, appBarLayout, attachButton,
          messageEditText, messagesRecyclerView, sendButton, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
