{"logs": [{"outputFile": "com.example.kpitrackerapp-mergeDebugResources-55:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\44fac508c06d1fa71634b2a2e87bd97f\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5587", "endColumns": "139", "endOffsets": "5722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d69c07a7d8d82aa3a4b3977e5b803b90\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3576,3673,3775,3873,3972,4086,4191,11722", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3668,3770,3868,3967,4081,4186,4308,11818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\32f7978bc74fb055e711be763c16e3bd\\transformed\\jetified-play-services-base-18.1.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4597,4701,4869,4991,5101,5252,5377,5488,5727,5898,6007,6182,6310,6469,6630,6699,6765", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "4696,4864,4986,5096,5247,5372,5483,5582,5893,6002,6177,6305,6464,6625,6694,6760,6844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1fb6da15b525de990376081401abcb58\\transformed\\appcompat-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,11639", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,11717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2b029f212d90e38d6ae5a8afe6446dd6\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,325", "endColumns": "58,46,163,109", "endOffsets": "109,156,320,430"}, "to": {"startLines": "130,131,132,133", "startColumns": "4,4,4,4", "startOffsets": "11823,11882,11929,12093", "endColumns": "58,46,163,109", "endOffsets": "11877,11924,12088,12198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36b4f88c4f8ab3af336856b4860e45b2\\transformed\\material-1.11.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1162,1243,1307,1368,1479,1543,1611,1665,1734,1796,1850,1961,2022,2084,2138,2210,2339,2428,2510,2659,2741,2824,2961,3048,3125,3179,3230,3296,3367,3443,3532,3615,3692,3770,3848,3924,4032,4122,4195,4290,4387,4459,4533,4633,4685,4770,4836,4924,5014,5076,5140,5203,5274,5381,5493,5592,5699,5757,5812", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,136,86,76,53,50,65,70,75,88,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1157,1238,1302,1363,1474,1538,1606,1660,1729,1791,1845,1956,2017,2079,2133,2205,2334,2423,2505,2654,2736,2819,2956,3043,3120,3174,3225,3291,3362,3438,3527,3610,3687,3765,3843,3919,4027,4117,4190,4285,4382,4454,4528,4628,4680,4765,4831,4919,5009,5071,5135,5198,5269,5376,5488,5587,5694,5752,5807,5883"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3223,3298,3377,3481,4313,4398,4515,6849,6913,6994,7058,7119,7230,7294,7362,7416,7485,7547,7601,7712,7773,7835,7889,7961,8090,8179,8261,8410,8492,8575,8712,8799,8876,8930,8981,9047,9118,9194,9283,9366,9443,9521,9599,9675,9783,9873,9946,10041,10138,10210,10284,10384,10436,10521,10587,10675,10765,10827,10891,10954,11025,11132,11244,11343,11450,11508,11563", "endLines": "7,35,36,37,38,39,47,48,49,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,136,86,76,53,50,65,70,75,88,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75", "endOffsets": "431,3218,3293,3372,3476,3571,4393,4510,4592,6908,6989,7053,7114,7225,7289,7357,7411,7480,7542,7596,7707,7768,7830,7884,7956,8085,8174,8256,8405,8487,8570,8707,8794,8871,8925,8976,9042,9113,9189,9278,9361,9438,9516,9594,9670,9778,9868,9941,10036,10133,10205,10279,10379,10431,10516,10582,10670,10760,10822,10886,10949,11020,11127,11239,11338,11445,11503,11558,11634"}}]}]}