<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_edit_task" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\dialog_edit_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_edit_task_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="14"/></Target><Target id="@+id/tilEditTaskName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="59"/></Target><Target id="@+id/etEditTaskName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="15" startOffset="8" endLine="18" endOffset="50"/></Target><Target id="@+id/tilEditTaskExpirationDate" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="21" startOffset="4" endLine="36" endOffset="59"/></Target><Target id="@+id/etEditTaskExpirationDate" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="29" startOffset="8" endLine="35" endOffset="38"/></Target><Target id="@+id/tilEditTaskExpirationTime" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="38" startOffset="4" endLine="53" endOffset="59"/></Target><Target id="@+id/etEditTaskExpirationTime" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="46" startOffset="8" endLine="52" endOffset="38"/></Target><Target id="@+id/tilEditTaskReminderDays" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="55" startOffset="4" endLine="68" endOffset="59"/></Target><Target id="@+id/etEditTaskReminderDays" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="8" endLine="67" endOffset="40"/></Target><Target id="@+id/cbTaskCompleted" view="CheckBox"><Expressions/><location startLine="70" startOffset="4" endLine="75" endOffset="42"/></Target></Targets></Layout>