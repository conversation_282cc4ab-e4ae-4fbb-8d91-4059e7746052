// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class UserSummaryCardItemBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final View bottomBackgroundView;

  @NonNull
  public final ImageView calendarIcon;

  @NonNull
  public final ImageView dragHandleImageView;

  @NonNull
  public final LinearLayout expiryDateContainer;

  @NonNull
  public final TextView expiryDateTextView;

  @NonNull
  public final LinearLayout kpiDetailsContainer;

  @NonNull
  public final ShapeableImageView ownerImageView;

  @NonNull
  public final TextView ownerNameTextView;

  @NonNull
  public final View topBackgroundView;

  @NonNull
  public final LinearLayout topContentContainer;

  @NonNull
  public final MaterialCardView userSummaryCardView;

  private UserSummaryCardItemBinding(@NonNull MaterialCardView rootView,
      @NonNull View bottomBackgroundView, @NonNull ImageView calendarIcon,
      @NonNull ImageView dragHandleImageView, @NonNull LinearLayout expiryDateContainer,
      @NonNull TextView expiryDateTextView, @NonNull LinearLayout kpiDetailsContainer,
      @NonNull ShapeableImageView ownerImageView, @NonNull TextView ownerNameTextView,
      @NonNull View topBackgroundView, @NonNull LinearLayout topContentContainer,
      @NonNull MaterialCardView userSummaryCardView) {
    this.rootView = rootView;
    this.bottomBackgroundView = bottomBackgroundView;
    this.calendarIcon = calendarIcon;
    this.dragHandleImageView = dragHandleImageView;
    this.expiryDateContainer = expiryDateContainer;
    this.expiryDateTextView = expiryDateTextView;
    this.kpiDetailsContainer = kpiDetailsContainer;
    this.ownerImageView = ownerImageView;
    this.ownerNameTextView = ownerNameTextView;
    this.topBackgroundView = topBackgroundView;
    this.topContentContainer = topContentContainer;
    this.userSummaryCardView = userSummaryCardView;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static UserSummaryCardItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static UserSummaryCardItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.user_summary_card_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static UserSummaryCardItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomBackgroundView;
      View bottomBackgroundView = ViewBindings.findChildViewById(rootView, id);
      if (bottomBackgroundView == null) {
        break missingId;
      }

      id = R.id.calendarIcon;
      ImageView calendarIcon = ViewBindings.findChildViewById(rootView, id);
      if (calendarIcon == null) {
        break missingId;
      }

      id = R.id.dragHandleImageView;
      ImageView dragHandleImageView = ViewBindings.findChildViewById(rootView, id);
      if (dragHandleImageView == null) {
        break missingId;
      }

      id = R.id.expiryDateContainer;
      LinearLayout expiryDateContainer = ViewBindings.findChildViewById(rootView, id);
      if (expiryDateContainer == null) {
        break missingId;
      }

      id = R.id.expiryDateTextView;
      TextView expiryDateTextView = ViewBindings.findChildViewById(rootView, id);
      if (expiryDateTextView == null) {
        break missingId;
      }

      id = R.id.kpiDetailsContainer;
      LinearLayout kpiDetailsContainer = ViewBindings.findChildViewById(rootView, id);
      if (kpiDetailsContainer == null) {
        break missingId;
      }

      id = R.id.ownerImageView;
      ShapeableImageView ownerImageView = ViewBindings.findChildViewById(rootView, id);
      if (ownerImageView == null) {
        break missingId;
      }

      id = R.id.ownerNameTextView;
      TextView ownerNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (ownerNameTextView == null) {
        break missingId;
      }

      id = R.id.topBackgroundView;
      View topBackgroundView = ViewBindings.findChildViewById(rootView, id);
      if (topBackgroundView == null) {
        break missingId;
      }

      id = R.id.topContentContainer;
      LinearLayout topContentContainer = ViewBindings.findChildViewById(rootView, id);
      if (topContentContainer == null) {
        break missingId;
      }

      MaterialCardView userSummaryCardView = (MaterialCardView) rootView;

      return new UserSummaryCardItemBinding((MaterialCardView) rootView, bottomBackgroundView,
          calendarIcon, dragHandleImageView, expiryDateContainer, expiryDateTextView,
          kpiDetailsContainer, ownerImageView, ownerNameTextView, topBackgroundView,
          topContentContainer, userSummaryCardView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
