<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_create_user" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_create_user.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_create_user_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="209" endOffset="12"/></Target><Target id="@+id/headerTextView" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="27" endOffset="55"/></Target><Target id="@+id/profilePictureCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="30" startOffset="8" endLine="50" endOffset="59"/></Target><Target id="@+id/profilePictureImageView" view="ImageView"><Expressions/><location startLine="41" startOffset="12" endLine="48" endOffset="63"/></Target><Target id="@+id/addPhotoButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="53" startOffset="8" endLine="64" endOffset="75"/></Target><Target id="@+id/formLayout" view="LinearLayout"><Expressions/><location startLine="67" startOffset="8" endLine="156" endOffset="22"/></Target><Target id="@+id/fullNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="78" startOffset="12" endLine="94" endOffset="67"/></Target><Target id="@+id/fullNameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="87" startOffset="16" endLine="92" endOffset="42"/></Target><Target id="@+id/usernameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="97" startOffset="12" endLine="114" endOffset="67"/></Target><Target id="@+id/usernameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="107" startOffset="16" endLine="112" endOffset="42"/></Target><Target id="@+id/emailInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="117" startOffset="12" endLine="134" endOffset="67"/></Target><Target id="@+id/emailEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="127" startOffset="16" endLine="132" endOffset="42"/></Target><Target id="@+id/departmentInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="137" startOffset="12" endLine="154" endOffset="67"/></Target><Target id="@+id/departmentEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="147" startOffset="16" endLine="152" endOffset="42"/></Target><Target id="@+id/buttonLayout" view="LinearLayout"><Expressions/><location startLine="159" startOffset="8" endLine="194" endOffset="22"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="171" startOffset="12" endLine="180" endOffset="41"/></Target><Target id="@+id/createButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="183" startOffset="12" endLine="192" endOffset="41"/></Target><Target id="@+id/loadingProgressBar" view="ProgressBar"><Expressions/><location startLine="197" startOffset="8" endLine="205" endOffset="55"/></Target></Targets></Layout>